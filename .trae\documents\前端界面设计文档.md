# 题库管理系统前端界面设计文档

## 1. 整体设计原则

### 1.1 设计理念
- **简洁高效**：界面简洁明了，操作流程高效便捷
- **一致性**：保持与现有系统的视觉和交互一致性
- **可用性**：优先考虑用户体验，降低学习成本
- **响应式**：支持桌面端和移动端的良好体验

### 1.2 视觉风格
- **主色调**：#2563eb（蓝色）- 专业、可靠
- **辅助色**：#10b981（绿色）- 成功状态，#ef4444（红色）- 警告状态
- **背景色**：#f8fafc（浅灰）- 清爽、现代
- **文字色**：#1e293b（深灰）- 易读性强
- **边框色**：#e2e8f0（浅灰）- 层次分明

## 2. 重复题目检测与管理界面

### 2.1 主界面布局

```html
<!-- 重复检测主页面 -->
<div class="duplicate-detection-page">
  <!-- 页面头部 -->
  <div class="page-header">
    <h1 class="page-title">重复题目检测</h1>
    <div class="header-actions">
      <button class="btn btn-primary" @click="startDetection">
        <i class="icon-search"></i> 开始检测
      </button>
      <button class="btn btn-secondary" @click="viewHistory">
        <i class="icon-history"></i> 检测历史
      </button>
    </div>
  </div>

  <!-- 检测配置区域 -->
  <div class="detection-config-panel">
    <div class="config-section">
      <h3>检测范围</h3>
      <div class="form-group">
        <label>选择题库</label>
        <select v-model="selectedBanks" multiple class="form-control">
          <option v-for="bank in questionBanks" :key="bank.id" :value="bank.id">
            {{ bank.name }} ({{ bank.question_count }}题)
          </option>
        </select>
      </div>
      
      <div class="form-group">
        <label>
          <input type="checkbox" v-model="includeCrossBanks"> 
          包含不同等级题库间检测
        </label>
      </div>
    </div>

    <div class="config-section">
      <h3>检测参数</h3>
      <div class="form-group">
        <label>相似度阈值</label>
        <div class="slider-container">
          <input type="range" v-model="similarityThreshold" 
                 min="50" max="100" step="5" class="slider">
          <span class="slider-value">{{ similarityThreshold }}%</span>
        </div>
      </div>
      
      <div class="form-group">
        <label>检测算法</label>
        <div class="algorithm-options">
          <label class="radio-option">
            <input type="radio" v-model="algorithm" value="comprehensive">
            <span>综合算法（推荐）</span>
          </label>
          <label class="radio-option">
            <input type="radio" v-model="algorithm" value="fast">
            <span>快速检测</span>
          </label>
          <label class="radio-option">
            <input type="radio" v-model="algorithm" value="precise">
            <span>精确检测</span>
          </label>
        </div>
      </div>
    </div>
  </div>

  <!-- 检测结果区域 -->
  <div class="detection-results" v-if="detectionResults.length > 0">
    <div class="results-header">
      <h3>检测结果</h3>
      <div class="results-summary">
        <span class="summary-item">
          <i class="icon-duplicate"></i>
          发现 {{ duplicateGroups.length }} 组重复题目
        </span>
        <span class="summary-item">
          <i class="icon-question"></i>
          涉及 {{ totalDuplicateQuestions }} 道题目
        </span>
      </div>
      
      <div class="batch-actions">
        <button class="btn btn-danger" @click="batchDelete" 
                :disabled="selectedDuplicates.length === 0">
          <i class="icon-trash"></i> 批量删除 ({{ selectedDuplicates.length }})
        </button>
        <button class="btn btn-warning" @click="exportResults">
          <i class="icon-download"></i> 导出结果
        </button>
      </div>
    </div>

    <!-- 重复组列表 -->
    <div class="duplicate-groups">
      <div v-for="group in duplicateGroups" :key="group.id" 
           class="duplicate-group">
        <div class="group-header">
          <div class="group-info">
            <span class="similarity-badge" 
                  :class="getSimilarityClass(group.similarity)">
              {{ group.similarity }}% 相似
            </span>
            <span class="question-count">{{ group.questions.length }} 道题目</span>
          </div>
          
          <div class="group-actions">
            <button class="btn btn-sm btn-outline" @click="toggleGroup(group.id)">
              {{ group.expanded ? '收起' : '展开' }}
            </button>
            <button class="btn btn-sm btn-danger" @click="deleteGroup(group.id)">
              删除组
            </button>
          </div>
        </div>

        <!-- 题目详情 -->
        <div v-if="group.expanded" class="group-questions">
          <div v-for="question in group.questions" :key="question.id" 
               class="question-item">
            <div class="question-header">
              <input type="checkbox" v-model="selectedDuplicates" 
                     :value="question.id" class="question-checkbox">
              <span class="question-id">ID: {{ question.id }}</span>
              <span class="question-bank">{{ question.bank_name }}</span>
              <span class="question-difficulty">{{ question.difficulty }}</span>
            </div>
            
            <div class="question-content">
              <div class="question-stem">{{ question.stem }}</div>
              <div class="question-options">
                <div v-for="(option, index) in question.options" 
                     :key="index" class="option-item">
                  <span class="option-label">{{ String.fromCharCode(65 + index) }}.</span>
                  <span class="option-text">{{ option }}</span>
                </div>
              </div>
              <div class="correct-answer">
                <strong>正确答案：{{ question.correct_answer }}</strong>
              </div>
            </div>
            
            <div class="question-actions">
              <button class="btn btn-sm btn-primary" @click="editQuestion(question.id)">
                <i class="icon-edit"></i> 编辑
              </button>
              <button class="btn btn-sm btn-danger" @click="deleteQuestion(question.id)">
                <i class="icon-trash"></i> 删除
              </button>
              <button class="btn btn-sm btn-secondary" @click="viewDetails(question.id)">
                <i class="icon-eye"></i> 详情
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 检测进度模态框 -->
  <div v-if="isDetecting" class="modal-overlay">
    <div class="progress-modal">
      <h3>正在检测重复题目</h3>
      <div class="progress-bar">
        <div class="progress-fill" :style="{width: detectionProgress + '%'}"></div>
      </div>
      <p class="progress-text">{{ detectionStatus }}</p>
      <button class="btn btn-secondary" @click="cancelDetection">取消检测</button>
    </div>
  </div>
</div>
```

### 2.2 交互流程设计

#### 检测流程：
1. **配置检测参数** → 选择题库、设置相似度阈值、选择算法
2. **启动检测** → 显示进度条，实时更新检测状态
3. **查看结果** → 分组展示重复题目，支持展开/收起
4. **处理重复** → 批量删除、单个编辑、导出结果

#### 状态管理：
```javascript
// Vue组件状态管理
data() {
  return {
    // 检测配置
    selectedBanks: [],
    includeCrossBanks: true,
    similarityThreshold: 80,
    algorithm: 'comprehensive',
    
    // 检测状态
    isDetecting: false,
    detectionProgress: 0,
    detectionStatus: '',
    
    // 检测结果
    detectionResults: [],
    duplicateGroups: [],
    selectedDuplicates: [],
    
    // 界面状态
    expandedGroups: new Set()
  }
}
```

## 3. 备用题目管理界面

### 3.1 主界面布局

```html
<!-- 备用题目管理页面 -->
<div class="backup-management-page">
  <!-- 页面头部 -->
  <div class="page-header">
    <h1 class="page-title">备用题目管理</h1>
    <div class="header-actions">
      <button class="btn btn-primary" @click="configureRotation">
        <i class="icon-settings"></i> 轮换配置
      </button>
      <button class="btn btn-success" @click="executeRotation">
        <i class="icon-refresh"></i> 执行轮换
      </button>
    </div>
  </div>

  <!-- 题库选择器 -->
  <div class="bank-selector">
    <select v-model="selectedBank" @change="loadBankData" class="form-control">
      <option value="">请选择题库</option>
      <option v-for="bank in questionBanks" :key="bank.id" :value="bank.id">
        {{ bank.name }}
      </option>
    </select>
  </div>

  <!-- 题库概览 -->
  <div v-if="selectedBank" class="bank-overview">
    <div class="overview-cards">
      <div class="overview-card">
        <div class="card-icon">
          <i class="icon-target"></i>
        </div>
        <div class="card-content">
          <h3>{{ bankData.standard_count }}</h3>
          <p>标准题目数量</p>
        </div>
      </div>
      
      <div class="overview-card">
        <div class="card-icon">
          <i class="icon-check-circle"></i>
        </div>
        <div class="card-content">
          <h3>{{ bankData.active_count }}</h3>
          <p>当前活跃题目</p>
        </div>
      </div>
      
      <div class="overview-card">
        <div class="card-icon">
          <i class="icon-archive"></i>
        </div>
        <div class="card-content">
          <h3>{{ bankData.backup_count }}</h3>
          <p>备用题目</p>
        </div>
      </div>
      
      <div class="overview-card">
        <div class="card-icon">
          <i class="icon-percent"></i>
        </div>
        <div class="card-content">
          <h3>{{ bankData.rotation_rate }}%</h3>
          <p>年轮换比例</p>
        </div>
      </div>
    </div>

    <!-- 轮换规则配置 -->
    <div class="rotation-config">
      <h3>轮换规则</h3>
      <div class="config-form">
        <div class="form-row">
          <div class="form-group">
            <label>标准题目数量</label>
            <input type="number" v-model="rotationConfig.standard_count" 
                   class="form-control" min="1">
          </div>
          
          <div class="form-group">
            <label>年轮换比例 (%)</label>
            <input type="number" v-model="rotationConfig.rotation_rate" 
                   class="form-control" min="1" max="100">
          </div>
          
          <div class="form-group">
            <label>轮换策略</label>
            <select v-model="rotationConfig.strategy" class="form-control">
              <option value="random">随机轮换</option>
              <option value="difficulty_based">基于难度</option>
              <option value="time_based">基于时间</option>
              <option value="intelligent">智能轮换</option>
            </select>
          </div>
        </div>
        
        <div class="form-actions">
          <button class="btn btn-primary" @click="saveRotationConfig">
            <i class="icon-save"></i> 保存配置
          </button>
          <button class="btn btn-secondary" @click="previewRotation">
            <i class="icon-eye"></i> 预览轮换
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 题目状态管理 -->
  <div v-if="selectedBank" class="question-management">
    <div class="management-tabs">
      <button class="tab-button" 
              :class="{active: activeTab === 'active'}" 
              @click="activeTab = 'active'">
        活跃题目 ({{ bankData.active_count }})
      </button>
      <button class="tab-button" 
              :class="{active: activeTab === 'backup'}" 
              @click="activeTab = 'backup'">
        备用题目 ({{ bankData.backup_count }})
      </button>
      <button class="tab-button" 
              :class="{active: activeTab === 'history'}" 
              @click="activeTab = 'history'">
        轮换历史
      </button>
    </div>

    <!-- 活跃题目列表 -->
    <div v-if="activeTab === 'active'" class="question-list">
      <div class="list-header">
        <div class="search-bar">
          <input type="text" v-model="searchKeyword" 
                 placeholder="搜索题目..." class="search-input">
          <button class="btn btn-outline" @click="searchQuestions">
            <i class="icon-search"></i>
          </button>
        </div>
        
        <div class="list-actions">
          <button class="btn btn-warning" @click="moveToBackup" 
                  :disabled="selectedActiveQuestions.length === 0">
            <i class="icon-archive"></i> 
            移至备用 ({{ selectedActiveQuestions.length }})
          </button>
        </div>
      </div>
      
      <div class="question-table">
        <table class="table">
          <thead>
            <tr>
              <th>
                <input type="checkbox" @change="selectAllActive" 
                       :checked="allActiveSelected">
              </th>
              <th>题目ID</th>
              <th>题干</th>
              <th>难度</th>
              <th>使用次数</th>
              <th>最后使用</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="question in activeQuestions" :key="question.id">
              <td>
                <input type="checkbox" v-model="selectedActiveQuestions" 
                       :value="question.id">
              </td>
              <td>{{ question.id }}</td>
              <td class="question-stem-cell">
                <div class="stem-preview">{{ question.stem_preview }}</div>
              </td>
              <td>
                <span class="difficulty-badge" 
                      :class="getDifficultyClass(question.difficulty)">
                  {{ question.difficulty }}
                </span>
              </td>
              <td>{{ question.usage_count }}</td>
              <td>{{ formatDate(question.last_used) }}</td>
              <td>
                <div class="action-buttons">
                  <button class="btn btn-sm btn-outline" 
                          @click="viewQuestion(question.id)">
                    <i class="icon-eye"></i>
                  </button>
                  <button class="btn btn-sm btn-warning" 
                          @click="moveQuestionToBackup(question.id)">
                    <i class="icon-archive"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 备用题目列表 -->
    <div v-if="activeTab === 'backup'" class="question-list">
      <!-- 类似活跃题目列表的结构，但操作按钮不同 -->
      <div class="list-header">
        <div class="search-bar">
          <input type="text" v-model="searchKeyword" 
                 placeholder="搜索备用题目..." class="search-input">
        </div>
        
        <div class="list-actions">
          <button class="btn btn-success" @click="moveToActive" 
                  :disabled="selectedBackupQuestions.length === 0">
            <i class="icon-check-circle"></i> 
            激活题目 ({{ selectedBackupQuestions.length }})
          </button>
        </div>
      </div>
      
      <!-- 备用题目表格 -->
      <div class="question-table">
        <!-- 表格结构类似，但列有所不同 -->
      </div>
    </div>

    <!-- 轮换历史 -->
    <div v-if="activeTab === 'history'" class="rotation-history">
      <div class="history-list">
        <div v-for="record in rotationHistory" :key="record.id" 
             class="history-item">
          <div class="history-header">
            <span class="history-date">{{ formatDate(record.rotation_date) }}</span>
            <span class="history-strategy">{{ record.strategy_used }}</span>
            <span class="history-count">轮换 {{ record.rotation_count }} 道题目</span>
          </div>
          
          <div class="history-details" v-if="record.expanded">
            <div class="rotated-in">
              <h4>轮换进入：</h4>
              <div class="question-chips">
                <span v-for="qid in record.rotated_in_questions.split(',')" 
                      :key="qid" class="question-chip">
                  {{ qid }}
                </span>
              </div>
            </div>
            
            <div class="rotated-out">
              <h4>轮换退出：</h4>
              <div class="question-chips">
                <span v-for="qid in record.rotated_out_questions.split(',')" 
                      :key="qid" class="question-chip">
                  {{ qid }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="history-actions">
            <button class="btn btn-sm btn-outline" 
                    @click="toggleHistoryDetails(record.id)">
              {{ record.expanded ? '收起' : '详情' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 轮换预览模态框 -->
  <div v-if="showRotationPreview" class="modal-overlay">
    <div class="rotation-preview-modal">
      <div class="modal-header">
        <h3>轮换预览</h3>
        <button class="close-btn" @click="showRotationPreview = false">
          <i class="icon-x"></i>
        </button>
      </div>
      
      <div class="modal-content">
        <div class="preview-summary">
          <p>本次轮换将影响 <strong>{{ rotationPreview.total_count }}</strong> 道题目</p>
        </div>
        
        <div class="preview-sections">
          <div class="preview-section">
            <h4>将激活的备用题目 ({{ rotationPreview.activate_count }})</h4>
            <div class="question-list-preview">
              <div v-for="question in rotationPreview.to_activate" 
                   :key="question.id" class="question-preview-item">
                <span class="question-id">{{ question.id }}</span>
                <span class="question-stem">{{ question.stem_preview }}</span>
                <span class="question-difficulty">{{ question.difficulty }}</span>
              </div>
            </div>
          </div>
          
          <div class="preview-section">
            <h4>将备用的活跃题目 ({{ rotationPreview.backup_count }})</h4>
            <div class="question-list-preview">
              <div v-for="question in rotationPreview.to_backup" 
                   :key="question.id" class="question-preview-item">
                <span class="question-id">{{ question.id }}</span>
                <span class="question-stem">{{ question.stem_preview }}</span>
                <span class="question-difficulty">{{ question.difficulty }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-actions">
        <button class="btn btn-secondary" @click="showRotationPreview = false">
          取消
        </button>
        <button class="btn btn-success" @click="confirmRotation">
          <i class="icon-check"></i> 确认轮换
        </button>
      </div>
    </div>
  </div>
</div>
```

## 4. 批量编辑界面

### 4.1 主界面布局

```html
<!-- 批量编辑页面 -->
<div class="batch-edit-page">
  <!-- 页面头部 -->
  <div class="page-header">
    <h1 class="page-title">题目批量编辑</h1>
    <div class="header-actions">
      <button class="btn btn-primary" @click="applyChanges" 
              :disabled="!hasChanges">
        <i class="icon-check"></i> 应用修改
      </button>
      <button class="btn btn-secondary" @click="previewChanges" 
              :disabled="selectedQuestions.length === 0">
        <i class="icon-eye"></i> 预览修改
      </button>
      <button class="btn btn-outline" @click="resetChanges">
        <i class="icon-refresh"></i> 重置
      </button>
    </div>
  </div>

  <!-- 题目选择区域 -->
  <div class="question-selection">
    <div class="selection-header">
      <h3>选择要编辑的题目</h3>
      <div class="selection-stats">
        <span>已选择 {{ selectedQuestions.length }} 道题目</span>
      </div>
    </div>
    
    <div class="selection-filters">
      <div class="filter-group">
        <label>题库</label>
        <select v-model="filterBank" @change="loadQuestions" class="form-control">
          <option value="">全部题库</option>
          <option v-for="bank in questionBanks" :key="bank.id" :value="bank.id">
            {{ bank.name }}
          </option>
        </select>
      </div>
      
      <div class="filter-group">
        <label>难度</label>
        <select v-model="filterDifficulty" @change="loadQuestions" class="form-control">
          <option value="">全部难度</option>
          <option value="easy">简单</option>
          <option value="medium">中等</option>
          <option value="hard">困难</option>
        </select>
      </div>
      
      <div class="filter-group">
        <label>题目类型</label>
        <select v-model="filterType" @change="loadQuestions" class="form-control">
          <option value="">全部类型</option>
          <option value="single">单选题</option>
          <option value="multiple">多选题</option>
          <option value="judge">判断题</option>
        </select>
      </div>
      
      <div class="filter-group">
        <input type="text" v-model="searchKeyword" 
               placeholder="搜索题目内容..." class="search-input">
        <button class="btn btn-outline" @click="loadQuestions">
          <i class="icon-search"></i>
        </button>
      </div>
    </div>
    
    <div class="question-list">
      <div class="list-header">
        <input type="checkbox" @change="selectAllQuestions" 
               :checked="allQuestionsSelected">
        <span>全选</span>
        
        <div class="bulk-actions">
          <button class="btn btn-sm btn-outline" @click="selectByDifficulty">
            按难度选择
          </button>
          <button class="btn btn-sm btn-outline" @click="selectByType">
            按类型选择
          </button>
        </div>
      </div>
      
      <div class="question-items">
        <div v-for="question in questions" :key="question.id" 
             class="question-item" 
             :class="{selected: selectedQuestions.includes(question.id)}">
          <div class="question-checkbox">
            <input type="checkbox" v-model="selectedQuestions" 
                   :value="question.id">
          </div>
          
          <div class="question-info">
            <div class="question-meta">
              <span class="question-id">ID: {{ question.id }}</span>
              <span class="question-bank">{{ question.bank_name }}</span>
              <span class="difficulty-badge" 
                    :class="getDifficultyClass(question.difficulty)">
                {{ question.difficulty }}
              </span>
              <span class="type-badge">{{ question.type }}</span>
            </div>
            
            <div class="question-content">
              <div class="question-stem">{{ question.stem }}</div>
              <div class="question-options">
                <div v-for="(option, index) in question.options" 
                     :key="index" class="option-item">
                  <span class="option-label">{{ String.fromCharCode(65 + index) }}.</span>
                  <span class="option-text">{{ option }}</span>
                </div>
              </div>
              <div class="correct-answer">
                <strong>正确答案：{{ question.correct_answer }}</strong>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="pagination">
        <button class="btn btn-outline" @click="prevPage" 
                :disabled="currentPage === 1">
          上一页
        </button>
        <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
        <button class="btn btn-outline" @click="nextPage" 
                :disabled="currentPage === totalPages">
          下一页
        </button>
      </div>
    </div>
  </div>

  <!-- 编辑操作区域 -->
  <div class="edit-operations">
    <div class="operation-tabs">
      <button class="tab-button" 
              :class="{active: activeOperation === 'reorder'}" 
              @click="activeOperation = 'reorder'">
        <i class="icon-shuffle"></i> 选项重排
      </button>
      <button class="tab-button" 
              :class="{active: activeOperation === 'replace'}" 
              @click="activeOperation = 'replace'">
        <i class="icon-edit"></i> 内容替换
      </button>
      <button class="tab-button" 
              :class="{active: activeOperation === 'format'}" 
              @click="activeOperation = 'format'">
        <i class="icon-type"></i> 格式调整
      </button>
    </div>

    <!-- 选项重排操作 -->
    <div v-if="activeOperation === 'reorder'" class="operation-panel">
      <h3>选项重排</h3>
      <p class="operation-description">
        调整选项的顺序，系统将自动更新对应的正确答案字母。
      </p>
      
      <div class="reorder-config">
        <h4>重排规则</h4>
        <div class="mapping-rules">
          <div v-for="(mapping, index) in optionMappings" 
               :key="index" class="mapping-rule">
            <select v-model="mapping.from" class="form-control">
              <option value="A">选项 A</option>
              <option value="B">选项 B</option>
              <option value="C">选项 C</option>
              <option value="D">选项 D</option>
              <option value="E">选项 E</option>
            </select>
            
            <span class="mapping-arrow">→</span>
            
            <select v-model="mapping.to" class="form-control">
              <option value="A">选项 A</option>
              <option value="B">选项 B</option>
              <option value="C">选项 C</option>
              <option value="D">选项 D</option>
              <option value="E">选项 E</option>
            </select>
            
            <button class="btn btn-sm btn-danger" 
                    @click="removeMappingRule(index)">
              <i class="icon-trash"></i>
            </button>
          </div>
        </div>
        
        <button class="btn btn-outline" @click="addMappingRule">
          <i class="icon-plus"></i> 添加规则
        </button>
        
        <div class="preset-mappings">
          <h4>预设方案</h4>
          <div class="preset-buttons">
            <button class="btn btn-sm btn-outline" 
                    @click="applyPresetMapping('shuffle')">
              随机打乱
            </button>
            <button class="btn btn-sm btn-outline" 
                    @click="applyPresetMapping('reverse')">
              倒序排列
            </button>
            <button class="btn btn-sm btn-outline" 
                    @click="applyPresetMapping('rotate')">
              循环移位
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容替换操作 -->
    <div v-if="activeOperation === 'replace'" class="operation-panel">
      <h3>内容替换</h3>
      <p class="operation-description">
        批量替换题目中的特定文本内容。
      </p>
      
      <div class="replace-config">
        <div class="form-group">
          <label>查找内容</label>
          <input type="text" v-model="replaceConfig.searchText" 
                 placeholder="输入要查找的文本" class="form-control">
        </div>
        
        <div class="form-group">
          <label>替换为</label>
          <input type="text" v-model="replaceConfig.replaceText" 
                 placeholder="输入替换后的文本" class="form-control">
        </div>
        
        <div class="form-group">
          <label>替换范围</label>
          <div class="checkbox-group">
            <label class="checkbox-option">
              <input type="checkbox" v-model="replaceConfig.includeStems">
              <span>题干</span>
            </label>
            <label class="checkbox-option">
              <input type="checkbox" v-model="replaceConfig.includeOptions">
              <span>选项</span>
            </label>
            <label class="checkbox-option">
              <input type="checkbox" v-model="replaceConfig.includeExplanations">
              <span>解析</span>
            </label>
          </div>
        </div>
        
        <div class="form-group">
          <label>
            <input type="checkbox" v-model="replaceConfig.caseSensitive">
            区分大小写
          </label>
        </div>
        
        <div class="form-group">
          <label>
            <input type="checkbox" v-model="replaceConfig.wholeWord">
            全词匹配
          </label>
        </div>
      </div>
    </div>

    <!-- 格式调整操作 -->
    <div v-if="activeOperation === 'format'" class="operation-panel">
      <h3>格式调整</h3>
      <p class="operation-description">
        统一调整题目的格式和样式。
      </p>
      
      <div class="format-config">
        <div class="format-section">
          <h4>选项格式</h4>
          <div class="format-options">
            <label class="radio-option">
              <input type="radio" v-model="formatConfig.optionStyle" value="letter">
              <span>A. B. C. D.</span>
            </label>
            <label class="radio-option">
              <input type="radio" v-model="formatConfig.optionStyle" value="parenthesis">
              <span>(A) (B) (C) (D)</span>
            </label>
            <label class="radio-option">
              <input type="radio" v-model="formatConfig.optionStyle" value="bracket">
              <span>[A] [B] [C] [D]</span>
            </label>
          </div>
        </div>
        
        <div class="format-section">
          <h4>文本格式</h4>
          <div class="format-options">
            <label class="checkbox-option">
              <input type="checkbox" v-model="formatConfig.removeExtraSpaces">
              <span>移除多余空格</span>
            </label>
            <label class="checkbox-option">
              <input type="checkbox" v-model="formatConfig.standardizePunctuation">
              <span>标准化标点符号</span>
            </label>
            <label class="checkbox-option">
              <input type="checkbox" v-model="formatConfig.unifyLineBreaks">
              <span>统一换行符</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 预览修改模态框 -->
  <div v-if="showPreview" class="modal-overlay">
    <div class="preview-modal">
      <div class="modal-header">
        <h3>修改预览</h3>
        <button class="close-btn" @click="showPreview = false">
          <i class="icon-x"></i>
        </button>
      </div>
      
      <div class="modal-content">
        <div class="preview-summary">
          <p>将对 <strong>{{ previewData.affected_count }}</strong> 道题目进行修改</p>
        </div>
        
        <div class="preview-list">
          <div v-for="item in previewData.changes" :key="item.question_id" 
               class="preview-item">
            <div class="preview-header">
              <span class="question-id">题目 {{ item.question_id }}</span>
              <button class="btn btn-sm btn-outline" 
                      @click="togglePreviewDetails(item.question_id)">
                {{ item.expanded ? '收起' : '展开' }}
              </button>
            </div>
            
            <div v-if="item.expanded" class="preview-details">
              <div class="change-comparison">
                <div class="before-after">
                  <div class="before">
                    <h4>修改前</h4>
                    <div class="question-content">
                      <div class="question-stem">{{ item.original.stem }}</div>
                      <div class="question-options">
                        <div v-for="(option, index) in item.original.options" 
                             :key="index" class="option-item">
                          <span class="option-label">{{ String.fromCharCode(65 + index) }}.</span>
                          <span class="option-text">{{ option }}</span>
                        </div>
                      </div>
                      <div class="correct-answer">
                        正确答案：{{ item.original.correct_answer }}
                      </div>
                    </div>
                  </div>
                  
                  <div class="after">
                    <h4>修改后</h4>
                    <div class="question-content">
                      <div class="question-stem">{{ item.modified.stem }}</div>
                      <div class="question-options">
                        <div v-for="(option, index) in item.modified.options" 
                             :key="index" class="option-item">
                          <span class="option-label">{{ String.fromCharCode(65 + index) }}.</span>
                          <span class="option-text">{{ option }}</span>
                        </div>
                      </div>
                      <div class="correct-answer">
                        正确答案：{{ item.modified.correct_answer }}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="change-highlights">
                  <h4>变更详情</h4>
                  <div v-for="change in item.changes" :key="change.field" 
                       class="change-item">
                    <span class="change-field">{{ change.field }}</span>
                    <span class="change-arrow">→</span>
                    <span class="change-description">{{ change.description }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-actions">
        <button class="btn btn-secondary" @click="showPreview = false">
          取消
        </button>
        <button class="btn btn-primary" @click="confirmChanges">
          <i class="icon-check"></i> 确认修改
        </button>
      </div>
    </div>
  </div>
</div>
```

## 5. 响应式设计

### 5.1 断点设置

```css
/* 响应式断点 */
@media (max-width: 768px) {
  /* 移动端样式 */
  .page-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .question-table {
    overflow-x: auto;
  }
  
  .modal-content {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  /* 小屏幕移动端 */
  .overview-cards {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .operation-tabs {
    flex-direction: column;
  }
}
```

### 5.2 触摸优化

```css
/* 触摸友好的按钮尺寸 */
@media (max-width: 768px) {
  .btn {
    min-height: 44px;
    padding: 0.75rem 1rem;
  }
  
  .tab-button {
    min-height: 48px;
  }
  
  .question-item {
    padding: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .checkbox, .radio {
    transform: scale(1.2);
  }
}
```

## 6. 交互动效

### 6.1 过渡动画

```css
/* 页面切换动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 模态框动画 */
.modal-enter-active {
  transition: all 0.3s ease;
}

.modal-leave-active {
  transition: all 0.2s ease;
}

.modal-enter-from, .modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* 列表项动画 */
.list-enter-active, .list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.list-leave-to {
  opacity: 0;
  transform: translateX(10px);
}
```

### 6.2 加载状态

```css
/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 骨架屏 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
```

这套前端界面设计文档提供了完整的用户界面解决方案，确保三个新功能都有直观、易用的操作界面，同时保持与现有系统的一致性和良好的用户体验。