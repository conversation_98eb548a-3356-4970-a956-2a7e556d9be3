# -*- coding: utf-8 -*-
"""
备用题目管理和轮换算法模块

本模块实现备用题目的管理和轮换功能，包括：
1. 题目状态管理（活跃/备用/归档）
2. 轮换规则配置和执行
3. 多种轮换策略（随机、基于难度、基于时间、智能轮换）
4. 轮换历史记录和统计

作者：SOLO Coding
创建时间：2024
"""

import random
import datetime
import uuid
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import json
import math
from collections import defaultdict, Counter

class QuestionStatus(Enum):
    """题目状态枚举"""
    ACTIVE = "active"      # 活跃状态
    BACKUP = "backup"      # 备用状态
    ARCHIVED = "archived"  # 归档状态

class RotationStrategy(Enum):
    """轮换策略枚举"""
    RANDOM = "random"                    # 随机轮换
    DIFFICULTY_BASED = "difficulty_based"  # 基于难度的轮换
    TIME_BASED = "time_based"            # 基于时间的轮换
    INTELLIGENT = "intelligent"          # 智能轮换

class RotationFrequency(Enum):
    """轮换频率枚举"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    YEARLY = "yearly"
    CUSTOM = "custom"

@dataclass
class RotationConfig:
    """轮换配置数据类"""
    strategy: RotationStrategy
    frequency: RotationFrequency
    rotation_percentage: float  # 轮换比例 (0-100)
    difficulty_weights: Optional[Dict[str, float]] = None  # 难度权重
    time_weights: Optional[Dict[str, float]] = None       # 时间权重
    custom_params: Optional[Dict[str, Any]] = None        # 自定义参数

class BackupQuestionManager:
    """
    备用题目管理器
    
    负责管理题目的状态转换、轮换规则配置和执行。
    """
    
    def __init__(self):
        """
        初始化备用题目管理器
        """
        self.rotation_strategies = {
            RotationStrategy.RANDOM: self._random_rotation,
            RotationStrategy.DIFFICULTY_BASED: self._difficulty_based_rotation,
            RotationStrategy.TIME_BASED: self._time_based_rotation,
            RotationStrategy.INTELLIGENT: self._intelligent_rotation
        }
    
    def create_backup_rule(self, question_bank_id: str, rule_name: str,
                          standard_count: int, total_count: int,
                          rotation_config: RotationConfig) -> Dict[str, Any]:
        """
        创建备用题目轮换规则
        
        Args:
            question_bank_id: 题库ID
            rule_name: 规则名称
            standard_count: 标准题目数量
            total_count: 总题目数量
            rotation_config: 轮换配置
            
        Returns:
            Dict[str, Any]: 创建的规则数据
        """
        if standard_count >= total_count:
            raise ValueError("标准题目数量不能大于等于总题目数量")
        
        if rotation_config.rotation_percentage <= 0 or rotation_config.rotation_percentage > 100:
            raise ValueError("轮换比例必须在0-100之间")
        
        backup_count = total_count - standard_count
        rotation_count = math.ceil(standard_count * rotation_config.rotation_percentage / 100)
        
        if rotation_count > backup_count:
            raise ValueError(f"轮换数量({rotation_count})不能超过备用题目数量({backup_count})")
        
        rule_data = {
            'id': str(uuid.uuid4()),
            'question_bank_id': question_bank_id,
            'rule_name': rule_name,
            'standard_count': standard_count,
            'total_count': total_count,
            'rotation_percentage': rotation_config.rotation_percentage,
            'rotation_strategy': rotation_config.strategy.value,
            'rotation_frequency': rotation_config.frequency.value,
            'is_active': True,
            'strategy_config': {
                'difficulty_weights': rotation_config.difficulty_weights or {},
                'time_weights': rotation_config.time_weights or {},
                'custom_params': rotation_config.custom_params or {}
            },
            'created_at': datetime.datetime.utcnow(),
            'next_rotation_date': self._calculate_next_rotation_date(rotation_config.frequency)
        }
        
        return rule_data
    
    def _calculate_next_rotation_date(self, frequency: RotationFrequency) -> datetime.datetime:
        """
        计算下次轮换日期
        
        Args:
            frequency: 轮换频率
            
        Returns:
            datetime.datetime: 下次轮换日期
        """
        now = datetime.datetime.utcnow()
        
        if frequency == RotationFrequency.DAILY:
            return now + datetime.timedelta(days=1)
        elif frequency == RotationFrequency.WEEKLY:
            return now + datetime.timedelta(weeks=1)
        elif frequency == RotationFrequency.MONTHLY:
            # 下个月的同一天
            if now.month == 12:
                return now.replace(year=now.year + 1, month=1)
            else:
                return now.replace(month=now.month + 1)
        elif frequency == RotationFrequency.YEARLY:
            return now.replace(year=now.year + 1)
        else:
            # 自定义频率，默认一周后
            return now + datetime.timedelta(weeks=1)
    
    def execute_rotation(self, rule_data: Dict[str, Any], 
                        active_questions: List[Dict], 
                        backup_questions: List[Dict]) -> Dict[str, Any]:
        """
        执行题目轮换
        
        Args:
            rule_data: 轮换规则数据
            active_questions: 当前活跃题目列表
            backup_questions: 备用题目列表
            
        Returns:
            Dict[str, Any]: 轮换执行结果
                {
                    'success': bool,
                    'rotation_count': int,
                    'questions_rotated_in': List[str],  # 轮换进入的题目ID
                    'questions_rotated_out': List[str], # 轮换出去的题目ID
                    'execution_details': Dict,
                    'error_message': str
                }
        """
        try:
            strategy = RotationStrategy(rule_data['rotation_strategy'])
            rotation_percentage = rule_data['rotation_percentage']
            strategy_config = rule_data.get('strategy_config', {})
            
            # 计算轮换数量
            rotation_count = math.ceil(len(active_questions) * rotation_percentage / 100)
            
            if rotation_count > len(backup_questions):
                return {
                    'success': False,
                    'error_message': f'备用题目数量({len(backup_questions)})不足以支持轮换({rotation_count})'
                }
            
            # 执行轮换策略
            rotation_func = self.rotation_strategies[strategy]
            questions_out, questions_in, details = rotation_func(
                active_questions, backup_questions, rotation_count, strategy_config
            )
            
            return {
                'success': True,
                'rotation_count': rotation_count,
                'questions_rotated_in': [q['id'] for q in questions_in],
                'questions_rotated_out': [q['id'] for q in questions_out],
                'execution_details': details,
                'error_message': None
            }
            
        except Exception as e:
            return {
                'success': False,
                'rotation_count': 0,
                'questions_rotated_in': [],
                'questions_rotated_out': [],
                'execution_details': {},
                'error_message': str(e)
            }
    
    def _random_rotation(self, active_questions: List[Dict], 
                        backup_questions: List[Dict], 
                        rotation_count: int, 
                        config: Dict) -> Tuple[List[Dict], List[Dict], Dict]:
        """
        随机轮换策略
        
        Args:
            active_questions: 活跃题目列表
            backup_questions: 备用题目列表
            rotation_count: 轮换数量
            config: 策略配置
            
        Returns:
            Tuple[List[Dict], List[Dict], Dict]: (轮出题目, 轮入题目, 执行详情)
        """
        # 随机选择要轮出的活跃题目
        questions_out = random.sample(active_questions, rotation_count)
        
        # 随机选择要轮入的备用题目
        questions_in = random.sample(backup_questions, rotation_count)
        
        details = {
            'strategy': 'random',
            'selection_method': 'random_sampling',
            'rotation_timestamp': datetime.datetime.utcnow().isoformat()
        }
        
        return questions_out, questions_in, details
    
    def _difficulty_based_rotation(self, active_questions: List[Dict], 
                                  backup_questions: List[Dict], 
                                  rotation_count: int, 
                                  config: Dict) -> Tuple[List[Dict], List[Dict], Dict]:
        """
        基于难度的轮换策略
        
        优先轮换难度分布不均的题目，保持题库难度平衡。
        
        Args:
            active_questions: 活跃题目列表
            backup_questions: 备用题目列表
            rotation_count: 轮换数量
            config: 策略配置
            
        Returns:
            Tuple[List[Dict], List[Dict], Dict]: (轮出题目, 轮入题目, 执行详情)
        """
        difficulty_weights = config.get('difficulty_weights', {
            '1': 0.1,  # 很简单
            '2': 0.2,  # 简单
            '3': 0.4,  # 中等
            '4': 0.2,  # 困难
            '5': 0.1   # 很难
        })
        
        # 分析当前活跃题目的难度分布
        active_difficulty_dist = self._analyze_difficulty_distribution(active_questions)
        backup_difficulty_dist = self._analyze_difficulty_distribution(backup_questions)
        
        # 计算需要调整的难度类别
        questions_out = []
        questions_in = []
        
        # 按难度分组
        active_by_difficulty = self._group_by_difficulty(active_questions)
        backup_by_difficulty = self._group_by_difficulty(backup_questions)
        
        remaining_rotation = rotation_count
        
        for difficulty in ['1', '2', '3', '4', '5']:
            if remaining_rotation <= 0:
                break
                
            target_ratio = difficulty_weights.get(difficulty, 0)
            current_ratio = active_difficulty_dist.get(difficulty, 0)
            
            # 如果当前比例高于目标比例，优先轮出
            if current_ratio > target_ratio and difficulty in active_by_difficulty:
                available_out = active_by_difficulty[difficulty]
                out_count = min(len(available_out), remaining_rotation, 
                              int((current_ratio - target_ratio) * len(active_questions)))
                
                if out_count > 0 and difficulty in backup_by_difficulty:
                    available_in = backup_by_difficulty[difficulty]
                    in_count = min(len(available_in), out_count)
                    
                    if in_count > 0:
                        questions_out.extend(random.sample(available_out, in_count))
                        questions_in.extend(random.sample(available_in, in_count))
                        remaining_rotation -= in_count
        
        # 如果还有剩余轮换数量，随机补充
        if remaining_rotation > 0:
            remaining_active = [q for q in active_questions if q not in questions_out]
            remaining_backup = [q for q in backup_questions if q not in questions_in]
            
            if len(remaining_active) >= remaining_rotation and len(remaining_backup) >= remaining_rotation:
                questions_out.extend(random.sample(remaining_active, remaining_rotation))
                questions_in.extend(random.sample(remaining_backup, remaining_rotation))
        
        details = {
            'strategy': 'difficulty_based',
            'active_difficulty_distribution': active_difficulty_dist,
            'backup_difficulty_distribution': backup_difficulty_dist,
            'target_weights': difficulty_weights,
            'rotation_timestamp': datetime.datetime.utcnow().isoformat()
        }
        
        return questions_out, questions_in, details
    
    def _time_based_rotation(self, active_questions: List[Dict], 
                            backup_questions: List[Dict], 
                            rotation_count: int, 
                            config: Dict) -> Tuple[List[Dict], List[Dict], Dict]:
        """
        基于时间的轮换策略
        
        优先轮换使用时间较长或使用频率较高的题目。
        
        Args:
            active_questions: 活跃题目列表
            backup_questions: 备用题目列表
            rotation_count: 轮换数量
            config: 策略配置
            
        Returns:
            Tuple[List[Dict], List[Dict], Dict]: (轮出题目, 轮入题目, 执行详情)
        """
        # 按使用时间和频率排序
        def time_score(question):
            last_used = question.get('last_used_date')
            usage_count = question.get('usage_count', 0)
            
            # 计算时间分数（使用时间越久分数越高）
            if last_used:
                if isinstance(last_used, str):
                    last_used = datetime.datetime.fromisoformat(last_used.replace('Z', '+00:00'))
                days_since_used = (datetime.datetime.utcnow() - last_used).days
                time_score = min(days_since_used / 365, 1.0)  # 最多1年
            else:
                time_score = 0.5  # 从未使用过的题目给中等分数
            
            # 计算使用频率分数（使用次数越多分数越高）
            usage_score = min(usage_count / 100, 1.0)  # 最多100次
            
            # 综合分数
            return time_score * 0.6 + usage_score * 0.4
        
        # 按时间分数排序，选择分数最高的题目轮出
        active_sorted = sorted(active_questions, key=time_score, reverse=True)
        questions_out = active_sorted[:rotation_count]
        
        # 选择备用题目中时间分数最低的题目轮入
        backup_sorted = sorted(backup_questions, key=time_score)
        questions_in = backup_sorted[:rotation_count]
        
        details = {
            'strategy': 'time_based',
            'selection_criteria': 'usage_time_and_frequency',
            'avg_out_score': sum(time_score(q) for q in questions_out) / len(questions_out) if questions_out else 0,
            'avg_in_score': sum(time_score(q) for q in questions_in) / len(questions_in) if questions_in else 0,
            'rotation_timestamp': datetime.datetime.utcnow().isoformat()
        }
        
        return questions_out, questions_in, details
    
    def _intelligent_rotation(self, active_questions: List[Dict], 
                             backup_questions: List[Dict], 
                             rotation_count: int, 
                             config: Dict) -> Tuple[List[Dict], List[Dict], Dict]:
        """
        智能轮换策略
        
        综合考虑难度分布、使用时间、题目质量等多个因素。
        
        Args:
            active_questions: 活跃题目列表
            backup_questions: 备用题目列表
            rotation_count: 轮换数量
            config: 策略配置
            
        Returns:
            Tuple[List[Dict], List[Dict], Dict]: (轮出题目, 轮入题目, 执行详情)
        """
        # 智能评分函数
        def intelligent_score(question, is_active=True):
            score = 0.0
            
            # 难度分数（保持难度平衡）
            difficulty = question.get('difficulty_code', '3')
            difficulty_weights = {'1': 0.1, '2': 0.2, '3': 0.4, '4': 0.2, '5': 0.1}
            current_dist = self._analyze_difficulty_distribution(active_questions if is_active else backup_questions)
            target_ratio = difficulty_weights.get(difficulty, 0.2)
            current_ratio = current_dist.get(difficulty, 0)
            
            if is_active:
                # 活跃题目：当前比例高于目标时，轮出分数更高
                difficulty_score = max(0, current_ratio - target_ratio)
            else:
                # 备用题目：当前比例低于目标时，轮入分数更高
                difficulty_score = max(0, target_ratio - current_ratio)
            
            score += difficulty_score * 0.3
            
            # 时间分数
            last_used = question.get('last_used_date')
            usage_count = question.get('usage_count', 0)
            
            if last_used:
                if isinstance(last_used, str):
                    last_used = datetime.datetime.fromisoformat(last_used.replace('Z', '+00:00'))
                days_since_used = (datetime.datetime.utcnow() - last_used).days
                time_score = min(days_since_used / 365, 1.0)
            else:
                time_score = 0.5
            
            usage_score = min(usage_count / 100, 1.0)
            
            if is_active:
                # 活跃题目：使用时间长、频率高的优先轮出
                score += (time_score * 0.6 + usage_score * 0.4) * 0.4
            else:
                # 备用题目：使用时间短、频率低的优先轮入
                score += (1 - time_score * 0.6 - usage_score * 0.4) * 0.4
            
            # 质量分数（基于一致性代码）
            consistency = question.get('consistency_code', '3')
            quality_score = int(consistency) / 5.0 if consistency.isdigit() else 0.6
            
            if is_active:
                # 活跃题目：质量低的优先轮出
                score += (1 - quality_score) * 0.2
            else:
                # 备用题目：质量高的优先轮入
                score += quality_score * 0.2
            
            # 随机因子（增加一些随机性）
            score += random.random() * 0.1
            
            return score
        
        # 计算所有题目的智能分数
        active_scores = [(q, intelligent_score(q, True)) for q in active_questions]
        backup_scores = [(q, intelligent_score(q, False)) for q in backup_questions]
        
        # 按分数排序
        active_sorted = sorted(active_scores, key=lambda x: x[1], reverse=True)
        backup_sorted = sorted(backup_scores, key=lambda x: x[1], reverse=True)
        
        # 选择分数最高的题目
        questions_out = [q for q, _ in active_sorted[:rotation_count]]
        questions_in = [q for q, _ in backup_sorted[:rotation_count]]
        
        details = {
            'strategy': 'intelligent',
            'scoring_factors': ['difficulty_balance', 'usage_time', 'usage_frequency', 'quality', 'randomness'],
            'factor_weights': {'difficulty': 0.3, 'time': 0.4, 'quality': 0.2, 'random': 0.1},
            'avg_out_score': sum(score for _, score in active_sorted[:rotation_count]) / rotation_count if rotation_count > 0 else 0,
            'avg_in_score': sum(score for _, score in backup_sorted[:rotation_count]) / rotation_count if rotation_count > 0 else 0,
            'rotation_timestamp': datetime.datetime.utcnow().isoformat()
        }
        
        return questions_out, questions_in, details
    
    def _analyze_difficulty_distribution(self, questions: List[Dict]) -> Dict[str, float]:
        """
        分析题目难度分布
        
        Args:
            questions: 题目列表
            
        Returns:
            Dict[str, float]: 难度分布比例
        """
        if not questions:
            return {}
        
        difficulty_counts = Counter(q.get('difficulty_code', '3') for q in questions)
        total = len(questions)
        
        return {difficulty: count / total for difficulty, count in difficulty_counts.items()}
    
    def _group_by_difficulty(self, questions: List[Dict]) -> Dict[str, List[Dict]]:
        """
        按难度分组题目
        
        Args:
            questions: 题目列表
            
        Returns:
            Dict[str, List[Dict]]: 按难度分组的题目
        """
        groups = defaultdict(list)
        for question in questions:
            difficulty = question.get('difficulty_code', '3')
            groups[difficulty].append(question)
        return dict(groups)
    
    def update_question_status(self, question_ids: List[str], 
                              new_status: QuestionStatus) -> Dict[str, Any]:
        """
        更新题目状态
        
        Args:
            question_ids: 题目ID列表
            new_status: 新状态
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        return {
            'updated_count': len(question_ids),
            'question_ids': question_ids,
            'new_status': new_status.value,
            'timestamp': datetime.datetime.utcnow().isoformat()
        }
    
    def generate_rotation_report(self, rotation_history: List[Dict]) -> Dict[str, Any]:
        """
        生成轮换报告
        
        Args:
            rotation_history: 轮换历史记录
            
        Returns:
            Dict[str, Any]: 轮换报告
        """
        if not rotation_history:
            return {'total_rotations': 0, 'message': '暂无轮换记录'}
        
        total_rotations = len(rotation_history)
        total_questions_rotated = sum(h.get('rotation_count', 0) for h in rotation_history)
        
        # 策略使用统计
        strategy_counts = Counter(h.get('strategy_used', 'unknown') for h in rotation_history)
        
        # 成功率统计
        successful_rotations = sum(1 for h in rotation_history if h.get('execution_status') == 'completed')
        success_rate = successful_rotations / total_rotations if total_rotations > 0 else 0
        
        # 最近轮换
        recent_rotations = sorted(rotation_history, 
                                key=lambda x: x.get('rotation_date', datetime.datetime.min), 
                                reverse=True)[:5]
        
        return {
            'total_rotations': total_rotations,
            'total_questions_rotated': total_questions_rotated,
            'success_rate': success_rate,
            'strategy_usage': dict(strategy_counts),
            'recent_rotations': recent_rotations,
            'avg_questions_per_rotation': total_questions_rotated / total_rotations if total_rotations > 0 else 0
        }

# 使用示例
if __name__ == "__main__":
    # 创建备用题目管理器
    manager = BackupQuestionManager()
    
    # 创建轮换配置
    config = RotationConfig(
        strategy=RotationStrategy.INTELLIGENT,
        frequency=RotationFrequency.MONTHLY,
        rotation_percentage=30.0,
        difficulty_weights={'1': 0.1, '2': 0.2, '3': 0.4, '4': 0.2, '5': 0.1}
    )
    
    # 创建备用规则
    rule = manager.create_backup_rule(
        question_bank_id="test-bank-001",
        rule_name="月度智能轮换",
        standard_count=1000,
        total_count=1500,
        rotation_config=config
    )
    
    print(f"创建轮换规则: {rule['rule_name']}")
    print(f"轮换策略: {rule['rotation_strategy']}")
    print(f"轮换比例: {rule['rotation_percentage']}%")