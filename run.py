# -*- coding: utf-8 -*-
"""
题库管理系统一键启动脚本
自动检查环境、安装依赖、初始化数据库并启动服务
"""

import os
import sys
import subprocess
import platform
import time
import webbrowser
import threading
from pathlib import Path

# 设置控制台编码
if platform.system() == 'Windows':
    import locale
    try:
        # 尝试设置UTF-8编码
        os.system('chcp 65001 >nul 2>&1')
        # 设置环境变量
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['PYTHONUTF8'] = '1'
        # 设置控制台代码页
        import ctypes
        ctypes.windll.kernel32.SetConsoleCP(65001)
        ctypes.windll.kernel32.SetConsoleOutputCP(65001)
    except Exception as e:
        # 如果设置失败，至少确保基本的UTF-8环境变量
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['PYTHONUTF8'] = '1'

def safe_print(text):
    """安全打印函数，处理Unicode编码问题"""
    try:
        # 在Windows系统中，尝试使用UTF-8编码输出
        if platform.system() == 'Windows':
            try:
                # 尝试直接打印
                print(text, flush=True)
            except UnicodeEncodeError:
                # 如果失败，使用UTF-8编码
                print(text.encode('utf-8', 'replace').decode('utf-8'), flush=True)
        else:
            print(text)
    except (UnicodeEncodeError, UnicodeDecodeError):
        # 移除所有emoji和特殊Unicode字符，只保留文本内容
        import re
        # 使用更全面的Unicode字符正则表达式
        unicode_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F1E0-\U0001F1FF"  # flags (iOS)
            "\U00002000-\U0000206F"  # General Punctuation
            "\U00002070-\U0000209F"  # Superscripts and Subscripts
            "\U000020A0-\U000020CF"  # Currency Symbols
            "\U00002100-\U0000214F"  # Letterlike Symbols
            "\U00002150-\U0000218F"  # Number Forms
            "\U00002190-\U000021FF"  # Arrows
            "\U00002200-\U000022FF"  # Mathematical Operators
            "\U00002300-\U000023FF"  # Miscellaneous Technical
            "\U00002400-\U0000243F"  # Control Pictures
            "\U00002440-\U0000245F"  # Optical Character Recognition
            "\U00002460-\U000024FF"  # Enclosed Alphanumerics
            "\U00002500-\U0000257F"  # Box Drawing
            "\U00002580-\U0000259F"  # Block Elements
            "\U000025A0-\U000025FF"  # Geometric Shapes
            "\U00002600-\U000026FF"  # Miscellaneous Symbols
            "\U00002700-\U000027BF"  # Dingbats
            "\U000027C0-\U000027EF"  # Miscellaneous Mathematical Symbols-A
            "\U000027F0-\U000027FF"  # Supplemental Arrows-A
            "\U00002800-\U000028FF"  # Braille Patterns
            "\U00002900-\U0000297F"  # Supplemental Arrows-B
            "\U00002980-\U000029FF"  # Miscellaneous Mathematical Symbols-B
            "\U00002A00-\U00002AFF"  # Supplemental Mathematical Operators
            "\U00002B00-\U00002BFF"  # Miscellaneous Symbols and Arrows
            "\U0000FE00-\U0000FE0F"  # Variation Selectors
            "\U0000FE10-\U0000FE1F"  # Vertical Forms
            "\U0000FE20-\U0000FE2F"  # Combining Half Marks
            "\U0000FE30-\U0000FE4F"  # CJK Compatibility Forms
            "\U0000FE50-\U0000FE6F"  # Small Form Variants
            "\U0000FE70-\U0000FEFF"  # Arabic Presentation Forms-B
            "\U0000FF00-\U0000FFEF"  # Halfwidth and Fullwidth Forms
            "\U000024C2-\U0001F251"
            "]+", flags=re.UNICODE)
        clean_text = unicode_pattern.sub('', text)
        # 如果清理后的文本为空，则使用简化的消息
        if not clean_text.strip():
            if "访问地址" in text:
                clean_text = "访问地址: http://localhost:5000"
            elif "移动端" in text:
                clean_text = "移动端访问: http://你的IP地址:5000"
            elif "停止" in text:
                clean_text = "按 Ctrl+C 停止服务"
            else:
                clean_text = text.encode('ascii', 'ignore').decode('ascii')
        print(clean_text.strip())

def print_banner():
    """打印启动横幅"""
    # 设置控制台编码为UTF-8以支持Unicode字符
    try:
        if platform.system() == "Windows":
            os.system('chcp 65001 >nul 2>&1')
    except:
        pass
    
    print("="*60)
    safe_print("🎓 题库管理系统 - 一键启动脚本")
    safe_print("📚 Question Bank Management System")
    print("="*60)
    print()

def check_python_version():
    """检查Python版本"""
    safe_print("🔍 检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        safe_print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("   需要Python 3.7或更高版本")
        return False
    safe_print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def check_pip():
    """检查pip是否可用"""
    safe_print("🔍 检查pip...")
    try:
        # 设置环境变量以避免编码问题
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True, text=True, encoding='utf-8', env=env)
        safe_print("✅ pip可用")
        return True
    except subprocess.CalledProcessError:
        safe_print("❌ pip不可用")
        return False
    except Exception as e:
        safe_print(f"❌ pip检查出错: {e}")
        return False

def install_dependencies():
    """安装项目依赖"""
    safe_print("📦 检查并安装依赖包...")
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        safe_print("⚠️  requirements.txt文件不存在，跳过依赖安装")
        return True
    
    try:
        # 设置环境变量以避免编码问题
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        
        # 升级pip
        print("   升级pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True, text=True, encoding='utf-8', env=env)
        
        # 安装依赖
        print("   安装项目依赖...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True, text=True, encoding='utf-8', env=env)
        safe_print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        safe_print(f"❌ 依赖安装失败: {e}")
        print("   请手动运行: pip install -r requirements.txt")
        return False
    except Exception as e:
        safe_print(f"❌ 安装过程出错: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    safe_print("📁 创建必要目录...")
    directories = ['uploads', 'error_reports', 'templates']
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"   创建目录: {directory}")
        else:
            print(f"   目录已存在: {directory}")
    
    safe_print("✅ 目录检查完成")

def initialize_database():
    """初始化数据库"""
    safe_print("🗄️  初始化数据库...")
    
    try:
        # 导入数据库创建模块
        from create_tables import create_tables
        create_tables()
        safe_print("✅ 数据库初始化完成")
        return True
    except ImportError:
        safe_print("⚠️  create_tables模块不存在，跳过数据库初始化")
        return True
    except Exception as e:
        safe_print(f"❌ 数据库初始化失败: {e}")
        return False

def check_port(port=5000):
    """检查端口是否被占用"""
    safe_print(f"🔍 检查端口 {port}...")
    
    try:
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            result = s.connect_ex(('localhost', port))
            if result == 0:
                safe_print(f"⚠️  端口 {port} 已被占用")
                return False
            else:
                safe_print(f"✅ 端口 {port} 可用")
                return True
    except Exception as e:
        safe_print(f"⚠️  端口检查失败: {e}")
        return True

def open_browser():
    """延迟打开浏览器"""
    time.sleep(3)  # 等待服务器启动
    try:
        webbrowser.open('http://localhost:5000')
        safe_print("🌐 已自动打开浏览器访问系统")
    except Exception as e:
        safe_print(f"⚠️  自动打开浏览器失败: {e}")
        safe_print("   请手动访问: http://localhost:5000")

def start_application():
    """启动应用程序"""
    safe_print("🚀 启动题库管理系统...")
    print()
    
    # 处理打包环境的路径问题
    if getattr(sys, 'frozen', False):
        # 打包环境：设置工作目录为_internal目录
        bundle_dir = os.path.dirname(sys.executable)
        internal_dir = os.path.join(bundle_dir, '_internal')
        if os.path.exists(internal_dir):
            try:
                os.chdir(internal_dir)
                safe_print(f"📁 切换到内部目录: {internal_dir}")
            except Exception as e:
                safe_print(f"⚠️  目录切换失败: {e}")
    else:
        # 确保在正确的工作目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        try:
            os.chdir(script_dir)
            safe_print(f"📁 工作目录: {script_dir}")
        except Exception as e:
            safe_print(f"⚠️  目录设置失败: {e}")
    
    safe_print("📊 系统信息:")
    print(f"   操作系统: {platform.system()} {platform.release()}")
    print(f"   Python版本: {sys.version.split()[0]}")
    print(f"   工作目录: {os.getcwd()}")
    print()
    safe_print("🌐 访问地址: http://localhost:5000")
    safe_print("📱 移动端访问: http://你的IP地址:5000")
    safe_print("⏹️  按 Ctrl+C 停止服务")
    print("-" * 60)
    
    try:
        # 检查app.py文件是否存在
        app_file = os.path.join(os.getcwd(), 'app.py')
        if not os.path.exists(app_file):
            safe_print(f"❌ 找不到app.py文件: {app_file}")
            safe_print("   请确保在正确的目录中运行此脚本")
            safe_print("   按任意键退出...")
            input()
            return False
        
        safe_print(f"📄 找到app.py文件: {app_file}")
        
        # 导入Flask应用
        safe_print("🔄 正在导入Flask应用...")
        from app import app
        safe_print("✅ Flask应用导入成功")
        
        # 启动浏览器线程
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # 启动应用
        safe_print("🚀 Flask应用启动中...")
        app.run(debug=False, host='0.0.0.0', port=5000, threaded=True)
        
    except ImportError as e:
        safe_print(f"❌ 导入应用失败: {str(e)}")
        safe_print("   可能的原因:")
        safe_print("   1. app.py文件存在语法错误")
        safe_print("   2. 缺少必要的依赖包")
        safe_print("   3. Python路径配置问题")
        safe_print(f"   当前工作目录: {os.getcwd()}")
        safe_print(f"   Python路径: {sys.path[:3]}...")
        safe_print("   按任意键退出...")
        input()
        return False
    except KeyboardInterrupt:
        safe_print("\n👋 服务已停止")
        return True
    except OSError as e:
        if "Address already in use" in str(e) or "端口" in str(e):
            safe_print(f"❌ 端口5000已被占用: {e}")
            safe_print("   请关闭其他占用5000端口的程序后重试")
        else:
            safe_print(f"❌ 网络错误: {e}")
        safe_print("   按任意键退出...")
        input()
        return False
    except Exception as e:
        safe_print(f"❌ 启动失败: {e}")
        safe_print(f"   错误类型: {type(e).__name__}")
        safe_print("   按任意键退出...")
        input()
        return False

def main():
    """主函数 - 一键启动流程"""
    print_banner()
    
    # 环境检查
    if not check_python_version():
        sys.exit(1)
    
    # 检测是否为打包环境
    is_packaged = getattr(sys, 'frozen', False)
    
    if not is_packaged:
        # 只在非打包环境中检查pip和安装依赖
        if not check_pip():
            sys.exit(1)
        
        # 安装依赖
        if not install_dependencies():
            safe_print("⚠️  依赖安装失败，但继续尝试启动...")
    else:
        safe_print("🔍 检测到打包环境，跳过依赖检查...")
        safe_print("✅ 所有依赖已内置")
    
    # 创建目录
    create_directories()
    
    # 初始化数据库
    if not initialize_database():
        safe_print("⚠️  数据库初始化失败，但继续尝试启动...")
    
    # 检查端口
    if not check_port():
        safe_print("⚠️  端口被占用，但继续尝试启动...")
    
    print()
    safe_print("🎉 环境检查完成，准备启动服务...")
    time.sleep(2)
    print()
    
    # 启动应用
    if not start_application():
        sys.exit(1)

if __name__ == '__main__':
    main()