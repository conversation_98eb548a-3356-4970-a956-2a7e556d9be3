# -*- coding: utf-8 -*-
"""
重复题目检测算法模块

本模块实现多种文本相似度算法，用于检测题库中的重复题目。
支持的算法包括：
1. 编辑距离（Levenshtein Distance）
2. 余弦相似度（Cosine Similarity）
3. <PERSON><PERSON><PERSON>相似度（Jaccard Similarity）
4. 模糊匹配（Fuzzy Matching）
5. 综合相似度（Combined Similarity）

作者：SOLO Coding
创建时间：2024
"""

import re
import math
import jieba
import difflib
from typing import List, Dict, Tuple, Optional, Set
from collections import Counter
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
from fuzzywuzzy import fuzz
import hashlib

class SimilarityCalculator:
    """
    相似度计算器类
    
    提供多种文本相似度计算方法，支持中文文本处理。
    """
    
    def __init__(self, algorithm_weights: Optional[Dict[str, float]] = None):
        """
        初始化相似度计算器
        
        Args:
            algorithm_weights: 各算法权重配置，默认为均等权重
                格式: {
                    'edit_distance': 0.25,
                    'cosine': 0.25, 
                    'jaccard': 0.25,
                    'fuzzy': 0.25
                }
        """
        self.algorithm_weights = algorithm_weights or {
            'edit_distance': 0.25,
            'cosine': 0.25,
            'jaccard': 0.25,
            'fuzzy': 0.25
        }
        
        # 初始化TF-IDF向量化器
        self.tfidf_vectorizer = TfidfVectorizer(
            analyzer='char',  # 字符级别分析，适合中文
            ngram_range=(1, 3),  # 1-3字符的n-gram
            max_features=10000,
            lowercase=True
        )
        
        # 文本预处理正则表达式
        self.clean_pattern = re.compile(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]')  # 保留中文、英文、数字和空格
        
    def preprocess_text(self, text: str) -> str:
        """
        文本预处理
        
        Args:
            text: 原始文本
            
        Returns:
            str: 预处理后的文本
        """
        if not text:
            return ""
            
        # 转换为小写
        text = text.lower().strip()
        
        # 移除特殊字符，保留中文、英文、数字
        text = self.clean_pattern.sub(' ', text)
        
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def calculate_edit_distance(self, text1: str, text2: str) -> float:
        """
        计算编辑距离相似度
        
        使用Levenshtein距离算法计算两个文本的相似度。
        相似度 = 1 - (编辑距离 / max(len1, len2))
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            float: 相似度分数 (0-1)
        """
        text1 = self.preprocess_text(text1)
        text2 = self.preprocess_text(text2)
        
        if not text1 and not text2:
            return 1.0
        if not text1 or not text2:
            return 0.0
            
        # 计算编辑距离
        distance = self._levenshtein_distance(text1, text2)
        max_len = max(len(text1), len(text2))
        
        # 转换为相似度
        similarity = 1.0 - (distance / max_len) if max_len > 0 else 0.0
        return max(0.0, min(1.0, similarity))
    
    def _levenshtein_distance(self, s1: str, s2: str) -> int:
        """
        计算Levenshtein编辑距离
        
        Args:
            s1: 字符串1
            s2: 字符串2
            
        Returns:
            int: 编辑距离
        """
        if len(s1) < len(s2):
            return self._levenshtein_distance(s2, s1)
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]
    
    def calculate_cosine_similarity(self, text1: str, text2: str) -> float:
        """
        计算余弦相似度
        
        使用TF-IDF向量化后计算余弦相似度。
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            float: 相似度分数 (0-1)
        """
        text1 = self.preprocess_text(text1)
        text2 = self.preprocess_text(text2)
        
        if not text1 and not text2:
            return 1.0
        if not text1 or not text2:
            return 0.0
        
        try:
            # 创建TF-IDF向量
            tfidf_matrix = self.tfidf_vectorizer.fit_transform([text1, text2])
            
            # 计算余弦相似度
            similarity_matrix = cosine_similarity(tfidf_matrix)
            similarity = similarity_matrix[0][1]
            
            return max(0.0, min(1.0, similarity))
        except Exception:
            # 如果TF-IDF失败，使用简单的字符集合相似度
            return self._simple_cosine_similarity(text1, text2)
    
    def _simple_cosine_similarity(self, text1: str, text2: str) -> float:
        """
        简单余弦相似度计算（备用方法）
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            float: 相似度分数 (0-1)
        """
        # 字符频率统计
        counter1 = Counter(text1)
        counter2 = Counter(text2)
        
        # 获取所有字符
        all_chars = set(counter1.keys()) | set(counter2.keys())
        
        # 构建向量
        vector1 = [counter1.get(char, 0) for char in all_chars]
        vector2 = [counter2.get(char, 0) for char in all_chars]
        
        # 计算余弦相似度
        dot_product = sum(a * b for a, b in zip(vector1, vector2))
        magnitude1 = math.sqrt(sum(a * a for a in vector1))
        magnitude2 = math.sqrt(sum(b * b for b in vector2))
        
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0
        
        return dot_product / (magnitude1 * magnitude2)
    
    def calculate_jaccard_similarity(self, text1: str, text2: str) -> float:
        """
        计算Jaccard相似度
        
        基于字符n-gram的Jaccard相似度计算。
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            float: 相似度分数 (0-1)
        """
        text1 = self.preprocess_text(text1)
        text2 = self.preprocess_text(text2)
        
        if not text1 and not text2:
            return 1.0
        if not text1 or not text2:
            return 0.0
        
        # 生成字符2-gram集合
        ngrams1 = self._generate_ngrams(text1, n=2)
        ngrams2 = self._generate_ngrams(text2, n=2)
        
        # 计算Jaccard相似度
        intersection = len(ngrams1 & ngrams2)
        union = len(ngrams1 | ngrams2)
        
        return intersection / union if union > 0 else 0.0
    
    def _generate_ngrams(self, text: str, n: int = 2) -> Set[str]:
        """
        生成n-gram集合
        
        Args:
            text: 输入文本
            n: n-gram的长度
            
        Returns:
            Set[str]: n-gram集合
        """
        if len(text) < n:
            return {text}
        
        return {text[i:i+n] for i in range(len(text) - n + 1)}
    
    def calculate_fuzzy_similarity(self, text1: str, text2: str) -> float:
        """
        计算模糊匹配相似度
        
        使用fuzzywuzzy库的多种算法计算相似度。
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            float: 相似度分数 (0-1)
        """
        text1 = self.preprocess_text(text1)
        text2 = self.preprocess_text(text2)
        
        if not text1 and not text2:
            return 1.0
        if not text1 or not text2:
            return 0.0
        
        # 使用多种模糊匹配算法
        ratio = fuzz.ratio(text1, text2) / 100.0
        partial_ratio = fuzz.partial_ratio(text1, text2) / 100.0
        token_sort_ratio = fuzz.token_sort_ratio(text1, text2) / 100.0
        token_set_ratio = fuzz.token_set_ratio(text1, text2) / 100.0
        
        # 加权平均
        fuzzy_score = (
            ratio * 0.3 +
            partial_ratio * 0.2 +
            token_sort_ratio * 0.25 +
            token_set_ratio * 0.25
        )
        
        return max(0.0, min(1.0, fuzzy_score))
    
    def calculate_combined_similarity(self, text1: str, text2: str) -> Dict[str, float]:
        """
        计算综合相似度
        
        使用多种算法计算相似度，并返回详细结果。
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            Dict[str, float]: 包含各算法结果和综合分数的字典
                {
                    'edit_distance': float,
                    'cosine': float,
                    'jaccard': float,
                    'fuzzy': float,
                    'combined': float,
                    'percentage': float
                }
        """
        # 计算各种相似度
        edit_distance_score = self.calculate_edit_distance(text1, text2)
        cosine_score = self.calculate_cosine_similarity(text1, text2)
        jaccard_score = self.calculate_jaccard_similarity(text1, text2)
        fuzzy_score = self.calculate_fuzzy_similarity(text1, text2)
        
        # 计算加权综合分数
        combined_score = (
            edit_distance_score * self.algorithm_weights['edit_distance'] +
            cosine_score * self.algorithm_weights['cosine'] +
            jaccard_score * self.algorithm_weights['jaccard'] +
            fuzzy_score * self.algorithm_weights['fuzzy']
        )
        
        return {
            'edit_distance': edit_distance_score,
            'cosine': cosine_score,
            'jaccard': jaccard_score,
            'fuzzy': fuzzy_score,
            'combined': combined_score,
            'percentage': combined_score * 100
        }
    
    def generate_cache_key(self, text1: str, text2: str, algorithm_version: str = "1.0") -> str:
        """
        生成缓存键值
        
        Args:
            text1: 文本1
            text2: 文本2
            algorithm_version: 算法版本
            
        Returns:
            str: 缓存键值
        """
        # 确保文本顺序一致（较小的在前）
        if text1 > text2:
            text1, text2 = text2, text1
        
        # 创建唯一标识
        content = f"{text1}|{text2}|{algorithm_version}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()

class QuestionSimilarityDetector:
    """
    题目相似度检测器
    
    专门用于检测题库中题目的相似度。
    """
    
    def __init__(self, similarity_calculator: Optional[SimilarityCalculator] = None):
        """
        初始化题目相似度检测器
        
        Args:
            similarity_calculator: 相似度计算器实例
        """
        self.calculator = similarity_calculator or SimilarityCalculator()
    
    def extract_question_text(self, question_data: Dict) -> str:
        """
        提取题目的主要文本内容
        
        Args:
            question_data: 题目数据字典，包含题干、选项等信息
            
        Returns:
            str: 提取的文本内容
        """
        text_parts = []
        
        # 添加题干
        if question_data.get('question_stem'):
            text_parts.append(question_data['question_stem'])
        
        # 添加选项（如果是选择题）
        options = ['option_a', 'option_b', 'option_c', 'option_d', 'option_e']
        for option in options:
            if question_data.get(option):
                text_parts.append(question_data[option])
        
        return ' '.join(text_parts)
    
    def detect_similarity(self, question1: Dict, question2: Dict) -> Dict[str, float]:
        """
        检测两个题目的相似度
        
        Args:
            question1: 题目1数据
            question2: 题目2数据
            
        Returns:
            Dict[str, float]: 相似度检测结果
        """
        text1 = self.extract_question_text(question1)
        text2 = self.extract_question_text(question2)
        
        return self.calculator.calculate_combined_similarity(text1, text2)
    
    def batch_detect_duplicates(self, questions: List[Dict], 
                              similarity_threshold: float = 0.8) -> List[Dict]:
        """
        批量检测重复题目
        
        Args:
            questions: 题目列表
            similarity_threshold: 相似度阈值 (0-1)
            
        Returns:
            List[Dict]: 重复题目对列表
        """
        duplicates = []
        
        for i in range(len(questions)):
            for j in range(i + 1, len(questions)):
                similarity_result = self.detect_similarity(questions[i], questions[j])
                
                if similarity_result['combined'] >= similarity_threshold:
                    duplicates.append({
                        'question1': questions[i],
                        'question2': questions[j],
                        'similarity': similarity_result,
                        'similarity_percentage': similarity_result['percentage']
                    })
        
        # 按相似度降序排序
        duplicates.sort(key=lambda x: x['similarity_percentage'], reverse=True)
        
        return duplicates

# 使用示例
if __name__ == "__main__":
    # 创建相似度计算器
    calculator = SimilarityCalculator()
    
    # 测试文本
    text1 = "下列哪个选项是正确的？A. 选项1 B. 选项2 C. 选项3 D. 选项4"
    text2 = "以下哪个选项正确？A. 选项1 B. 选项2 C. 选项3 D. 选项4"
    
    # 计算相似度
    result = calculator.calculate_combined_similarity(text1, text2)
    print(f"相似度结果: {result}")
    
    # 创建题目检测器
    detector = QuestionSimilarityDetector(calculator)
    
    # 测试题目数据
    question1 = {
        'question_stem': '下列哪个选项是正确的？',
        'option_a': '选项1',
        'option_b': '选项2',
        'option_c': '选项3',
        'option_d': '选项4'
    }
    
    question2 = {
        'question_stem': '以下哪个选项正确？',
        'option_a': '选项1',
        'option_b': '选项2',
        'option_c': '选项3',
        'option_d': '选项4'
    }
    
    # 检测题目相似度
    similarity = detector.detect_similarity(question1, question2)
    print(f"题目相似度: {similarity['percentage']:.2f}%")