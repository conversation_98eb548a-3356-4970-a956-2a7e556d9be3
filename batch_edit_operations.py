# -*- coding: utf-8 -*-
"""
题目批量编辑操作模块

本模块实现题目的批量编辑功能，包括：
1. 选项位置批量调整
2. 正确答案字母位置自动关联更新
3. 修改预览和确认机制
4. 撤销操作和编辑历史记录
5. 多种编辑操作支持

作者：SOLO Coding
创建时间：2024
"""

import uuid
import datetime
import json
import copy
from typing import List, Dict, Optional, Any, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
import re
from collections import defaultdict

class EditOperation(Enum):
    """编辑操作类型枚举"""
    REORDER_OPTIONS = "reorder_options"          # 重新排序选项
    SHUFFLE_OPTIONS = "shuffle_options"          # 随机打乱选项
    SWAP_OPTIONS = "swap_options"                # 交换选项位置
    REVERSE_OPTIONS = "reverse_options"          # 反转选项顺序
    CUSTOM_ORDER = "custom_order"                # 自定义顺序
    FORMAT_OPTIONS = "format_options"            # 格式化选项
    REPLACE_TEXT = "replace_text"                # 替换文本
    UPDATE_DIFFICULTY = "update_difficulty"      # 更新难度

class EditStatus(Enum):
    """编辑状态枚举"""
    DRAFT = "draft"              # 草稿
    PREVIEW = "preview"          # 预览
    CONFIRMED = "confirmed"      # 已确认
    APPLIED = "applied"          # 已应用
    REVERTED = "reverted"        # 已撤销

@dataclass
class EditChange:
    """编辑变更数据类"""
    field_name: str              # 字段名称
    old_value: Any               # 原始值
    new_value: Any               # 新值
    change_type: str             # 变更类型
    description: str             # 变更描述

@dataclass
class QuestionEdit:
    """题目编辑数据类"""
    question_id: str
    original_data: Dict[str, Any]
    modified_data: Dict[str, Any]
    changes: List[EditChange]
    operation_type: EditOperation
    status: EditStatus
    created_at: datetime.datetime
    applied_at: Optional[datetime.datetime] = None

@dataclass
class BatchEditSession:
    """批量编辑会话数据类"""
    session_id: str
    question_ids: List[str]
    operations: List[EditOperation]
    question_edits: List[QuestionEdit]
    status: EditStatus
    created_by: str
    created_at: datetime.datetime
    preview_generated_at: Optional[datetime.datetime] = None
    applied_at: Optional[datetime.datetime] = None
    reverted_at: Optional[datetime.datetime] = None
    summary: Optional[Dict[str, Any]] = None

class BatchEditManager:
    """
    批量编辑管理器
    
    负责管理题目的批量编辑操作，包括预览、确认、应用和撤销。
    """
    
    def __init__(self):
        """
        初始化批量编辑管理器
        """
        self.active_sessions = {}  # 活跃编辑会话
        self.edit_history = []     # 编辑历史记录
        self.option_letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']  # 选项字母
    
    def create_edit_session(self, question_ids: List[str], 
                           operations: List[EditOperation],
                           created_by: str = "system") -> str:
        """
        创建批量编辑会话
        
        Args:
            question_ids: 要编辑的题目ID列表
            operations: 编辑操作列表
            created_by: 创建者
            
        Returns:
            str: 会话ID
        """
        session_id = str(uuid.uuid4())
        
        session = BatchEditSession(
            session_id=session_id,
            question_ids=question_ids,
            operations=operations,
            question_edits=[],
            status=EditStatus.DRAFT,
            created_by=created_by,
            created_at=datetime.datetime.utcnow()
        )
        
        self.active_sessions[session_id] = session
        return session_id
    
    def apply_batch_operations(self, session_id: str, 
                              questions_data: List[Dict[str, Any]],
                              operation_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用批量操作到题目数据
        
        Args:
            session_id: 会话ID
            questions_data: 题目数据列表
            operation_params: 操作参数
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        if session_id not in self.active_sessions:
            return {'success': False, 'error': '会话不存在'}
        
        session = self.active_sessions[session_id]
        
        try:
            # 为每个题目应用操作
            for question_data in questions_data:
                if question_data['id'] in session.question_ids:
                    question_edit = self._apply_operations_to_question(
                        question_data, session.operations, operation_params
                    )
                    session.question_edits.append(question_edit)
            
            # 更新会话状态
            session.status = EditStatus.PREVIEW
            session.preview_generated_at = datetime.datetime.utcnow()
            session.summary = self._generate_edit_summary(session.question_edits)
            
            return {
                'success': True,
                'session_id': session_id,
                'edited_count': len(session.question_edits),
                'summary': session.summary
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _apply_operations_to_question(self, question_data: Dict[str, Any],
                                     operations: List[EditOperation],
                                     params: Dict[str, Any]) -> QuestionEdit:
        """
        对单个题目应用操作
        
        Args:
            question_data: 题目数据
            operations: 操作列表
            params: 操作参数
            
        Returns:
            QuestionEdit: 题目编辑对象
        """
        original_data = copy.deepcopy(question_data)
        modified_data = copy.deepcopy(question_data)
        changes = []
        
        for operation in operations:
            if operation == EditOperation.REORDER_OPTIONS:
                change = self._reorder_options(modified_data, params.get('reorder_pattern', 'alphabetical'))
                if change:
                    changes.append(change)
            
            elif operation == EditOperation.SHUFFLE_OPTIONS:
                change = self._shuffle_options(modified_data, params.get('shuffle_seed'))
                if change:
                    changes.append(change)
            
            elif operation == EditOperation.SWAP_OPTIONS:
                swap_pairs = params.get('swap_pairs', [])
                change = self._swap_options(modified_data, swap_pairs)
                if change:
                    changes.append(change)
            
            elif operation == EditOperation.REVERSE_OPTIONS:
                change = self._reverse_options(modified_data)
                if change:
                    changes.append(change)
            
            elif operation == EditOperation.CUSTOM_ORDER:
                custom_order = params.get('custom_order', [])
                change = self._apply_custom_order(modified_data, custom_order)
                if change:
                    changes.append(change)
            
            elif operation == EditOperation.FORMAT_OPTIONS:
                format_rules = params.get('format_rules', {})
                change = self._format_options(modified_data, format_rules)
                if change:
                    changes.append(change)
            
            elif operation == EditOperation.REPLACE_TEXT:
                replace_rules = params.get('replace_rules', {})
                change = self._replace_text(modified_data, replace_rules)
                if change:
                    changes.append(change)
            
            elif operation == EditOperation.UPDATE_DIFFICULTY:
                new_difficulty = params.get('new_difficulty')
                change = self._update_difficulty(modified_data, new_difficulty)
                if change:
                    changes.append(change)
        
        # 确定主要操作类型
        primary_operation = operations[0] if operations else EditOperation.REORDER_OPTIONS
        
        return QuestionEdit(
            question_id=question_data['id'],
            original_data=original_data,
            modified_data=modified_data,
            changes=changes,
            operation_type=primary_operation,
            status=EditStatus.PREVIEW,
            created_at=datetime.datetime.utcnow()
        )
    
    def _reorder_options(self, question_data: Dict[str, Any], pattern: str) -> Optional[EditChange]:
        """
        重新排序选项
        
        Args:
            question_data: 题目数据
            pattern: 排序模式 ('alphabetical', 'length', 'random')
            
        Returns:
            Optional[EditChange]: 变更记录
        """
        options = self._extract_options(question_data)
        if not options:
            return None
        
        original_options = copy.deepcopy(options)
        original_answer = question_data.get('correct_answer', 'A')
        
        # 创建选项索引映射
        option_mapping = {}
        
        if pattern == 'alphabetical':
            # 按选项内容字母顺序排序
            sorted_options = sorted(enumerate(options), key=lambda x: x[1].strip())
            new_options = [opt for _, opt in sorted_options]
            option_mapping = {i: sorted_options[i][0] for i in range(len(options))}
        
        elif pattern == 'length':
            # 按选项长度排序
            sorted_options = sorted(enumerate(options), key=lambda x: len(x[1].strip()))
            new_options = [opt for _, opt in sorted_options]
            option_mapping = {i: sorted_options[i][0] for i in range(len(options))}
        
        elif pattern == 'random':
            # 随机排序
            import random
            indexed_options = list(enumerate(options))
            random.shuffle(indexed_options)
            new_options = [opt for _, opt in indexed_options]
            option_mapping = {i: indexed_options[i][0] for i in range(len(options))}
        
        else:
            return None
        
        # 更新选项
        self._update_options(question_data, new_options)
        
        # 更新正确答案
        new_answer = self._update_correct_answer(original_answer, option_mapping)
        question_data['correct_answer'] = new_answer
        
        return EditChange(
            field_name='options_and_answer',
            old_value={'options': original_options, 'answer': original_answer},
            new_value={'options': new_options, 'answer': new_answer},
            change_type='reorder',
            description=f'按{pattern}模式重新排序选项，答案从{original_answer}更新为{new_answer}'
        )
    
    def _shuffle_options(self, question_data: Dict[str, Any], seed: Optional[int] = None) -> Optional[EditChange]:
        """
        随机打乱选项
        
        Args:
            question_data: 题目数据
            seed: 随机种子
            
        Returns:
            Optional[EditChange]: 变更记录
        """
        import random
        
        if seed is not None:
            random.seed(seed)
        
        options = self._extract_options(question_data)
        if not options:
            return None
        
        original_options = copy.deepcopy(options)
        original_answer = question_data.get('correct_answer', 'A')
        
        # 创建索引列表并打乱
        indices = list(range(len(options)))
        random.shuffle(indices)
        
        # 重新排列选项
        new_options = [options[i] for i in indices]
        
        # 创建映射关系
        option_mapping = {new_pos: old_pos for new_pos, old_pos in enumerate(indices)}
        
        # 更新选项和答案
        self._update_options(question_data, new_options)
        new_answer = self._update_correct_answer(original_answer, {v: k for k, v in option_mapping.items()})
        question_data['correct_answer'] = new_answer
        
        return EditChange(
            field_name='options_and_answer',
            old_value={'options': original_options, 'answer': original_answer},
            new_value={'options': new_options, 'answer': new_answer},
            change_type='shuffle',
            description=f'随机打乱选项顺序，答案从{original_answer}更新为{new_answer}'
        )
    
    def _swap_options(self, question_data: Dict[str, Any], swap_pairs: List[Tuple[int, int]]) -> Optional[EditChange]:
        """
        交换选项位置
        
        Args:
            question_data: 题目数据
            swap_pairs: 交换对列表 [(index1, index2), ...]
            
        Returns:
            Optional[EditChange]: 变更记录
        """
        options = self._extract_options(question_data)
        if not options or not swap_pairs:
            return None
        
        original_options = copy.deepcopy(options)
        original_answer = question_data.get('correct_answer', 'A')
        
        # 执行交换
        new_options = copy.deepcopy(options)
        option_mapping = {i: i for i in range(len(options))}  # 初始映射
        
        for pos1, pos2 in swap_pairs:
            if 0 <= pos1 < len(new_options) and 0 <= pos2 < len(new_options):
                # 交换选项
                new_options[pos1], new_options[pos2] = new_options[pos2], new_options[pos1]
                
                # 更新映射
                for i in range(len(options)):
                    if option_mapping[i] == pos1:
                        option_mapping[i] = pos2
                    elif option_mapping[i] == pos2:
                        option_mapping[i] = pos1
        
        # 更新选项和答案
        self._update_options(question_data, new_options)
        new_answer = self._update_correct_answer(original_answer, {v: k for k, v in option_mapping.items()})
        question_data['correct_answer'] = new_answer
        
        return EditChange(
            field_name='options_and_answer',
            old_value={'options': original_options, 'answer': original_answer},
            new_value={'options': new_options, 'answer': new_answer},
            change_type='swap',
            description=f'交换选项位置{swap_pairs}，答案从{original_answer}更新为{new_answer}'
        )
    
    def _reverse_options(self, question_data: Dict[str, Any]) -> Optional[EditChange]:
        """
        反转选项顺序
        
        Args:
            question_data: 题目数据
            
        Returns:
            Optional[EditChange]: 变更记录
        """
        options = self._extract_options(question_data)
        if not options:
            return None
        
        original_options = copy.deepcopy(options)
        original_answer = question_data.get('correct_answer', 'A')
        
        # 反转选项
        new_options = list(reversed(options))
        
        # 创建映射关系
        option_mapping = {i: len(options) - 1 - i for i in range(len(options))}
        
        # 更新选项和答案
        self._update_options(question_data, new_options)
        new_answer = self._update_correct_answer(original_answer, {v: k for k, v in option_mapping.items()})
        question_data['correct_answer'] = new_answer
        
        return EditChange(
            field_name='options_and_answer',
            old_value={'options': original_options, 'answer': original_answer},
            new_value={'options': new_options, 'answer': new_answer},
            change_type='reverse',
            description=f'反转选项顺序，答案从{original_answer}更新为{new_answer}'
        )
    
    def _apply_custom_order(self, question_data: Dict[str, Any], custom_order: List[int]) -> Optional[EditChange]:
        """
        应用自定义选项顺序
        
        Args:
            question_data: 题目数据
            custom_order: 自定义顺序索引列表
            
        Returns:
            Optional[EditChange]: 变更记录
        """
        options = self._extract_options(question_data)
        if not options or len(custom_order) != len(options):
            return None
        
        original_options = copy.deepcopy(options)
        original_answer = question_data.get('correct_answer', 'A')
        
        # 按自定义顺序重排选项
        try:
            new_options = [options[i] for i in custom_order]
        except IndexError:
            return None
        
        # 创建映射关系
        option_mapping = {new_pos: old_pos for new_pos, old_pos in enumerate(custom_order)}
        
        # 更新选项和答案
        self._update_options(question_data, new_options)
        new_answer = self._update_correct_answer(original_answer, {v: k for k, v in option_mapping.items()})
        question_data['correct_answer'] = new_answer
        
        return EditChange(
            field_name='options_and_answer',
            old_value={'options': original_options, 'answer': original_answer},
            new_value={'options': new_options, 'answer': new_answer},
            change_type='custom_order',
            description=f'应用自定义顺序{custom_order}，答案从{original_answer}更新为{new_answer}'
        )
    
    def _format_options(self, question_data: Dict[str, Any], format_rules: Dict[str, Any]) -> Optional[EditChange]:
        """
        格式化选项文本
        
        Args:
            question_data: 题目数据
            format_rules: 格式化规则
            
        Returns:
            Optional[EditChange]: 变更记录
        """
        options = self._extract_options(question_data)
        if not options:
            return None
        
        original_options = copy.deepcopy(options)
        new_options = []
        
        for option in options:
            formatted_option = option
            
            # 应用格式化规则
            if format_rules.get('trim_whitespace', True):
                formatted_option = formatted_option.strip()
            
            if format_rules.get('remove_extra_spaces', True):
                formatted_option = re.sub(r'\s+', ' ', formatted_option)
            
            if format_rules.get('capitalize_first', False):
                formatted_option = formatted_option.capitalize()
            
            if format_rules.get('upper_case', False):
                formatted_option = formatted_option.upper()
            
            if format_rules.get('lower_case', False):
                formatted_option = formatted_option.lower()
            
            new_options.append(formatted_option)
        
        # 检查是否有变化
        if new_options == original_options:
            return None
        
        # 更新选项
        self._update_options(question_data, new_options)
        
        return EditChange(
            field_name='options',
            old_value=original_options,
            new_value=new_options,
            change_type='format',
            description=f'格式化选项文本，应用规则: {format_rules}'
        )
    
    def _replace_text(self, question_data: Dict[str, Any], replace_rules: Dict[str, Any]) -> Optional[EditChange]:
        """
        替换文本内容
        
        Args:
            question_data: 题目数据
            replace_rules: 替换规则 {'field': str, 'find': str, 'replace': str, 'regex': bool}
            
        Returns:
            Optional[EditChange]: 变更记录
        """
        field = replace_rules.get('field', 'question_text')
        find_text = replace_rules.get('find', '')
        replace_text = replace_rules.get('replace', '')
        use_regex = replace_rules.get('regex', False)
        
        if not find_text or field not in question_data:
            return None
        
        original_value = question_data[field]
        
        if use_regex:
            try:
                new_value = re.sub(find_text, replace_text, original_value)
            except re.error:
                return None
        else:
            new_value = original_value.replace(find_text, replace_text)
        
        if new_value == original_value:
            return None
        
        question_data[field] = new_value
        
        return EditChange(
            field_name=field,
            old_value=original_value,
            new_value=new_value,
            change_type='replace_text',
            description=f'在{field}中将"{find_text}"替换为"{replace_text}"'
        )
    
    def _update_difficulty(self, question_data: Dict[str, Any], new_difficulty: str) -> Optional[EditChange]:
        """
        更新题目难度
        
        Args:
            question_data: 题目数据
            new_difficulty: 新难度值
            
        Returns:
            Optional[EditChange]: 变更记录
        """
        if not new_difficulty:
            return None
        
        old_difficulty = question_data.get('difficulty_code', '3')
        
        if old_difficulty == new_difficulty:
            return None
        
        question_data['difficulty_code'] = new_difficulty
        
        return EditChange(
            field_name='difficulty_code',
            old_value=old_difficulty,
            new_value=new_difficulty,
            change_type='update_difficulty',
            description=f'难度从{old_difficulty}更新为{new_difficulty}'
        )
    
    def _extract_options(self, question_data: Dict[str, Any]) -> List[str]:
        """
        提取题目选项
        
        Args:
            question_data: 题目数据
            
        Returns:
            List[str]: 选项列表
        """
        options = []
        
        # 尝试不同的选项字段名
        option_fields = ['option_a', 'option_b', 'option_c', 'option_d', 'option_e', 'option_f']
        
        for field in option_fields:
            if field in question_data and question_data[field]:
                options.append(question_data[field])
            else:
                break
        
        return options
    
    def _update_options(self, question_data: Dict[str, Any], new_options: List[str]) -> None:
        """
        更新题目选项
        
        Args:
            question_data: 题目数据
            new_options: 新选项列表
        """
        option_fields = ['option_a', 'option_b', 'option_c', 'option_d', 'option_e', 'option_f']
        
        # 清空现有选项
        for field in option_fields:
            if field in question_data:
                question_data[field] = ''
        
        # 设置新选项
        for i, option in enumerate(new_options):
            if i < len(option_fields):
                question_data[option_fields[i]] = option
    
    def _update_correct_answer(self, original_answer: str, option_mapping: Dict[int, int]) -> str:
        """
        更新正确答案字母
        
        Args:
            original_answer: 原始答案字母
            option_mapping: 选项位置映射 {new_position: old_position}
            
        Returns:
            str: 新答案字母
        """
        # 将答案字母转换为索引
        if original_answer in self.option_letters:
            original_index = self.option_letters.index(original_answer)
        else:
            return original_answer
        
        # 查找新位置
        for new_pos, old_pos in option_mapping.items():
            if old_pos == original_index:
                return self.option_letters[new_pos] if new_pos < len(self.option_letters) else original_answer
        
        return original_answer
    
    def _generate_edit_summary(self, question_edits: List[QuestionEdit]) -> Dict[str, Any]:
        """
        生成编辑摘要
        
        Args:
            question_edits: 题目编辑列表
            
        Returns:
            Dict[str, Any]: 编辑摘要
        """
        if not question_edits:
            return {'total_questions': 0, 'total_changes': 0}
        
        total_changes = sum(len(edit.changes) for edit in question_edits)
        change_types = defaultdict(int)
        operation_types = defaultdict(int)
        
        for edit in question_edits:
            operation_types[edit.operation_type.value] += 1
            for change in edit.changes:
                change_types[change.change_type] += 1
        
        return {
            'total_questions': len(question_edits),
            'total_changes': total_changes,
            'change_types': dict(change_types),
            'operation_types': dict(operation_types),
            'avg_changes_per_question': total_changes / len(question_edits)
        }
    
    def get_edit_preview(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取编辑预览
        
        Args:
            session_id: 会话ID
            
        Returns:
            Optional[Dict[str, Any]]: 预览数据
        """
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        
        preview_data = []
        for edit in session.question_edits:
            preview_data.append({
                'question_id': edit.question_id,
                'original_data': edit.original_data,
                'modified_data': edit.modified_data,
                'changes': [asdict(change) for change in edit.changes],
                'operation_type': edit.operation_type.value
            })
        
        return {
            'session_id': session_id,
            'status': session.status.value,
            'preview_data': preview_data,
            'summary': session.summary
        }
    
    def confirm_edit_session(self, session_id: str) -> Dict[str, Any]:
        """
        确认编辑会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            Dict[str, Any]: 确认结果
        """
        if session_id not in self.active_sessions:
            return {'success': False, 'error': '会话不存在'}
        
        session = self.active_sessions[session_id]
        
        if session.status != EditStatus.PREVIEW:
            return {'success': False, 'error': '会话状态不正确'}
        
        # 更新状态
        session.status = EditStatus.CONFIRMED
        
        # 更新所有编辑的状态
        for edit in session.question_edits:
            edit.status = EditStatus.CONFIRMED
        
        return {
            'success': True,
            'session_id': session_id,
            'confirmed_questions': len(session.question_edits)
        }
    
    def apply_edit_session(self, session_id: str) -> Dict[str, Any]:
        """
        应用编辑会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            Dict[str, Any]: 应用结果
        """
        if session_id not in self.active_sessions:
            return {'success': False, 'error': '会话不存在'}
        
        session = self.active_sessions[session_id]
        
        if session.status != EditStatus.CONFIRMED:
            return {'success': False, 'error': '会话未确认'}
        
        try:
            # 这里应该调用数据库更新操作
            # 为了演示，我们只更新状态
            
            session.status = EditStatus.APPLIED
            session.applied_at = datetime.datetime.utcnow()
            
            for edit in session.question_edits:
                edit.status = EditStatus.APPLIED
                edit.applied_at = datetime.datetime.utcnow()
            
            # 添加到历史记录
            self.edit_history.append(session)
            
            return {
                'success': True,
                'session_id': session_id,
                'applied_questions': len(session.question_edits),
                'applied_at': session.applied_at.isoformat()
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def revert_edit_session(self, session_id: str) -> Dict[str, Any]:
        """
        撤销编辑会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            Dict[str, Any]: 撤销结果
        """
        # 查找会话（可能在活跃会话或历史记录中）
        session = None
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
        else:
            for hist_session in self.edit_history:
                if hist_session.session_id == session_id:
                    session = hist_session
                    break
        
        if not session:
            return {'success': False, 'error': '会话不存在'}
        
        if session.status != EditStatus.APPLIED:
            return {'success': False, 'error': '只能撤销已应用的编辑'}
        
        try:
            # 这里应该调用数据库回滚操作
            # 为了演示，我们只更新状态
            
            session.status = EditStatus.REVERTED
            session.reverted_at = datetime.datetime.utcnow()
            
            for edit in session.question_edits:
                edit.status = EditStatus.REVERTED
            
            return {
                'success': True,
                'session_id': session_id,
                'reverted_questions': len(session.question_edits),
                'reverted_at': session.reverted_at.isoformat()
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_edit_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取编辑历史记录
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 历史记录列表
        """
        # 合并活跃会话和历史记录
        all_sessions = list(self.active_sessions.values()) + self.edit_history
        
        # 按创建时间排序
        all_sessions.sort(key=lambda x: x.created_at, reverse=True)
        
        history = []
        for session in all_sessions[:limit]:
            history.append({
                'session_id': session.session_id,
                'question_count': len(session.question_ids),
                'operations': [op.value for op in session.operations],
                'status': session.status.value,
                'created_by': session.created_by,
                'created_at': session.created_at.isoformat(),
                'applied_at': session.applied_at.isoformat() if session.applied_at else None,
                'reverted_at': session.reverted_at.isoformat() if session.reverted_at else None,
                'summary': session.summary
            })
        
        return history

# 使用示例
if __name__ == "__main__":
    # 创建批量编辑管理器
    manager = BatchEditManager()
    
    # 创建编辑会话
    session_id = manager.create_edit_session(
        question_ids=["q1", "q2", "q3"],
        operations=[EditOperation.SHUFFLE_OPTIONS],
        created_by="admin"
    )
    
    print(f"创建编辑会话: {session_id}")
    
    # 模拟题目数据
    questions = [
        {
            'id': 'q1',
            'question_text': '什么是Python？',
            'option_a': 'A programming language',
            'option_b': 'A snake',
            'option_c': 'A movie',
            'option_d': 'A book',
            'correct_answer': 'A'
        }
    ]
    
    # 应用操作
    result = manager.apply_batch_operations(session_id, questions, {'shuffle_seed': 42})
    print(f"操作结果: {result}")
    
    # 获取预览
    preview = manager.get_edit_preview(session_id)
    print(f"编辑预览: {preview}")