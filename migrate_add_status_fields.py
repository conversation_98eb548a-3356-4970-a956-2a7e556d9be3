#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本：为questions表添加缺失的字段
"""

import sqlite3
import os
from datetime import datetime

def migrate_database():
    """为questions表添加缺失的字段"""
    db_path = 'question_bank.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件 {db_path} 不存在")
        return False
    
    try:
        conn = sqlite3.connect('local_dev.db')
        cursor = conn.cursor()
        
        # 检查当前表结构
        cursor.execute('PRAGMA table_info(questions)')
        columns = cursor.fetchall()
        existing_columns = [col[1] for col in columns]
        
        print("当前questions表的字段：")
        for col in existing_columns:
            print(f"  - {col}")
        
        # 需要添加的字段列表
        fields_to_add = [
            ('status', "VARCHAR(20) DEFAULT 'active'"),
            ('is_backup', "BOOLEAN DEFAULT 0"),
            ('backup_priority', "INTEGER DEFAULT 0"),
            ('last_used_date', "DATETIME"),
            ('usage_count', "INTEGER DEFAULT 0")
        ]
        
        # 添加缺失的字段
        added_fields = []
        for field_name, field_definition in fields_to_add:
            if field_name not in existing_columns:
                try:
                    sql = f"ALTER TABLE questions ADD COLUMN {field_name} {field_definition}"
                    print(f"执行SQL: {sql}")
                    cursor.execute(sql)
                    added_fields.append(field_name)
                    print(f"✓ 成功添加字段: {field_name}")
                except sqlite3.Error as e:
                    print(f"✗ 添加字段 {field_name} 失败: {e}")
            else:
                print(f"○ 字段 {field_name} 已存在，跳过")
        
        # 提交更改
        conn.commit()
        
        # 验证添加的字段
        if added_fields:
            print("\n验证新添加的字段：")
            cursor.execute('PRAGMA table_info(questions)')
            new_columns = cursor.fetchall()
            for col in new_columns:
                if col[1] in added_fields:
                    print(f"  ✓ {col[1]}: {col[2]}")
        
        conn.close()
        
        if added_fields:
            print(f"\n✓ 数据库迁移完成！成功添加了 {len(added_fields)} 个字段")
        else:
            print("\n○ 所有字段都已存在，无需迁移")
        
        return True
        
    except sqlite3.Error as e:
        print(f"数据库操作失败: {e}")
        return False
    except Exception as e:
        print(f"迁移过程中发生错误: {e}")
        return False

if __name__ == '__main__':
    print("开始数据库迁移...")
    print("=" * 50)
    
    success = migrate_database()
    
    print("=" * 50)
    if success:
        print("数据库迁移成功完成！")
    else:
        print("数据库迁移失败！")