#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
组题功能核心逻辑
支持按规则自动组题和手动组题
"""

import random
from typing import List, Dict, Optional, Tuple
from sqlalchemy.orm import Session
from models import Question, Paper, PaperQuestion, QuestionGroup, QuestionBank, QuestionUsageRecord, UsageStatistics
import datetime
from docx import Document
from docx.shared import Pt
from io import BytesIO

class PaperGenerator:
    """试卷生成器"""
    
    def __init__(self, db_session: Session):
        self.db_session = db_session
        
    def generate_paper_by_rules(self, 
                               paper_name: str,
                               paper_description: str = "",
                               total_score: float = 100.0,
                               duration: int = 120,
                               difficulty_level: str = "中等",
                               rules: Optional[List[Dict]] = None) -> Paper:
        """
        根据规则自动生成试卷
        
        Args:
            paper_name: 试卷名称
            paper_description: 试卷描述
            total_score: 试卷总分
            duration: 考试时长（分钟）
            difficulty_level: 试卷难度等级
            rules: 组题规则列表，格式如下：
                [
                    {
                        "question_type": "B",  # 题型代码
                        "difficulty": "3",     # 难度代码
                        "count": 10,           # 题目数量
                        "score_per_question": 5.0,  # 每题分值
                        "section_name": "单选题"    # 章节名称
                    },
                    ...
                ]
        
        Returns:
            Paper: 生成的试卷对象
        """
        if rules is None:
            rules = self._get_default_rules()
        
        # 创建试卷
        paper = Paper(
            name=paper_name,
            description=paper_description,
            total_score=total_score,
            duration=duration,
            difficulty_level=difficulty_level
        )
        
        self.db_session.add(paper)
        self.db_session.flush()  # 获取paper.id
        
        # 按规则选择题目
        question_order = 1
        for rule in rules:
            questions = self._select_questions_by_rule(rule)
            
            if len(questions) < rule.get('count', 1):
                raise ValueError(f"题型 {rule.get('question_type')} 难度 {rule.get('difficulty')} 的题目数量不足，需要 {rule.get('count')} 题，只有 {len(questions)} 题")
            
            # 随机选择指定数量的题目
            selected_questions = random.sample(questions, rule.get('count', 1))
            
            # 添加到试卷
            for question in selected_questions:
                paper_question = PaperQuestion(
                    paper_id=paper.id,
                    question_id=question.id,
                    question_order=question_order,
                    score=rule.get('score_per_question', 5.0),
                    section_name=rule.get('section_name', '')
                )
                self.db_session.add(paper_question)
                question_order += 1
        
        self.db_session.commit()
        return paper
    
    def _select_questions_by_rule(self, rule: Dict) -> List[Question]:
        """根据规则选择题目"""
        query = self.db_session.query(Question)
        
        # 按题型筛选
        if rule.get('question_type'):
            query = query.filter(Question.question_type_code == rule['question_type'])
        
        # 按难度筛选
        if rule.get('difficulty'):
            query = query.filter(Question.difficulty_code == rule['difficulty'])
        
        # 按一致性筛选（可选）
        if rule.get('consistency'):
            query = query.filter(Question.consistency_code == rule['consistency'])
        
        return query.all()
    
    def _get_default_rules(self) -> List[Dict]:
        """获取默认组题规则"""
        return [
            {
                "question_type": "B",
                "difficulty": "3",
                "count": 10,
                "score_per_question": 5.0,
                "section_name": "单选题"
            },
            {
                "question_type": "G",
                "difficulty": "3",
                "count": 5,
                "score_per_question": 8.0,
                "section_name": "多选题"
            },
            {
                "question_type": "C",
                "difficulty": "3",
                "count": 5,
                "score_per_question": 2.0,
                "section_name": "判断题"
            },
            {
                "question_type": "T",
                "difficulty": "3",
                "count": 3,
                "score_per_question": 5.0,
                "section_name": "填空题"
            },
            {
                "question_type": "D",
                "difficulty": "3",
                "count": 2,
                "score_per_question": 10.0,
                "section_name": "简答题"
            }
        ]
    
    def generate_paper_by_difficulty_distribution(self,
                                                paper_name: str,
                                                paper_description: str = "",
                                                total_score: float = 100.0,
                                                duration: int = 120,
                                                difficulty_distribution: Optional[Dict[str, float]] = None) -> Paper:
        """
        按难度分布生成试卷
        
        Args:
            paper_name: 试卷名称
            paper_description: 试卷描述
            total_score: 试卷总分
            duration: 考试时长（分钟）
            difficulty_distribution: 难度分布，格式：{"1": 0.1, "2": 0.2, "3": 0.4, "4": 0.2, "5": 0.1}
        """
        if difficulty_distribution is None:
            difficulty_distribution = {"1": 0.1, "2": 0.2, "3": 0.4, "4": 0.2, "5": 0.1}
        
        # 计算每种难度的题目数量
        total_questions = 25  # 默认总题数
        difficulty_counts = {}
        for difficulty, ratio in difficulty_distribution.items():
            difficulty_counts[difficulty] = int(total_questions * ratio)
        
        # 构建规则
        rules = []
        question_types = ["B", "G", "C", "T", "D"]  # 支持的题型
        scores = {"B": 4.0, "G": 6.0, "C": 2.0, "T": 4.0, "D": 8.0}  # 各题型分值
        
        for difficulty, count in difficulty_counts.items():
            if count > 0:
                # 为每种难度分配题型
                for i, question_type in enumerate(question_types):
                    if i < count:
                        rules.append({
                            "question_type": question_type,
                            "difficulty": difficulty,
                            "count": 1,
                            "score_per_question": scores[question_type],
                            "section_name": f"难度{difficulty}-{self._get_question_type_name(question_type)}"
                        })
        
        return self.generate_paper_by_rules(
            paper_name=paper_name,
            paper_description=paper_description,
            total_score=total_score,
            duration=duration,
            rules=rules
        )
    
    def _get_question_type_name(self, question_type_code: str) -> str:
        """获取题型名称，能处理 'B' 或 'B（单选题）' 等格式"""
        if not question_type_code:
            return "未知题型"
        
        # 提取括号前的代码，例如从 "B（单选题）" 中提取 "B"
        clean_code = question_type_code.split('（')[0].strip()

        type_names = {
            "B": "单选题",
            "G": "多选题", 
            "C": "判断题",
            "T": "填空题",
            "D": "简答题",
            "U": "计算题",
            "W": "论述题",
            "E": "案例分析",
            "F": "综合题"
        }
        return type_names.get(clean_code, "未知题型")
    
    def get_paper_statistics(self, paper_id: str) -> Dict:
        """获取试卷统计信息"""
        paper = self.db_session.query(Paper).filter(Paper.id == paper_id).first()
        if not paper:
            return {}
        
        paper_questions = self.db_session.query(PaperQuestion).filter(
            PaperQuestion.paper_id == paper_id
        ).order_by(PaperQuestion.question_order).all()
        
        # 统计信息
        stats = {
            "total_questions": len(paper_questions),
            "total_score": sum(pq.score for pq in paper_questions),
            "question_types": {},
            "difficulty_distribution": {},
            "sections": {}
        }
        
        for pq in paper_questions:
            question = pq.question
            
            # 题型统计
            question_type = question.question_type_code
            if question_type not in stats["question_types"]:
                stats["question_types"][question_type] = {
                    "count": 0,
                    "total_score": 0,
                    "name": self._get_question_type_name(question_type)
                }
            stats["question_types"][question_type]["count"] += 1
            stats["question_types"][question_type]["total_score"] += pq.score
            
            # 难度分布
            difficulty = question.difficulty_code
            if difficulty not in stats["difficulty_distribution"]:
                stats["difficulty_distribution"][difficulty] = {
                    "count": 0,
                    "total_score": 0
                }
            stats["difficulty_distribution"][difficulty]["count"] += 1
            stats["difficulty_distribution"][difficulty]["total_score"] += pq.score
            
            # 章节统计
            section_name = pq.section_name or "未分类"
            if section_name not in stats["sections"]:
                stats["sections"][section_name] = {
                    "count": 0,
                    "total_score": 0
                }
            stats["sections"][section_name]["count"] += 1
            stats["sections"][section_name]["total_score"] += pq.score
        
        return stats
    
    def export_paper_to_text(self, paper_id: str) -> str:
        """导出试卷为文本格式"""
        paper = self.db_session.query(Paper).filter(Paper.id == paper_id).first()
        if not paper:
            return "试卷不存在"
        
        paper_questions = self.db_session.query(PaperQuestion).filter(
            PaperQuestion.paper_id == paper_id
        ).order_by(PaperQuestion.question_order).all()
        
        # 生成试卷文本
        text = f"试卷名称：{paper.name}\n"
        text += f"试卷描述：{paper.description}\n"
        text += f"总分：{paper.total_score}分\n"
        text += f"考试时长：{paper.duration}分钟\n"
        text += f"难度等级：{paper.difficulty_level}\n"
        text += "=" * 50 + "\n\n"
        
        current_section = ""
        for i, pq in enumerate(paper_questions, 1):
            question = pq.question
            
            # 章节标题
            section_name = str(pq.section_name) if pq.section_name is not None else None
            if section_name is not None and section_name != current_section:
                current_section = pq.section_name
                text += f"\n{current_section}\n"
                text += "-" * 30 + "\n"
            
            # 题目内容
            text += f"{i}. ({pq.score}分) {question.stem}\n"
            
            # 选项（如果有）
            if question.option_a:
                text += f"   A. {question.option_a}\n"
            if question.option_b:
                text += f"   B. {question.option_b}\n"
            if question.option_c:
                text += f"   C. {question.option_c}\n"
            if question.option_d:
                text += f"   D. {question.option_d}\n"
            if question.option_e:
                text += f"   E. {question.option_e}\n"
            
            text += "\n"
        
        return text

    def generate_paper_by_knowledge_distribution(self, paper_name, paper_structure, knowledge_distribution, **kwargs):
        """
        核心组卷逻辑：根据题库、题型、知识点分布生成试卷
        支持试题ID重复率约束
        
        Args:
            paper_name: 试卷名称
            paper_structure: 试卷结构，包含题型、题量、分值等信息
            knowledge_distribution: 知识点分布
            **kwargs: 其他参数
                - paper_description: 试卷描述
                - total_score: 试卷总分
                - duration: 考试时长（分钟）
                - difficulty_level: 试卷难度等级
                - repeat_rate_limit: 试题ID重复率限制，默认为0.09（9%）
                  三级代码是指题目ID中的第5-7段，例如"BWGL-3-LL-B-A-A-A-001-001"中的"A-A-A"
        """
        # 获取重复率限制参数，默认为0.09（9%）
        repeat_rate_limit = kwargs.get('repeat_rate_limit', 0.09)
        print(f"使用试题ID重复率限制: {repeat_rate_limit * 100}%")
        
        paper = Paper(
            name=paper_name,
            description=kwargs.get('paper_description', f"基于知识点分布自动生成的试卷"),
            total_score=kwargs.get('total_score', 100.0),
            duration=kwargs.get('duration', 120),
            difficulty_level=kwargs.get('difficulty_level', '中等'),
        )
        self.db_session.add(paper)
        self.db_session.flush()

        # 获取已使用的题目ID，用于控制重复率
        # 记录已使用的题目ID和每套试卷的题目ID列表
        used_question_ids = set()
        previous_papers_questions = []  # 存储每套已生成试卷的题目ID列表
        
        # 从paper_name中提取套号（如果有）
        paper_set_num = None
        if "_第" in paper_name and "套" in paper_name:
            try:
                paper_set_num = int(paper_name.split("_第")[1].split("套")[0])
            except (ValueError, IndexError):
                pass
        
        # 获取之前生成的同名试卷（不含套号）的题目
        if paper_set_num and paper_set_num > 1:
            base_paper_name = paper_name.split("_第")[0]
            previous_papers = self.db_session.query(Paper).filter(
                Paper.name.like(f"{base_paper_name}%")
            ).all()
            
            for prev_paper in previous_papers:
                prev_questions = self.db_session.query(PaperQuestion).filter(
                    PaperQuestion.paper_id == prev_paper.id
                ).all()
                
                # 收集该试卷的所有题目ID
                paper_question_ids = []
                for pq in prev_questions:
                    used_question_ids.add(pq.question_id)
                    paper_question_ids.append(pq.question_id)
                
                # 将该试卷的题目ID列表添加到记录中
                if paper_question_ids:
                    previous_papers_questions.append(paper_question_ids)
            
            print(f"已生成{len(previous_papers_questions)}套试卷，重复率限制: {repeat_rate_limit * 100}%")

        # 如果有知识点分布要求，先计算整个试卷的三级代码分配
        global_level3_allocations = {}
        if knowledge_distribution:
            total_questions = sum(rule['count'] for rule in paper_structure)
            total_ratio = sum(knowledge_distribution.values())
            
            print(f"\n=== 全局知识点分布计算 ===")
            print(f"试卷总题目数: {total_questions}道题")
            print(f"知识点分布: {knowledge_distribution}")
            print(f"总比重: {total_ratio}")
            
            # 精确分配算法：先计算每个三级代码的精确题目数，然后处理余数
            remaining_count = total_questions
            
            # 第一轮：计算每个三级代码的基础分配数量（向下取整）
            for level3_code, ratio in knowledge_distribution.items():
                exact_count = total_questions * ratio / total_ratio
                base_count = int(exact_count)  # 向下取整
                global_level3_allocations[level3_code] = {
                    'total_count': base_count,
                    'exact_count': exact_count,
                    'remainder': exact_count - base_count,
                    'allocated_by_type': {},  # 记录每个题型分配的数量
                    'remaining_to_allocate': base_count  # 剩余待分配数量
                }
                remaining_count -= base_count
            
            # 第二轮：将剩余的题目按余数大小分配
            if remaining_count > 0:
                # 按余数从大到小排序
                sorted_by_remainder = sorted(
                    global_level3_allocations.items(),
                    key=lambda x: x[1]['remainder'],
                    reverse=True
                )
                
                # 将剩余题目分配给余数最大的几个三级代码
                for i in range(remaining_count):
                    if i < len(sorted_by_remainder):
                        level3_code = sorted_by_remainder[i][0]
                        global_level3_allocations[level3_code]['total_count'] += 1
                        global_level3_allocations[level3_code]['remaining_to_allocate'] += 1
            
            print(f"\n全局三级代码分配结果:")
            for level3_code, allocation in global_level3_allocations.items():
                print(f"  {level3_code}: 总需求{allocation['total_count']}道题 (精确值{allocation['exact_count']:.2f})")
            
            print(f"\n全局分配验证: 总分配{sum(a['total_count'] for a in global_level3_allocations.values())}道题, 预期{total_questions}道题")

        question_order = 1
        for rule in paper_structure:
            bank_name = rule['question_bank_name']
            q_type = rule['question_type']
            count = rule['count']
            score = rule['score_per_question']

            # 核心修改：通过JOIN和题库名称筛选题目
            base_query = self.db_session.query(Question).join(QuestionBank).filter(
                QuestionBank.题库名称 == str(bank_name),
                Question.question_type_code.startswith(q_type)
            )
            
            available_questions = base_query.all()
            
            if len(available_questions) < count:
                raise ValueError(f"题库 '{bank_name}' 中题型 '{q_type}' 的题目不足。需要 {count} 道，但只有 {len(available_questions)} 道。")
            
            # 根据知识点分布要求按比重精确分配题目
            if knowledge_distribution:
                # 将题目按三级代码分组
                questions_by_level3 = {}
                for q in available_questions:
                    if q.id in used_question_ids:
                        continue
                    
                    # 检查试题ID重复率约束
                    if paper_set_num and paper_set_num > 1 and previous_papers_questions:
                        # 计算当前题目与每套已生成试卷的重复情况
                        should_skip = False
                        for prev_paper_questions in previous_papers_questions:
                            if q.id in prev_paper_questions:
                                # 如果题目已在某套试卷中使用，跳过
                                should_skip = True
                                break
                        
                        if should_skip:
                            continue
                    
                    try:
                        parts = q.id.split('-')
                        if len(parts) >= 9:
                            level3_code = f"{parts[4]}-{parts[5]}-{parts[6]}"
                            
                            if level3_code not in questions_by_level3:
                                questions_by_level3[level3_code] = []
                            questions_by_level3[level3_code].append(q)
                    except Exception:
                        continue
                
                # 基于全局分配计算当前题型的分配
                selected_questions = []
                total_questions = sum(rule['count'] for rule in paper_structure)
                
                print(f"\n=== 题型 {q_type} 知识点分配 ===")
                print(f"题型需求: {count}道题")
                
                # 计算当前题型在全局分配中的比例
                type_ratio = count / total_questions
                print(f"题型比例: {type_ratio:.2%}")
                
                # 真正的按比例分配策略：确保各题型分配总和严格等于全局需求
                type_level3_allocations = {}
                
                # 第一步：按比例计算每个三级代码在当前题型中的基础分配
                remaining_count = count
                for level3_code, global_allocation in global_level3_allocations.items():
                    global_count = global_allocation['total_count']
                    remaining_to_allocate = global_allocation['remaining_to_allocate']
                    
                    # 按比例计算当前题型应分配的数量
                    if remaining_to_allocate > 0:
                        exact_allocation = global_count * type_ratio
                        base_allocation = int(exact_allocation)  # 向下取整
                        # 确保不超过剩余可分配数量
                        base_allocation = min(base_allocation, remaining_to_allocate)
                    else:
                        base_allocation = 0
                        exact_allocation = 0
                    
                    type_level3_allocations[level3_code] = {
                        'base_count': base_allocation,
                        'exact_count': exact_allocation,
                        'remainder': exact_allocation - base_allocation if exact_allocation > base_allocation else 0,
                        'global_count': global_count,
                        'remaining_to_allocate': remaining_to_allocate
                    }
                    remaining_count -= base_allocation
                
                # 第二步：将剩余题目按余数大小分配
                if remaining_count > 0:
                    # 只考虑还有剩余可分配数量的三级代码
                    candidates = [(k, v) for k, v in type_level3_allocations.items() 
                                if v['remaining_to_allocate'] > v['base_count'] and v['remainder'] > 0]
                    candidates.sort(key=lambda x: x[1]['remainder'], reverse=True)
                    
                    for i in range(min(remaining_count, len(candidates))):
                        level3_code = candidates[i][0]
                        if type_level3_allocations[level3_code]['base_count'] < type_level3_allocations[level3_code]['remaining_to_allocate']:
                            type_level3_allocations[level3_code]['base_count'] += 1
                            remaining_count -= 1
                            if remaining_count == 0:
                                break
                
                # 更新全局分配记录
                for level3_code, allocation in type_level3_allocations.items():
                    allocated_count = allocation['base_count']
                    global_level3_allocations[level3_code]['allocated_by_type'][q_type] = allocated_count
                    global_level3_allocations[level3_code]['remaining_to_allocate'] -= allocated_count
                
                # 执行分配
                for level3_code, allocation in type_level3_allocations.items():
                    required_count = allocation['base_count']
                    if required_count > 0:
                        print(f"三级代码 {level3_code}: 全局需求{global_level3_allocations[level3_code]['total_count']}道, 当前题型分配{required_count}道题")
                        
                        if level3_code in questions_by_level3:
                            available_for_this_code = questions_by_level3[level3_code]
                            actual_count = min(required_count, len(available_for_this_code))
                            
                            if actual_count > 0:
                                selected_from_this_code = random.sample(available_for_this_code, actual_count)
                                selected_questions.extend(selected_from_this_code)
                                print(f"  实际选择: {actual_count}道题")
                            else:
                                print(f"  警告: 该三级代码无可用题目")
                        else:
                            print(f"  警告: 题库中没有三级代码为 {level3_code} 的题目")
                
                print(f"\n题型分配验证: 总分配{sum(a['base_count'] for a in type_level3_allocations.values())}道题, 预期{count}道题")
                
                # 如果选择的题目数量不足，从其他可用题目中补充
                if len(selected_questions) < count:
                    remaining_needed = count - len(selected_questions)
                    print(f"\n需要补充 {remaining_needed} 道题")
                    
                    # 收集所有未被选中的可用题目
                    selected_ids = {q.id for q in selected_questions}
                    remaining_questions = []
                    for q in available_questions:
                        if q.id not in used_question_ids and q.id not in selected_ids:
                            try:
                                parts = q.id.split('-')
                                if len(parts) >= 9:
                                    level3_code = f"{parts[4]}-{parts[5]}-{parts[6]}"
                                    if level3_code in level3_code_usage:
                                        if paper_set_num and paper_set_num > 1:
                                            repeat_rate = level3_code_usage[level3_code] / (paper_set_num - 1)
                                            if repeat_rate > repeat_rate_limit:
                                                continue
                                remaining_questions.append(q)
                            except Exception:
                                remaining_questions.append(q)
                    
                    if len(remaining_questions) >= remaining_needed:
                        additional_questions = random.sample(remaining_questions, remaining_needed)
                        selected_questions.extend(additional_questions)
                        print(f"成功补充 {len(additional_questions)} 道题")
                    else:
                        print(f"警告: 只能补充 {len(remaining_questions)} 道题，仍缺少 {remaining_needed - len(remaining_questions)} 道题")
                        selected_questions.extend(remaining_questions)
                
                print(f"最终选择题目数量: {len(selected_questions)}")
                knowledge_filtered_questions = selected_questions
            else:
                # 如果没有知识点分布要求，只进行试题ID重复率筛选
                knowledge_filtered_questions = []
                for q in available_questions:
                    # 如果题目已经使用过，跳过
                    if q.id in used_question_ids:
                        continue
                    
                    # 检查试题ID重复率约束
                    if paper_set_num and paper_set_num > 1 and previous_papers_questions:
                        # 计算当前题目与每套已生成试卷的重复情况
                        should_skip = False
                        for prev_paper_questions in previous_papers_questions:
                            if q.id in prev_paper_questions:
                                # 如果题目已在某套试卷中使用，跳过
                                should_skip = True
                                break
                        
                        if should_skip:
                            continue
                    
                    knowledge_filtered_questions.append(q)
                
                # 如果没有知识点分布要求，随机选择指定数量的题目
                if len(knowledge_filtered_questions) >= count:
                    selected_questions = random.sample(knowledge_filtered_questions, count)
                else:
                    selected_questions = knowledge_filtered_questions
            
            # 确保有足够的题目
            if not knowledge_distribution:
                # 没有知识点分布要求的情况已在上面处理
                pass
            elif len(selected_questions) < count:
                print(f"警告：题库 '{bank_name}' 中题型 '{q_type}' 的符合知识点分布和重复率约束的题目不足。需要 {count} 道，但只有 {len(selected_questions)} 道。")
                if len(selected_questions) == 0:
                    # 如果完全没有符合条件的题目，回退到随机选择
                    selected_questions = random.sample(available_questions, min(count, len(available_questions)))

            for q in selected_questions:
                # 更新已使用的题目ID
                used_question_ids.add(q.id)
                
                pq = PaperQuestion(
                    paper_id=paper.id,
                    question_id=q.id,
                    question_order=question_order,
                    score=score,
                    section_name=f"{self._get_question_type_name(q_type)}"
                )
                self.db_session.add(pq)
                question_order += 1
        
        self.db_session.commit()
        
        # 记录题目使用情况
        self._record_question_usage(paper.id)
        
        # 验证试题重复率（如果是多套试卷）
        if paper_set_num and paper_set_num > 1:
            self._verify_paper_repeat_rate(paper.id, base_paper_name, repeat_rate_limit)
        
        # 验证各题型分配总和是否等于全局需求
        if knowledge_distribution:
            print(f"\n=== 各题型分配验证 ===")
            for level3_code, allocation in global_level3_allocations.items():
                type_sum = sum(allocation['allocated_by_type'].values())
                print(f"  {level3_code}: 全局需求{allocation['total_count']}道, 各题型分配总和{type_sum}道")
                
                # 检查分配是否正确
                if type_sum != allocation['total_count']:
                    print(f"  ⚠️ 警告: 分配不匹配! 全局需求{allocation['total_count']}道, 实际分配{type_sum}道")
        
        # 输出最终统计信息
        print("\n组卷完成统计:")
        print(f"- 试卷名称: {paper.name}")
        print(f"- 总题数: {question_order - 1}")
        # 获取题库名称列表
        bank_names = []
        for r in paper_structure:
            try:
                bank = self.db_session.query(QuestionBank).filter(
                    QuestionBank.题库名称 == r['question_bank_name']
                ).first()
                if bank:
                    bank_names.append(bank.name)
            except Exception as e:
                print(f"获取题库名称错误: {str(e)}")
        
        print(f"- 使用题库: {', '.join(set(bank_names)) if bank_names else '无'}")
        print(f"- 知识点分布:")
        stats = self.get_level3_code_statistics(str(paper.id))
        for code, percent in stats.items():
            print(f"  - {code}: {percent}%")
        
        return paper
    
    def _verify_paper_repeat_rate(self, current_paper_id: str, base_paper_name: str, repeat_rate_limit: float):
        """验证当前试卷与已有试卷的重复率"""
        try:
            # 获取当前试卷的题目
            current_questions = self.db_session.query(PaperQuestion).filter(
                PaperQuestion.paper_id == current_paper_id
            ).all()
            current_question_ids = set(pq.question_id for pq in current_questions)
            
            # 获取同名系列的其他试卷
            other_papers = self.db_session.query(Paper).filter(
                Paper.name.like(f"{base_paper_name}%"),
                Paper.id != current_paper_id
            ).all()
            
            print(f"\n=== 试题重复率验证 ===")
            print(f"当前试卷题目数: {len(current_question_ids)}")
            
            for other_paper in other_papers:
                other_questions = self.db_session.query(PaperQuestion).filter(
                    PaperQuestion.paper_id == other_paper.id
                ).all()
                other_question_ids = set(pq.question_id for pq in other_questions)
                
                # 计算重复题目数量
                common_questions = current_question_ids.intersection(other_question_ids)
                repeat_rate = len(common_questions) / len(current_question_ids) if current_question_ids else 0
                
                print(f"与 '{other_paper.name}' 的重复率: {repeat_rate:.2%} ({len(common_questions)}/{len(current_question_ids)})")
                
                if repeat_rate > repeat_rate_limit:
                    print(f"警告: 重复率 {repeat_rate:.2%} 超过限制 {repeat_rate_limit:.2%}")
                else:
                    print(f"✓ 重复率符合要求")
            
            print(f"重复率验证完成")
            
        except Exception as e:
            print(f"重复率验证失败: {e}")
    
    def _record_question_usage(self, paper_id: str):
        """
        记录试卷中题目的使用情况
        
        Args:
            paper_id: 试卷ID
        """
        try:
            # 获取试卷中的所有题目
            paper_questions = self.db_session.query(PaperQuestion).filter(
                PaperQuestion.paper_id == paper_id
            ).all()
            
            # 获取试卷信息
            paper = self.db_session.query(Paper).filter(Paper.id == paper_id).first()
            if not paper:
                print(f"警告: 试卷 {paper_id} 不存在，无法记录使用情况")
                return
            
            print(f"\n=== 记录题目使用情况 ===")
            print(f"试卷: {paper.name}")
            print(f"题目数量: {len(paper_questions)}")
            
            # 记录每个题目的使用情况
            for pq in paper_questions:
                # 创建使用记录
                usage_record = QuestionUsageRecord(
                    question_id=pq.question_id,
                    paper_id=paper_id,
                    used_at=datetime.datetime.now(),
                    question_order=pq.question_order,
                    score=pq.score,
                    section_name=pq.section_name
                )
                self.db_session.add(usage_record)
                
                # 更新或创建统计记录
                usage_stat = self.db_session.query(UsageStatistics).filter(
                    UsageStatistics.question_id == pq.question_id
                ).first()
                
                if usage_stat:
                    # 更新现有统计
                    usage_stat.usage_count += 1
                    usage_stat.last_used_at = datetime.datetime.now()
                    usage_stat.total_score += pq.score
                else:
                    # 创建新的统计记录
                    # 获取题目信息
                    question = self.db_session.query(Question).filter(
                        Question.id == pq.question_id
                    ).first()
                    
                    if question:
                        # 获取题库ID
                        bank = self.db_session.query(QuestionBank).filter(
                            QuestionBank.name == question.bank_name
                        ).first()
                        bank_id = bank.id if bank else None
                        
                        usage_stat = UsageStatistics(
                            question_id=pq.question_id,
                            bank_id=bank_id,
                            usage_count=1,
                            first_used_at=datetime.datetime.now(),
                            last_used_at=datetime.datetime.now(),
                            total_score=pq.score,
                            question_type=question.question_type_code,
                            difficulty_level=question.difficulty_code
                        )
                        self.db_session.add(usage_stat)
            
            # 提交使用记录
            self.db_session.commit()
            print(f"✓ 成功记录 {len(paper_questions)} 道题目的使用情况")
            
        except Exception as e:
            print(f"记录题目使用情况失败: {e}")
            self.db_session.rollback()
    
    def get_level3_code_statistics(self, paper_id: str) -> Dict:
        """获取试卷中题目的三级代码统计信息"""
        
        paper = self.db_session.query(Paper).filter(Paper.id == paper_id).first()
        if not paper:
            return {}
        
        paper_questions = self.db_session.query(PaperQuestion).filter(
            PaperQuestion.paper_id == paper_id
        ).all()
        
        # 统计三级代码分布
        level3_code_stats = {}
        total_questions = len(paper_questions)
        
        for pq in paper_questions:
            question = pq.question
            try:
                # 解析题目ID，提取三级代码
                # 使用正确的9段题目ID解析格式：BWGL-3-LL-B-A-A-A-001-001
                # 第1-3段为题库名称代码，第4段为题型代码缩写（B-单选题），第5-7段为三级代码，第8段为知识点代码，第9段为顺序号
                parts = question.id.split('-')
                if len(parts) >= 9:
                    # 构建三级代码（第5-7段，例如：A-A-A）
                    level3_code = f"{parts[4]}-{parts[5]}-{parts[6]}"
                    
                    # 更新统计信息
                    if level3_code not in level3_code_stats:
                        level3_code_stats[level3_code] = 0
                    level3_code_stats[level3_code] += 1
            except Exception as e:
                print(f"解析题目ID失败: {question.id}, 错误: {e}")
                continue
        
        # 计算比例
        level3_code_proportion = {}
        for code, count in level3_code_stats.items():
            proportion = round((count / total_questions) * 100, 1) if total_questions > 0 else 0
            level3_code_proportion[code] = proportion
        
        return level3_code_proportion
    
    def export_paper_to_docx(self, paper_id: str):
        """将试卷导出为 DOCX 格式，包含题型分组和格式化标题"""
        paper = self.db_session.query(Paper).filter(Paper.id == paper_id).first()
        if not paper:
            raise ValueError("试卷未找到")

        doc = Document()
        doc.add_heading(str(paper.name), level=1)
        
        meta_info = f"总分：{paper.total_score or 100.0}分    时长：{paper.duration or 120}分钟    难度：{paper.difficulty_level or '中等'}"
        doc.add_paragraph(meta_info)
        doc.add_paragraph("--------------------------------------------------")

        paper_questions = self.db_session.query(PaperQuestion).filter(PaperQuestion.paper_id == paper.id).order_by(PaperQuestion.question_order).all()

        if not paper_questions:
            doc.add_paragraph("该试卷内没有题目。")
        else:
            # 按题型分组
            sections = {}
            for pq in paper_questions:
                q = pq.question
                q_type_code = q.question_type_code or "UN"
                q_type_name = self._get_question_type_name(q_type_code)
                
                if q_type_name not in sections:
                    sections[q_type_name] = {"questions": [], "total_score": 0.0}
                
                sections[q_type_name]["questions"].append(pq)
                sections[q_type_name]["total_score"] += pq.score

            # 写入每个题型分组
            section_counter = 1
            roman_numerals = {1: '一', 2: '二', 3: '三', 4: '四', 5: '五', 6: '六'}
            
            for section_name, data in sections.items():
                questions_in_section = data["questions"]
                count = len(questions_in_section)
                score_per_question = questions_in_section[0].score if count > 0 else 0
                section_total_score = data["total_score"]
                
                section_title = f"{roman_numerals.get(section_counter, section_counter)}、{section_name}（共{count}题，每题{score_per_question}分，计{section_total_score}分）"
                doc.add_heading(section_title, level=2)

                for pq in questions_in_section:
                    q = pq.question
                    p = doc.add_paragraph()
                    p.add_run(f"第{pq.question_order}题. ").bold = True
                    stem_text = q.question_stem if q.question_stem is not None else ""
                    p.add_run(stem_text)

                    # 添加选项，并为单选题排除 E 选项
                    is_single_choice = q.question_type_code and q.question_type_code.startswith('B')
                    
                    if q.option_a is not None: p.add_run(f"\nA. {q.option_a}")
                    if q.option_b is not None: p.add_run(f"\nB. {q.option_b}")
                    if q.option_c is not None: p.add_run(f"\nC. {q.option_c}")
                    if q.option_d is not None: p.add_run(f"\nD. {q.option_d}")
                    if not is_single_choice and q.option_e is not None:
                        p.add_run(f"\nE. {q.option_e}")
                    
                    doc.add_paragraph() # 添加空行
                section_counter += 1

        output = BytesIO()
        doc.save(output)
        output.seek(0)
        return output