#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查questions表的列结构
"""

import sqlite3

def check_questions_columns():
    """
    检查questions表的列结构
    """
    try:
        conn = sqlite3.connect('question_bank.db')
        cursor = conn.cursor()
        
        # 获取questions表的列信息
        cursor.execute('PRAGMA table_info(questions)')
        columns = cursor.fetchall()
        
        print(f"Questions表的列结构 ({len(columns)} 个列):")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 检查是否有status字段
        column_names = [col[1] for col in columns]
        if 'status' in column_names:
            print("\n✅ status字段存在")
        else:
            print("\n❌ status字段不存在")
        
        # 检查其他关键字段
        required_fields = ['is_backup', 'backup_priority', 'last_used_date', 'usage_count']
        missing_fields = [field for field in required_fields if field not in column_names]
        
        if missing_fields:
            print(f"\n❌ 缺少字段: {', '.join(missing_fields)}")
        else:
            print("\n✅ 所有必需字段都存在")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == '__main__':
    check_questions_columns()