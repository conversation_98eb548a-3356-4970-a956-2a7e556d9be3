import sqlite3

def check_questions_table():
    conn = sqlite3.connect('question_bank.db')
    cursor = conn.cursor()
    
    # 检查questions表的列信息
    cursor.execute('PRAGMA table_info(questions)')
    columns = cursor.fetchall()
    
    print('Questions table columns:')
    for col in columns:
        print(f'  {col[1]} ({col[2]})')
    
    # 检查是否存在status字段
    status_exists = any(col[1] == 'status' for col in columns)
    print(f'\nStatus column exists: {status_exists}')
    
    conn.close()
    return status_exists

if __name__ == '__main__':
    check_questions_table()