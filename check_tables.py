#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_database():
    db_path = 'question_bank.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件 {db_path} 不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"数据库中的表 ({len(tables)} 个):")
        for table in tables:
            print(f"  - {table[0]}")
        
        # 如果有question_banks表，检查其内容
        if any('question_banks' in table for table in tables):
            print("\nquestion_banks表内容:")
            cursor.execute("SELECT * FROM question_banks")
            banks = cursor.fetchall()
            for bank in banks:
                print(f"  {bank}")
        
        conn.close()
        
    except sqlite3.Error as e:
        print(f"数据库操作失败: {e}")

if __name__ == '__main__':
    check_database()