from flask import Flask, request, render_template_string, redirect, url_for, flash, jsonify, send_file, send_from_directory
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, joinedload
from werkzeug.utils import secure_filename
from flask_wtf.csrf import CSRFProtect
import os
import pandas as pd
from io import BytesIO
import sqlite3
import mimetypes
import re
import time
from models import Base, Question, Paper, PaperQuestion, QuestionGroup, QuestionBank, DuplicateRecord, BackupRule, RotationHistory, EditHistory, SimilarityCache, QuestionUsageRecord, UsageStatistics, UsageReport
from excel_importer import import_questions_from_excel_standard as import_questions_from_excel, export_error_report, STANDARD_EXPECTED_COLUMNS as EXPECTED_COLUMNS
from excel_exporter import export_db_questions_to_excel
from paper_generator import PaperGenerator
# from duplicate_detection_api import DuplicateDetectionAPI, DetectionStatus, DuplicateAction

# 全局变量存储检测任务
detection_tasks = {}
detection_results = {}
from backup_management import BackupQuestionManager
from batch_edit_operations import BatchEditManager
from usage_statistics_api import UsageStatisticsAPI
from usage_statistics_algorithms import UsageStatisticsAlgorithms
import datetime
import json
from openpyxl import Workbook
import threading
import uuid
from docx import Document
from json_importer import import_questions_from_json

app = Flask(__name__)
# 使用固定的secret_key，避免重启后会话失效导致的错误
app.secret_key = 'phrl_question_bank_fixed_secret_key'
# 启用 CSRF 保护
csrf = CSRFProtect(app)
app.config['UPLOAD_FOLDER'] = 'uploads'
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 添加CSRF token到模板上下文
@app.context_processor
def inject_csrf_token():
    from flask_wtf.csrf import generate_csrf
    return dict(csrf_token=generate_csrf)

# 数据库配置
# 优先使用环境变量，没有则使用SQLite作为开发数据库
DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///local_dev.db')

try:
    engine = create_engine(DATABASE_URL)
    with engine.connect() as connection:
        print("Database connection successful")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
except Exception as e:
    print(f"Database connection failed: {e}")
    raise

# 文件上传白名单
ALLOWED_EXTENSIONS = {'xlsx'}
ALLOWED_MIME_TYPES = {
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
}

# 文件大小限制 (10MB)
MAX_FILE_SIZE = 10 * 1024 * 1024

def allowed_file(filename):
    """检查文件扩展名和MIME类型是否在允许列表中"""
    return ('.' in filename and 
            filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS)

def cleanup_old_files():
    """清理超过24小时的上传文件"""
    upload_folder = os.path.join(os.getcwd(), 'uploads')
    if not os.path.exists(upload_folder):
        return
    
    current_time = time.time()
    for filename in os.listdir(upload_folder):
        filepath = os.path.join(upload_folder, filename)
        if os.path.isfile(filepath):
            # 检查文件是否超过24小时
            if current_time - os.path.getmtime(filepath) > 24 * 3600:
                try:
                    os.remove(filepath)
                    print(f"已清理旧文件: {filename}")
                except Exception as e:
                    print(f"清理文件失败 {filename}: {e}")

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        return db
    except Exception as e:
        db.close()
        raise e

def close_db(db):
    """关闭数据库会话"""
    if db:
        db.close()

# SQL注入防护
def sanitize_input(input_str):
    """基本输入清理函数"""
    if not input_str:
        return ""
    # 移除潜在的SQL注入字符
    sanitized = re.sub(r'[;\'"\\]', '', input_str)
    return sanitized.strip()

# 定义内联模板
index_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库管理系统</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .flashes { 
            list-style-type: none; 
            padding: 0; 
            margin-bottom: 20px;
        }
        .flashes li { 
            margin-bottom: 10px; 
            padding: 12px; 
            border-radius: 6px; 
            border-left: 4px solid;
        }
        .flashes .error { 
            background-color: #f8d7da; 
            color: #721c24; 
            border-color: #dc3545;
        }
        .flashes .success { 
            background-color: #d4edda; 
            color: #155724; 
            border-color: #28a745;
        }
        .flashes .warning { 
            background-color: #fff3cd; 
            color: #856404; 
            border-color: #ffc107;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-info {
            background-color: #17a2b8;
        }
        .btn-info:hover {
            background-color: #138496;
        }
        .btn-primary {
            background-color: #007bff;
        }
        .btn-primary:hover {
            background-color: #0069d9;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 20px; 
            background: white;
        }
        th, td { 
            border: 1px solid #ddd; 
            padding: 12px; 
            text-align: left; 
        }
        th { 
            background-color: #f8f9fa; 
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #e9ecef;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }
        .nav-tabs .nav-link.active {
            color: #007bff;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
        .module-section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .module-title {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #343a40;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .module-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 题库管理系统</h1>
            <p>专业的题库导入和管理平台</p>
        </div>
        
        <div class="nav-tabs">
            <a href="/" class="nav-link active">🏠 首页</a>
            <a href="/papers" class="nav-link">📋 试卷管理</a>
            <a href="/import-excel" class="nav-link">📥 导入题库</a>
            <a href="/quick-generate" class="nav-link">⚡ 快速生成</a>
            <a href="/generate-paper" class="nav-link">🎯 自定义组卷</a>
            <a href="/upload-paper-rule" class="nav-link">📤 上传组卷规则</a>
            <a href="/banks" class="nav-link">📚 题库管理</a>
            <a href="/usage-statistics" class="nav-link">📊 采用率统计</a>
            <div class="nav-dropdown" style="position: relative; display: inline-block;">
                <a href="#" class="nav-link" onclick="toggleDropdown(event)">🔧 高级功能 ▼</a>
                <div class="dropdown-content" id="advancedDropdown" style="display: none; position: absolute; background-color: white; min-width: 200px; box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2); z-index: 1; border-radius: 5px; border: 1px solid #ddd; top: 100%;">
                    <a href="#" onclick="showDuplicateDetection()" style="color: black; padding: 12px 16px; text-decoration: none; display: block; border-bottom: 1px solid #eee;">🔍 重复题目检测</a>
                    <a href="#" onclick="showBackupManagement()" style="color: black; padding: 12px 16px; text-decoration: none; display: block; border-bottom: 1px solid #eee;">🔄 备用题目管理</a>
                    <a href="#" onclick="showBatchEdit()" style="color: black; padding: 12px 16px; text-decoration: none; display: block;">✏️ 批量编辑题目</a>
                </div>
            </div>
            <form method="post" action="{{ url_for('restart_system') }}" style="display: inline-block; margin-left: 20px;" 
                  onsubmit="return confirm('确定要重启系统吗？这将重新加载所有配置。')">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <button type="submit" class="nav-link" style="background: #dc3545; color: white; border: none; cursor: pointer;">🔄 系统重启</button>
            </form>
        </div>
        
        <!-- 题库管理模块 -->
        <div class="module-section">
            <h2 class="module-title">📚 题库管理</h2>
            <div class="module-actions">
                <a href="/import-excel" class="btn btn-success">📥 导入题库</a>
                <a href="{{ url_for('download_template') }}" class="btn">📋 下载题库模板</a>
                <a href="{{ url_for('handle_export_excel') }}" class="btn btn-success">📤 导出题库</a>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>题库名称</th>
                        <th>题目数量</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% if banks %}
                        {% for bank in banks %}
                            <tr>
                                <td><a href="{{ url_for('manage_bank_questions', bank_id=bank['id']) }}" style="color: #007bff; text-decoration: none;">{{ bank['name'] }}</a></td>
                                <td>{{ bank['question_count'] }}</td>
                                <td>
                                    <a href="{{ url_for('manage_bank_questions', bank_id=bank['id']) }}" class="btn btn-primary" style="padding: 4px 12px; font-size: 0.9em; margin-right: 5px;">📝 管理试题</a>
                                    <form method="post" action="{{ url_for('delete_bank', bank_id=bank['id']) }}" style="display: inline-block;" 
                                          onsubmit="return confirm('确定要删除题库【{{ bank['name'] }}】吗？此操作会同时删除该题库下所有题目！')">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-danger" style="padding: 4px 12px; font-size: 0.9em; border: none; border-radius: 4px; cursor: pointer;">🗑️ 删除</button>
                                    </form>
                                </td>
                            </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="3" style="text-align: center; color: #666;">暂无题库</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        <!-- 试卷管理模块 -->
        <div class="module-section">
            <h2 class="module-title">📝 试卷管理</h2>
            <div class="module-actions">
                <a href="/quick-generate" class="btn btn-primary">⚡ 快速生成</a>
                <a href="/generate-paper" class="btn btn-warning">🎯 自定义组卷</a>
                <a href="/upload-paper-rule" class="btn btn-info">🗂️ 上传组卷规则</a>
                <a href="/import-paper" class="btn btn-success">📥 导入试卷</a>
                <a href="/papers" class="btn">📋 查看所有试卷</a>
            </div>
        </div>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        <ul class=flashes>
        {% for category, message in messages %}
          <li class="{{ category }}">{{ message }}</li>
        {% endfor %}
        </ul>
      {% endif %}
    {% endwith %}

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">{{ total_questions }}</div>
                <div class="stat-label">总题目数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ total_papers }}</div>
                <div class="stat-label">总试卷数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ total_banks }}</div>
                <div class="stat-label">总题库数</div>
            </div>
        </div>
    </div>
    
    <!-- 题库选择模态框 -->
    <div id="bankSelectionModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);">
        <div style="background-color: #fefefe; margin: 15% auto; padding: 20px; border: none; border-radius: 10px; width: 80%; max-width: 500px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 id="modalTitle" style="margin: 0; color: #333;">选择题库</h3>
                <span onclick="closeBankModal()" style="color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
            </div>
            <div id="bankList" style="max-height: 300px; overflow-y: auto;">
                {% if banks %}
                    {% for bank in banks %}
                        <div onclick="selectBank({{ bank['id'] }}, '{{ bank['name'] }}')" style="padding: 12px; border: 1px solid #ddd; margin-bottom: 8px; border-radius: 5px; cursor: pointer; transition: background-color 0.3s;" onmouseover="this.style.backgroundColor='#f0f0f0'" onmouseout="this.style.backgroundColor='white'">
                            <strong>{{ bank['name'] }}</strong>
                            <span style="color: #666; float: right;">{{ bank['question_count'] }} 题</span>
                        </div>
                    {% endfor %}
                {% else %}
                    <div style="text-align: center; color: #666; padding: 20px;">暂无题库，请先导入题库</div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <script>
        let selectedFunction = '';
        
        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById('advancedDropdown');
            dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
        }
        
        // 点击页面其他地方关闭下拉菜单
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('advancedDropdown');
            const navDropdown = document.querySelector('.nav-dropdown');
            if (!navDropdown.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });
        
        function showDuplicateDetection() {
            selectedFunction = 'duplicate';
            document.getElementById('modalTitle').textContent = '选择题库 - 重复题目检测';
            document.getElementById('bankSelectionModal').style.display = 'block';
            document.getElementById('advancedDropdown').style.display = 'none';
        }
        
        function showBackupManagement() {
            selectedFunction = 'backup';
            document.getElementById('modalTitle').textContent = '选择题库 - 备用题目管理';
            document.getElementById('bankSelectionModal').style.display = 'block';
            document.getElementById('advancedDropdown').style.display = 'none';
        }
        
        function showBatchEdit() {
            selectedFunction = 'batch';
            document.getElementById('modalTitle').textContent = '选择题库 - 批量编辑题目';
            document.getElementById('bankSelectionModal').style.display = 'block';
            document.getElementById('advancedDropdown').style.display = 'none';
        }
        
        function selectBank(bankId, bankName) {
            let url = '';
            switch(selectedFunction) {
                case 'duplicate':
                    url = `/api/duplicate-detection/${bankId}/interface`;
                    break;
                case 'backup':
                    url = `/api/backup-management/${bankId}/interface`;
                    break;
                case 'batch':
                    url = `/api/batch-edit/${bankId}/interface`;
                    break;
            }
            if (url) {
                window.location.href = url;
            }
        }
        
        function closeBankModal() {
            document.getElementById('bankSelectionModal').style.display = 'none';
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('bankSelectionModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
"""

import_form_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导入Excel题库</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            background: #f8f9fa;
        }
        input[type="submit"] {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        input[type="submit"]:hover {
            background-color: #0056b3;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #545b62;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .info h4 {
            margin-top: 0;
            color: #0056b3;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }
        .nav-tabs .nav-link.active {
            color: #007bff;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📥 导入Excel题库</h1>
            <p>请选择要导入的Excel文件</p>
        </div>
        
        <div class="info">
            <h4>📋 文件要求：</h4>
            <ul>
                <li>文件格式：.xlsx (Excel 2007及以上版本)</li>
                <li>文件大小：不超过10MB</li>
                <li>必需列：ID, 题库名称, 题型代码, 试题（题干）, 正确答案, 难度代码</li>
            </ul>
            <p style="margin-top: 15px;">
                <strong>💡 提示：</strong>如果您不确定Excel文件格式，请先 
                <a href="{{ url_for('download_template') }}" style="color: #007bff; text-decoration: underline;">下载题库模板</a> 
                作为参考。
            </p>
        </div>
        
    <form method="post" enctype="multipart/form-data" action="{{ url_for('handle_import_excel') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <div class="form-group">
                <label for="file">选择Excel文件 (.xlsx):</label>
                <input type="file" id="file" name="file" accept=".xlsx" required>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" name="overwrite_duplicates" value="1" style="margin-right: 8px;">
                    🔄 覆盖重复题目 (如果题目ID已存在，则更新该题目)
                </label>
                <p style="font-size: 0.9em; color: #666; margin-top: 5px;">
                    💡 默认情况下，重复的题目ID会被跳过。勾选此选项将覆盖已存在的题目。
                </p>
            </div>
            <input type="submit" value="📤 上传并导入">
    </form>
        
        <div class="nav-tabs">
            <a href="{{ url_for('index') }}" class="nav-link">🏠 首页</a>
            <a href="/papers" class="nav-link">📋 试卷管理</a>
            <a href="/import-excel" class="nav-link active">📥 导入题库</a>
            <a href="/quick-generate" class="nav-link">⚡ 快速生成</a>
            <a href="/generate-paper" class="nav-link">🎯 自定义组卷</a>
            <a href="/upload-paper-rule" class="nav-link">📤 上传组卷规则</a>
            <a href="/banks" class="nav-link">📚 题库管理</a>
        </div>
    </div>
</body>
</html>
"""

banks_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>题库管理</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        h1, h2 {
            color: #333;
            margin-bottom: 20px;
        }
        a {
            color: #007bff;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        form {
            margin-bottom: 20px;
        }
        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
            margin-right: 10px;
        }
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .delete-btn {
            background-color: #dc3545;
        }
        .delete-btn:hover {
            background-color: #c82333;
        }
        .view-btn {
            background-color: #28a745;
        }
        .view-btn:hover {
            background-color: #218838;
        }
        .messages {
            margin-bottom: 20px;
        }
        .messages li {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            list-style: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }
        .nav-tabs .nav-link.active {
            color: #007bff;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>题库管理</h1>
        <div class="nav-tabs">
            <a href="{{ url_for('index') }}" class="nav-link">🏠 首页</a>
            <a href="/papers" class="nav-link">📋 试卷管理</a>
            <a href="/banks" class="nav-link active">📚 题库管理</a>
        </div>
        <div class="module-actions" style="margin-bottom: 20px;">
            <a href="/import-excel" class="btn btn-success" style="display:inline-block;padding:8px 16px;margin-right:10px;background-color:#28a745;color:white;text-decoration:none;border-radius:4px;">📥 导入题库</a>
            <a href="{{ url_for('download_template') }}" class="btn" style="display:inline-block;padding:8px 16px;margin-right:10px;background-color:#007bff;color:white;text-decoration:none;border-radius:4px;">📋 下载题库模板</a>
            <a href="{{ url_for('handle_export_excel') }}" class="btn btn-success" style="display:inline-block;padding:8px 16px;margin-right:10px;background-color:#28a745;color:white;text-decoration:none;border-radius:4px;">📤 导出题库</a>
        </div>
        <h2>新增题库</h2>
        <form method="post" action="{{ url_for('manage_banks') }}">
            <input type="text" name="bank_name" required placeholder="输入新题库名称">
            <button type="submit">创建</button>
        </form>
        <h2>现有题库</h2>
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="messages">
                    {% for category, message in messages %}
                        <li class="{{ category }}">{{ message|safe }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
        {% if banks %}
            <table>
                <tr>
                    <th>题库名称</th>
                    <th>题目数量</th>
                    <th>操作</th>
                </tr>
                {% for bank in banks %}
                <tr>
                    <td><a href="{{ url_for('manage_bank_questions', bank_id=bank['id']) }}">{{ bank['name'] }}</a></td>
                    <td>{{ bank['question_count'] }}</td>
                    <td>
                        <a href="{{ url_for('manage_bank_questions', bank_id=bank['id']) }}" class="view-btn" style="display:inline-block;margin-right:5px;">📝 管理试题</a>
                        <form method="post" action="{{ url_for('delete_bank', bank_id=bank['id']) }}" style="display:inline;margin:0;" onsubmit="return confirm('确定要删除该题库吗？此操作会同时删除该题库下所有题目！');">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <button type="submit" class="delete-btn">🗑️ 删除</button>
                        </form>
                    </td>
                </tr>
                {% endfor %}
            </table>
        {% else %}
            <p>暂无题库。</p>
        {% endif %}
    </div>
</body>
</html>
"""

# 题库试题管理模板
bank_questions_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>{{ bank.name }} - 试题管理</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        h1, h2 {
            color: #333;
            margin-bottom: 20px;
        }
        a {
            color: #007bff;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .messages {
            margin-bottom: 20px;
        }
        .messages li {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            list-style: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }
        .nav-tabs .nav-link.active {
            color: #007bff;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-right: 5px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-outline-primary {
            background-color: transparent;
            border: 1px solid #007bff;
            color: #007bff;
            text-decoration: none;
            display: inline-block;
            margin: 0 2px;
        }
        .btn-outline-primary:hover {
            background-color: #007bff;
            color: white;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        .pagination-info {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>{{ bank.name }} - 试题管理</h1>
        <div class="nav-tabs">
            <a href="{{ url_for('index') }}" class="nav-link">🏠 首页</a>
            <a href="/papers" class="nav-link">📋 试卷管理</a>
            <a href="/banks" class="nav-link">📚 题库管理</a>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="messages">
                    {% for category, message in messages %}
                        <li class="{{ category }}">{{ message|safe }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
        
        <p><a href="{{ url_for('manage_banks') }}" class="btn btn-primary">← 返回题库列表</a></p>
        
        <!-- 新功能入口 -->
        <div class="module-actions" style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #495057;">🔧 题库管理工具</h3>
            <a href="/api/duplicate-detection/{{ bank.id }}/interface" class="btn" style="background-color: #17a2b8; color: white; margin-right: 10px;">🔍 重复题目检测</a>
            <a href="/api/backup-management/{{ bank.id }}/interface" class="btn" style="background-color: #28a745; color: white; margin-right: 10px;">🔄 备用题目管理</a>
            <a href="/api/batch-edit/{{ bank.id }}/interface" class="btn" style="background-color: #ffc107; color: #212529; margin-right: 10px;">✏️ 批量编辑题目</a>
        </div>
        
        <h2>试题列表 (共 {{ pagination.total }} 道题目)</h2>
        {% if questions %}
            <div class="pagination-info" style="margin-bottom: 15px; color: #666;">
                显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} - {{ (pagination.page - 1) * pagination.per_page + questions|length }} 条，共 {{ pagination.total }} 条记录
            </div>
            <table>
                <tr>
                    <th>题目ID</th>
                    <th>题型</th>
                    <th>题干</th>
                    <th>正确答案</th>
                    <th>操作</th>
                </tr>
                {% for question in questions %}
                <tr>
                    <td>{{ question.id }}</td>
                    <td>{{ question.question_type_code }}</td>
                <td>{{ question.question_stem[:100] }}{% if question.question_stem|length > 100 %}...{% endif %}</td>
                <td>{{ question.correct_answer }}</td>
                    <td>
                        <a href="{{ url_for('view_question', bank_id=bank.id, question_id=question.id) }}" class="btn btn-primary">查看详情</a>
                    </td>
                </tr>
                {% endfor %}
            </table>
            
            <!-- 分页导航 -->
            {% if pagination.total_pages > 1 %}
            <div class="pagination" style="margin-top: 20px; text-align: center;">
                {% if pagination.has_prev %}
                    <a href="{{ url_for('manage_bank_questions', bank_id=bank.id, page=1) }}" class="btn btn-outline-primary">首页</a>
                    <a href="{{ url_for('manage_bank_questions', bank_id=bank.id, page=pagination.prev_num) }}" class="btn btn-outline-primary">上一页</a>
                {% endif %}
                
                {% for page_num in range(1, pagination.total_pages + 1) %}
                    {% if page_num == pagination.page %}
                        <span class="btn btn-primary" style="margin: 0 2px;">{{ page_num }}</span>
                    {% elif page_num <= pagination.page + 2 and page_num >= pagination.page - 2 %}
                        <a href="{{ url_for('manage_bank_questions', bank_id=bank.id, page=page_num) }}" class="btn btn-outline-primary" style="margin: 0 2px;">{{ page_num }}</a>
                    {% elif page_num == 1 or page_num == pagination.total_pages %}
                        <a href="{{ url_for('manage_bank_questions', bank_id=bank.id, page=page_num) }}" class="btn btn-outline-primary" style="margin: 0 2px;">{{ page_num }}</a>
                    {% elif page_num == pagination.page - 3 or page_num == pagination.page + 3 %}
                        <span style="margin: 0 5px;">...</span>
                    {% endif %}
                {% endfor %}
                
                {% if pagination.has_next %}
                    <a href="{{ url_for('manage_bank_questions', bank_id=bank.id, page=pagination.next_num) }}" class="btn btn-outline-primary">下一页</a>
                    <a href="{{ url_for('manage_bank_questions', bank_id=bank.id, page=pagination.total_pages) }}" class="btn btn-outline-primary">末页</a>
                {% endif %}
            </div>
            {% endif %}
        {% else %}
            <p>该题库暂无试题。</p>
        {% endif %}
    </div>
</body>
</html>
"""

# 重复题目检测界面模板
duplicate_detection_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>重复题目检测 - {{ bank.name }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-right: 5px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
        .duplicate-group {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
        }
        .question-item {
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            background-color: white;
        }
        .similarity-score {
            font-weight: bold;
            color: #dc3545;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .messages {
            margin-bottom: 20px;
        }
        .messages li {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            list-style: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
    <script>
        let taskId = null;
        let checkInterval = null;
        
        function startDetection() {
            const threshold = document.getElementById('threshold').value;
            const algorithms = Array.from(document.querySelectorAll('input[name="algorithms"]:checked')).map(cb => cb.value);
            
            if (algorithms.length === 0) {
                alert('请至少选择一种检测算法');
                return;
            }
            
            fetch('/api/duplicate-detection/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    bank_id: '{{ bank.id }}',
                    threshold: parseFloat(threshold),
                    algorithms: algorithms
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    taskId = data.task_id;
                    document.getElementById('detection-form').style.display = 'none';
                    document.getElementById('progress-section').style.display = 'block';
                    checkProgress();
                } else {
                    alert('启动检测失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('请求失败');
            });
        }
        
        function checkProgress() {
            if (!taskId) return;
            
            fetch(`/api/duplicate-detection/status/${taskId}`)
            .then(response => response.json())
            .then(data => {
                const progressBar = document.getElementById('progress-bar');
                const statusText = document.getElementById('status-text');
                
                progressBar.style.width = data.progress + '%';
                statusText.textContent = data.message;
                
                if (data.status === 'completed') {
                    clearInterval(checkInterval);
                    loadResults();
                } else if (data.status === 'failed') {
                    clearInterval(checkInterval);
                    alert('检测失败: ' + data.message);
                    resetForm();
                } else {
                    checkInterval = setTimeout(checkProgress, 2000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                clearInterval(checkInterval);
            });
        }
        
        function loadResults() {
            fetch(`/api/duplicate-detection/results/${taskId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayResults(data.results);
                    document.getElementById('progress-section').style.display = 'none';
                    document.getElementById('results-section').style.display = 'block';
                } else {
                    alert('获取结果失败: ' + data.message);
                }
            });
        }
        
        function displayResults(results) {
            const container = document.getElementById('results-container');
            container.innerHTML = '';
            
            if (results.length === 0) {
                container.innerHTML = '<p>未发现重复题目。</p>';
                return;
            }
            
            results.forEach((group, index) => {
                const groupDiv = document.createElement('div');
                groupDiv.className = 'duplicate-group';
                groupDiv.innerHTML = `
                    <h4>重复组 ${index + 1} (相似度: <span class="similarity-score">${(group.similarity * 100).toFixed(1)}%</span>)</h4>
                    <div class="group-actions" style="margin-bottom: 15px;">
                        <button class="btn btn-danger" onclick="deleteGroup(${index})">删除整组</button>
                        <button class="btn btn-warning" onclick="mergeGroup(${index})">合并题目</button>
                    </div>
                    <div class="questions">
                        ${group.questions.map((q, qIndex) => `
                            <div class="question-item">
                                <input type="checkbox" id="q_${index}_${qIndex}" value="${q.id}">
                                <label for="q_${index}_${qIndex}">
                                    <strong>ID:</strong> ${q.id}<br>
                                    <strong>题干:</strong> ${q.question_stem.substring(0, 100)}...<br>
                                    <strong>答案:</strong> ${q.correct_answer}
                                </label>
                                <div style="margin-top: 10px;">
                                    <button class="btn btn-primary" onclick="editQuestion('${q.id}')">编辑</button>
                                    <button class="btn btn-danger" onclick="deleteQuestion('${q.id}')">删除</button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
                container.appendChild(groupDiv);
            });
        }
        
        function deleteGroup(groupIndex) {
            if (confirm('确定要删除这组重复题目吗？')) {
                const checkboxes = document.querySelectorAll(`#results-container .duplicate-group:nth-child(${groupIndex + 1}) input[type="checkbox"]`);
                const questionIds = Array.from(checkboxes).map(cb => cb.value);
                
                fetch('/api/duplicate-detection/action', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'delete',
                        question_ids: questionIds
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                });
            }
        }
        
        function deleteQuestion(questionId) {
            if (confirm('确定要删除这道题目吗？')) {
                fetch('/api/duplicate-detection/action', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'delete',
                        question_ids: [questionId]
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                });
            }
        }
        
        function editQuestion(questionId) {
            window.open(`/bank/{{ bank.id }}/question/${questionId}`, '_blank');
        }
        
        function resetForm() {
            document.getElementById('detection-form').style.display = 'block';
            document.getElementById('progress-section').style.display = 'none';
            document.getElementById('results-section').style.display = 'none';
            taskId = null;
        }
        
        function cancelDetection() {
            if (taskId && confirm('确定要取消检测吗？')) {
                fetch(`/api/duplicate-detection/cancel/${taskId}`, {
                    method: 'POST'
                })
                .then(() => {
                    clearInterval(checkInterval);
                    resetForm();
                });
            }
        }
    </script>
</head>
<body>
    <div class="container">
        <h1>🔍 重复题目检测 - {{ bank.name }}</h1>
        <div class="nav-tabs">
            <a href="{{ url_for('index') }}" class="nav-link">🏠 首页</a>
            <a href="/papers" class="nav-link">📋 试卷管理</a>
            <a href="/banks" class="nav-link">📚 题库管理</a>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="messages">
                    {% for category, message in messages %}
                        <li class="{{ category }}">{{ message|safe }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
        
        <p>
            <a href="{{ url_for('manage_banks') }}" class="btn btn-primary">← 返回题库列表</a>
            <a href="{{ url_for('manage_bank_questions', bank_id=bank.id) }}" class="btn btn-primary">← 返回 {{ bank.name }}</a>
        </p>
        
        <!-- 检测配置表单 -->
        <div id="detection-form">
            <h2>检测配置</h2>
            <div class="form-group">
                <label for="threshold">相似度阈值 (%):</label>
                <input type="range" id="threshold" class="form-control" min="50" max="100" value="80" oninput="document.getElementById('threshold-value').textContent = this.value + '%'">
                <span id="threshold-value">80%</span>
            </div>
            
            <div class="form-group">
                <label>检测算法:</label>
                <div>
                    <input type="checkbox" id="edit_distance" name="algorithms" value="edit_distance" checked>
                    <label for="edit_distance">编辑距离算法</label>
                </div>
                <div>
                    <input type="checkbox" id="cosine_similarity" name="algorithms" value="cosine_similarity" checked>
                    <label for="cosine_similarity">余弦相似度算法</label>
                </div>
                <div>
                    <input type="checkbox" id="jaccard_similarity" name="algorithms" value="jaccard_similarity">
                    <label for="jaccard_similarity">Jaccard相似度算法</label>
                </div>
                <div>
                    <input type="checkbox" id="fuzzy_match" name="algorithms" value="fuzzy_match">
                    <label for="fuzzy_match">模糊匹配算法</label>
                </div>
            </div>
            
            <button class="btn btn-success" onclick="startDetection()">🚀 开始检测</button>
        </div>
        
        <!-- 进度显示 -->
        <div id="progress-section" style="display: none;">
            <h2>检测进度</h2>
            <div class="progress">
                <div id="progress-bar" class="progress-bar" style="width: 0%"></div>
            </div>
            <p id="status-text">正在初始化...</p>
            <button class="btn btn-danger" onclick="cancelDetection()">取消检测</button>
        </div>
        
        <!-- 结果显示 -->
        <div id="results-section" style="display: none;">
            <h2>检测结果</h2>
            <div style="margin-bottom: 15px;">
                <button class="btn btn-primary" onclick="resetForm()">重新检测</button>
                <button class="btn btn-success" onclick="exportResults()">导出结果</button>
            </div>
            <div id="results-container"></div>
        </div>
    </div>
</body>
</html>
"""

# 备用题目管理界面模板
backup_management_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>备用题目管理 - {{ bank.name }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-right: 5px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-info { background-color: #17a2b8; color: white; }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .status-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #f8f9fa;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
        }
        .rule-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: white;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .messages {
            margin-bottom: 20px;
        }
        .messages li {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            list-style: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .tab-nav {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab-nav button {
            padding: 10px 20px;
            border: none;
            background: none;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab-nav button.active {
            border-bottom-color: #007bff;
            color: #007bff;
        }
    </style>
    <script>
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有标签页按钮的激活状态
            document.querySelectorAll('.tab-nav button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            document.querySelector(`[onclick="showTab('${tabName}')"]`).classList.add('active');
        }
        
        function loadStatus() {
            fetch(`/api/backup-management/status/{{ bank.id }}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStatusDisplay(data.status);
                }
            });
        }
        
        function updateStatusDisplay(status) {
            document.getElementById('total-questions').textContent = status.total_questions;
            document.getElementById('active-questions').textContent = status.active_questions;
            document.getElementById('backup-questions').textContent = status.backup_questions;
            document.getElementById('archived-questions').textContent = status.archived_questions;
        }
        
        function createRule() {
            const formData = {
                bank_id: '{{ bank.id }}',
                standard_count: parseInt(document.getElementById('standard_count').value),
                rotation_percentage: parseFloat(document.getElementById('rotation_percentage').value),
                rotation_strategy: document.getElementById('rotation_strategy').value,
                rotation_interval: parseInt(document.getElementById('rotation_interval').value)
            };
            
            fetch('/api/backup-management/rules', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('轮换规则创建成功');
                    loadRules();
                    document.getElementById('rule-form').reset();
                } else {
                    alert('创建失败: ' + data.message);
                }
            });
        }
        
        function loadRules() {
            fetch(`/api/backup-management/rules?bank_id={{ bank.id }}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayRules(data.rules);
                }
            });
        }
        
        function displayRules(rules) {
            const container = document.getElementById('rules-container');
            container.innerHTML = '';
            
            if (rules.length === 0) {
                container.innerHTML = '<p>暂无轮换规则。</p>';
                return;
            }
            
            rules.forEach(rule => {
                const ruleDiv = document.createElement('div');
                ruleDiv.className = 'rule-card';
                ruleDiv.innerHTML = `
                    <h4>规则 #${rule.id}</h4>
                    <p><strong>标准题目数量:</strong> ${rule.standard_count}</p>
                    <p><strong>轮换比例:</strong> ${rule.rotation_percentage}%</p>
                    <p><strong>轮换策略:</strong> ${getStrategyName(rule.rotation_strategy)}</p>
                    <p><strong>轮换间隔:</strong> ${rule.rotation_interval} 天</p>
                    <p><strong>状态:</strong> ${rule.is_active ? '激活' : '停用'}</p>
                    <p><strong>创建时间:</strong> ${new Date(rule.created_at).toLocaleString()}</p>
                    <div>
                        <button class="btn btn-success" onclick="executeRule(${rule.id})">执行轮换</button>
                        <button class="btn btn-warning" onclick="toggleRule(${rule.id}, ${!rule.is_active})">${rule.is_active ? '停用' : '激活'}</button>
                        <button class="btn btn-danger" onclick="deleteRule(${rule.id})">删除</button>
                    </div>
                `;
                container.appendChild(ruleDiv);
            });
        }
        
        function getStrategyName(strategy) {
            const strategies = {
                'random': '随机轮换',
                'difficulty_based': '基于难度',
                'time_based': '基于时间',
                'smart': '智能轮换'
            };
            return strategies[strategy] || strategy;
        }
        
        function executeRule(ruleId) {
            if (confirm('确定要执行这个轮换规则吗？')) {
                fetch(`/api/backup-management/execute/${ruleId}`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`轮换执行成功！轮换了 ${data.rotated_count} 道题目`);
                        loadStatus();
                        loadHistory();
                    } else {
                        alert('执行失败: ' + data.message);
                    }
                });
            }
        }
        
        function toggleRule(ruleId, activate) {
            fetch(`/api/backup-management/rules`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    rule_id: ruleId,
                    is_active: activate
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(activate ? '规则已激活' : '规则已停用');
                    loadRules();
                } else {
                    alert('操作失败: ' + data.message);
                }
            });
        }
        
        function deleteRule(ruleId) {
            if (confirm('确定要删除这个轮换规则吗？')) {
                fetch(`/api/backup-management/rules`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        rule_id: ruleId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('规则删除成功');
                        loadRules();
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                });
            }
        }
        
        function loadHistory() {
            fetch(`/api/backup-management/history?bank_id={{ bank.id }}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayHistory(data.history);
                }
            });
        }
        
        function displayHistory(history) {
            const container = document.getElementById('history-container');
            container.innerHTML = '';
            
            if (history.length === 0) {
                container.innerHTML = '<p>暂无轮换历史。</p>';
                return;
            }
            
            history.forEach(record => {
                const recordDiv = document.createElement('div');
                recordDiv.className = 'rule-card';
                recordDiv.innerHTML = `
                    <h4>轮换记录 #${record.id}</h4>
                    <p><strong>执行时间:</strong> ${new Date(record.executed_at).toLocaleString()}</p>
                    <p><strong>轮换策略:</strong> ${getStrategyName(record.rotation_strategy)}</p>
                    <p><strong>轮换数量:</strong> ${record.rotated_count} 道题目</p>
                    <p><strong>详情:</strong> ${record.details}</p>
                `;
                container.appendChild(recordDiv);
            });
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            showTab('status');
            loadStatus();
            loadRules();
            loadHistory();
        });
    </script>
</head>
<body>
    <div class="container">
        <h1>🔄 备用题目管理 - {{ bank.name }}</h1>
        <div class="nav-tabs">
            <a href="{{ url_for('index') }}" class="nav-link">🏠 首页</a>
            <a href="/papers" class="nav-link">📋 试卷管理</a>
            <a href="/banks" class="nav-link">📚 题库管理</a>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="messages">
                    {% for category, message in messages %}
                        <li class="{{ category }}">{{ message|safe }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
        
        <p>
            <a href="{{ url_for('manage_banks') }}" class="btn btn-primary">← 返回题库列表</a>
            <a href="{{ url_for('manage_bank_questions', bank_id=bank.id) }}" class="btn btn-primary">← 返回 {{ bank.name }}</a>
        </p>
        
        <!-- 标签页导航 -->
        <div class="tab-nav">
            <button onclick="showTab('status')" class="active">📊 状态概览</button>
            <button onclick="showTab('rules')">⚙️ 轮换规则</button>
            <button onclick="showTab('history')">📋 轮换历史</button>
        </div>
        
        <!-- 状态概览标签页 -->
        <div id="status" class="tab-content active">
            <h2>题库状态概览</h2>
            <div class="status-card">
                <div class="status-item">
                    <span><strong>总题目数量:</strong></span>
                    <span id="total-questions">-</span>
                </div>
                <div class="status-item">
                    <span><strong>激活题目数量:</strong></span>
                    <span id="active-questions">-</span>
                </div>
                <div class="status-item">
                    <span><strong>备用题目数量:</strong></span>
                    <span id="backup-questions">-</span>
                </div>
                <div class="status-item">
                    <span><strong>归档题目数量:</strong></span>
                    <span id="archived-questions">-</span>
                </div>
            </div>
        </div>
        
        <!-- 轮换规则标签页 -->
        <div id="rules" class="tab-content">
            <h2>轮换规则管理</h2>
            
            <!-- 创建新规则表单 -->
            <div class="status-card">
                <h3>创建新轮换规则</h3>
                <form id="rule-form" onsubmit="event.preventDefault(); createRule();">
                    <div class="form-group">
                        <label for="standard_count">标准题目数量:</label>
                        <input type="number" id="standard_count" class="form-control" min="1" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="rotation_percentage">轮换比例 (%):</label>
                        <input type="number" id="rotation_percentage" class="form-control" min="1" max="100" step="0.1" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="rotation_strategy">轮换策略:</label>
                        <select id="rotation_strategy" class="form-control" required>
                            <option value="random">随机轮换</option>
                            <option value="difficulty_based">基于难度</option>
                            <option value="time_based">基于时间</option>
                            <option value="smart">智能轮换</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="rotation_interval">轮换间隔 (天):</label>
                        <input type="number" id="rotation_interval" class="form-control" min="1" required>
                    </div>
                    
                    <button type="submit" class="btn btn-success">创建规则</button>
                </form>
            </div>
            
            <!-- 现有规则列表 -->
            <h3>现有轮换规则</h3>
            <div id="rules-container"></div>
        </div>
        
        <!-- 轮换历史标签页 -->
        <div id="history" class="tab-content">
            <h2>轮换历史记录</h2>
            <div id="history-container"></div>
        </div>
    </div>
</body>
</html>
"""

# 批量编辑界面模板
batch_edit_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>批量编辑题目 - {{ bank.name }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-right: 5px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-info { background-color: #17a2b8; color: white; }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .question-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: white;
        }
        .question-card.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }
        .option-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            padding: 8px;
            border: 1px solid #eee;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .option-item.correct {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .option-letter {
            font-weight: bold;
            margin-right: 10px;
            min-width: 20px;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .messages {
            margin-bottom: 20px;
        }
        .messages li {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            list-style: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .tab-nav {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab-nav button {
            padding: 10px 20px;
            border: none;
            background: none;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab-nav button.active {
            border-bottom-color: #007bff;
            color: #007bff;
        }
        .preview-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            background-color: #f8f9fa;
        }
        .change-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            background-color: white;
        }
        .change-item.add {
            border-left: 4px solid #28a745;
        }
        .change-item.modify {
            border-left: 4px solid #ffc107;
        }
        .change-item.delete {
            border-left: 4px solid #dc3545;
        }
        .operation-panel {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #f8f9fa;
        }
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        .checkbox-group label {
            display: flex;
            align-items: center;
            margin-bottom: 0;
        }
        .checkbox-group input[type="checkbox"] {
            margin-right: 5px;
        }
    </style>
    <script>
        let selectedQuestions = new Set();
        let currentSession = null;
        let previewData = null;
        
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有标签页按钮的激活状态
            document.querySelectorAll('.tab-nav button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            document.querySelector(`[onclick="showTab('${tabName}')"]`).classList.add('active');
        }
        
        function toggleQuestionSelection(questionId) {
            const card = document.getElementById(`question-${questionId}`);
            const checkbox = document.getElementById(`checkbox-${questionId}`);
            
            if (selectedQuestions.has(questionId)) {
                selectedQuestions.delete(questionId);
                card.classList.remove('selected');
                checkbox.checked = false;
            } else {
                selectedQuestions.add(questionId);
                card.classList.add('selected');
                checkbox.checked = true;
            }
            
            updateSelectionCount();
        }
        
        function selectAllQuestions() {
            const checkboxes = document.querySelectorAll('input[name="question_ids"]');
            checkboxes.forEach(checkbox => {
                const questionId = parseInt(checkbox.value);
                selectedQuestions.add(questionId);
                checkbox.checked = true;
                document.getElementById(`question-${questionId}`).classList.add('selected');
            });
            updateSelectionCount();
        }
        
        function clearSelection() {
            selectedQuestions.clear();
            document.querySelectorAll('input[name="question_ids"]').forEach(checkbox => {
                checkbox.checked = false;
            });
            document.querySelectorAll('.question-card').forEach(card => {
                card.classList.remove('selected');
            });
            updateSelectionCount();
        }
        
        function updateSelectionCount() {
            document.getElementById('selection-count').textContent = selectedQuestions.size;
        }
        
        function createEditSession() {
            if (selectedQuestions.size === 0) {
                alert('请先选择要编辑的题目');
                return;
            }
            
            const questionIds = Array.from(selectedQuestions);
            
            fetch('/api/batch-edit/session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    bank_id: {{ bank.id }},
                    question_ids: questionIds
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentSession = data.session_id;
                    alert(`编辑会话创建成功！会话ID: ${currentSession}`);
                    showTab('operations');
                } else {
                    alert('创建会话失败: ' + data.message);
                }
            });
        }
        
        function previewChanges() {
            if (!currentSession) {
                alert('请先创建编辑会话');
                return;
            }
            
            const operation = document.getElementById('operation_type').value;
            const params = getOperationParams(operation);
            
            fetch('/api/batch-edit/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_id: currentSession,
                    operation: operation,
                    params: params
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    previewData = data;
                    displayPreview(data.preview);
                    showTab('preview');
                } else {
                    alert('预览失败: ' + data.message);
                }
            });
        }
        
        function getOperationParams(operation) {
            const params = {};
            
            switch (operation) {
                case 'reorder':
                    params.order = document.getElementById('reorder_type').value;
                    if (params.order === 'custom') {
                        params.custom_order = document.getElementById('custom_order').value.split(',').map(s => s.trim());
                    }
                    break;
                case 'shuffle':
                    // 随机打乱不需要额外参数
                    break;
                case 'swap':
                    params.position1 = parseInt(document.getElementById('swap_pos1').value);
                    params.position2 = parseInt(document.getElementById('swap_pos2').value);
                    break;
                case 'reverse':
                    // 反转不需要额外参数
                    break;
                case 'format':
                    params.format_type = document.getElementById('format_type').value;
                    break;
                case 'replace_text':
                    params.search_text = document.getElementById('search_text').value;
                    params.replace_text = document.getElementById('replace_text').value;
                    params.target_field = document.getElementById('target_field').value;
                    break;
                case 'update_difficulty':
                    params.new_difficulty = parseInt(document.getElementById('new_difficulty').value);
                    break;
            }
            
            return params;
        }
        
        function displayPreview(preview) {
            const container = document.getElementById('preview-container');
            container.innerHTML = '';
            
            if (preview.length === 0) {
                container.innerHTML = '<p>没有检测到变更。</p>';
                return;
            }
            
            preview.forEach(change => {
                const changeDiv = document.createElement('div');
                changeDiv.className = `change-item ${change.change_type}`;
                changeDiv.innerHTML = `
                    <h4>题目 #${change.question_id}</h4>
                    <p><strong>变更类型:</strong> ${getChangeTypeName(change.change_type)}</p>
                    <p><strong>字段:</strong> ${change.field}</p>
                    <p><strong>原值:</strong> ${change.old_value || '无'}</p>
                    <p><strong>新值:</strong> ${change.new_value || '无'}</p>
                `;
                container.appendChild(changeDiv);
            });
        }
        
        function getChangeTypeName(type) {
            const types = {
                'add': '新增',
                'modify': '修改',
                'delete': '删除'
            };
            return types[type] || type;
        }
        
        function applyChanges() {
            if (!previewData) {
                alert('请先预览变更');
                return;
            }
            
            if (confirm('确定要应用这些变更吗？此操作不可撤销。')) {
                fetch('/api/batch-edit/apply', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        session_id: currentSession
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`变更应用成功！共修改了 ${data.modified_count} 道题目`);
                        location.reload(); // 重新加载页面以显示最新数据
                    } else {
                        alert('应用变更失败: ' + data.message);
                    }
                });
            }
        }
        
        function showOperationParams(operation) {
            // 隐藏所有参数区域
            document.querySelectorAll('.operation-params').forEach(div => {
                div.style.display = 'none';
            });
            
            // 显示选中操作的参数区域
            const paramsDiv = document.getElementById(`params-${operation}`);
            if (paramsDiv) {
                paramsDiv.style.display = 'block';
            }
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            showTab('selection');
            updateSelectionCount();
        });
    </script>
</head>
<body>
    <div class="container">
        <h1>✏️ 批量编辑题目 - {{ bank.name }}</h1>
        <div class="nav-tabs">
            <a href="{{ url_for('index') }}" class="nav-link">🏠 首页</a>
            <a href="/papers" class="nav-link">📋 试卷管理</a>
            <a href="/banks" class="nav-link">📚 题库管理</a>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="messages">
                    {% for category, message in messages %}
                        <li class="{{ category }}">{{ message|safe }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
        
        <p>
            <a href="{{ url_for('manage_banks') }}" class="btn btn-primary">← 返回题库列表</a>
            <a href="{{ url_for('manage_bank_questions', bank_id=bank.id) }}" class="btn btn-primary">← 返回 {{ bank.name }}</a>
        </p>
        
        <!-- 标签页导航 -->
        <div class="tab-nav">
            <button onclick="showTab('selection')" class="active">📋 选择题目</button>
            <button onclick="showTab('operations')">⚙️ 编辑操作</button>
            <button onclick="showTab('preview')">👁️ 预览变更</button>
        </div>
        
        <!-- 选择题目标签页 -->
        <div id="selection" class="tab-content active">
            <h2>选择要编辑的题目</h2>
            <div class="operation-panel">
                <p>已选择 <span id="selection-count">0</span> 道题目</p>
                <button class="btn btn-success" onclick="selectAllQuestions()">全选</button>
                <button class="btn btn-warning" onclick="clearSelection()">清空选择</button>
                <button class="btn btn-primary" onclick="createEditSession()">创建编辑会话</button>
            </div>
            
            <div class="questions-list">
                {% for question in questions %}
                <div class="question-card" id="question-{{ question.id }}" onclick="toggleQuestionSelection({{ question.id }})">
                    <div class="checkbox-group">
                        <label>
                            <input type="checkbox" name="question_ids" value="{{ question.id }}" id="checkbox-{{ question.id }}">
                            题目 #{{ question.id }}
                        </label>
                    </div>
                    <h4>{{ question.question_stem[:100] }}{% if question.question_stem|length > 100 %}...{% endif %}</h4>
                    <p><strong>题型:</strong> {{ question.question_type_code }}</p>
                    
                    {% if question.question_type_code in ['单选题', '多选题'] %}
                    <div class="options">
                        {% set options = question.options.split('|') %}
                        {% for option in options %}
                            {% set option_letter = ['A', 'B', 'C', 'D', 'E', 'F'][loop.index0] %}
                            <div class="option-item {% if option_letter in question.correct_answer %}correct{% endif %}">
                                <span class="option-letter">{{ option_letter }}.</span>
                                <span>{{ option }}</span>
                            </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <p><strong>正确答案:</strong> {{ question.correct_answer }}</p>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- 编辑操作标签页 -->
        <div id="operations" class="tab-content">
            <h2>选择编辑操作</h2>
            <div class="operation-panel">
                <div class="form-group">
                    <label for="operation_type">操作类型:</label>
                    <select id="operation_type" class="form-control" onchange="showOperationParams(this.value)">
                        <option value="reorder">重新排序选项</option>
                        <option value="shuffle">随机打乱选项</option>
                        <option value="swap">交换选项位置</option>
                        <option value="reverse">反转选项顺序</option>
                        <option value="format">格式化选项</option>
                        <option value="replace_text">替换文本</option>
                        <option value="update_difficulty">更新难度</option>
                    </select>
                </div>
                
                <!-- 重新排序参数 -->
                <div id="params-reorder" class="operation-params">
                    <div class="form-group">
                        <label for="reorder_type">排序方式:</label>
                        <select id="reorder_type" class="form-control">
                            <option value="alphabetical">按字母顺序</option>
                            <option value="length">按长度排序</option>
                            <option value="custom">自定义顺序</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="custom_order">自定义顺序 (用逗号分隔，如: A,C,B,D):</label>
                        <input type="text" id="custom_order" class="form-control" placeholder="A,C,B,D">
                    </div>
                </div>
                
                <!-- 交换位置参数 -->
                <div id="params-swap" class="operation-params" style="display: none;">
                    <div class="form-group">
                        <label for="swap_pos1">位置1 (1-6):</label>
                        <input type="number" id="swap_pos1" class="form-control" min="1" max="6">
                    </div>
                    <div class="form-group">
                        <label for="swap_pos2">位置2 (1-6):</label>
                        <input type="number" id="swap_pos2" class="form-control" min="1" max="6">
                    </div>
                </div>
                
                <!-- 格式化参数 -->
                <div id="params-format" class="operation-params" style="display: none;">
                    <div class="form-group">
                        <label for="format_type">格式化类型:</label>
                        <select id="format_type" class="form-control">
                            <option value="trim">去除首尾空格</option>
                            <option value="capitalize">首字母大写</option>
                            <option value="lowercase">转为小写</option>
                            <option value="uppercase">转为大写</option>
                        </select>
                    </div>
                </div>
                
                <!-- 替换文本参数 -->
                <div id="params-replace_text" class="operation-params" style="display: none;">
                    <div class="form-group">
                        <label for="target_field">目标字段:</label>
                        <select id="target_field" class="form-control">
                            <option value="question_stem">题干</option>
                            <option value="options">选项</option>
                            <option value="explanation">解析</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="search_text">查找文本:</label>
                        <input type="text" id="search_text" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="replace_text">替换为:</label>
                        <input type="text" id="replace_text" class="form-control">
                    </div>
                </div>
                
                <!-- 更新难度参数 -->
                <div id="params-update_difficulty" class="operation-params" style="display: none;">
                    <div class="form-group">
                        <label for="new_difficulty">新难度 (1-5):</label>
                        <input type="number" id="new_difficulty" class="form-control" min="1" max="5">
                    </div>
                </div>
                
                <button class="btn btn-info" onclick="previewChanges()">预览变更</button>
            </div>
        </div>
        
        <!-- 预览变更标签页 -->
        <div id="preview" class="tab-content">
            <h2>预览变更</h2>
            <div class="preview-section">
                <div id="preview-container">
                    <p>请先在编辑操作页面预览变更。</p>
                </div>
                <div style="margin-top: 20px;">
                    <button class="btn btn-success" onclick="applyChanges()">应用变更</button>
                    <button class="btn btn-warning" onclick="showTab('operations')">返回编辑</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
"""

# 试题详情模板
question_detail_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>试题详情 - {{ question.id }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        h1, h2 {
            color: #333;
            margin-bottom: 20px;
        }
        a {
            color: #007bff;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        .question-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .question-info p {
            margin: 10px 0;
        }
        .question-info strong {
            color: #495057;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-right: 5px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>试题详情</h1>
        <div class="nav-tabs">
            <a href="{{ url_for('index') }}" class="nav-link">🏠 首页</a>
            <a href="/papers" class="nav-link">📋 试卷管理</a>
            <a href="/banks" class="nav-link">📚 题库管理</a>
        </div>
        
        <p>
            <a href="{{ url_for('manage_banks') }}" class="btn btn-primary">← 返回题库列表</a>
            <a href="{{ url_for('manage_bank_questions', bank_id=bank.id) }}" class="btn btn-primary">← 返回 {{ bank.name }}</a>
        </p>
        
        <div class="question-info">
            <h2>{{ question.id }}</h2>
            <p><strong>题库：</strong>{{ bank.name }}</p>
            <p><strong>题型代码：</strong>{{ question.question_type_code }}</p>
            <p><strong>题干：</strong>{{ question.question_stem }}</p>
            {% if question.option_a %}
                <p><strong>选项A：</strong>{{ question.option_a }}</p>
            {% endif %}
            {% if question.option_b %}
                <p><strong>选项B：</strong>{{ question.option_b }}</p>
            {% endif %}
            {% if question.option_c %}
                <p><strong>选项C：</strong>{{ question.option_c }}</p>
            {% endif %}
            {% if question.option_d %}
                <p><strong>选项D：</strong>{{ question.option_d }}</p>
            {% endif %}
            {% if question.option_e %}
                <p><strong>选项E：</strong>{{ question.option_e }}</p>
            {% endif %}
            <p><strong>正确答案：</strong>{{ question.correct_answer }}</p>
            {% if question.analysis %}
                <p><strong>解析：</strong>{{ question.analysis }}</p>
            {% endif %}
            {% if question.difficulty_code %}
                <p><strong>难度代码：</strong>{{ question.difficulty_code }}</p>
            {% endif %}
            {% if question.image_location %}
                <p><strong>图片信息：</strong>{{ question.image_location }}</p>
            {% endif %}
        </div>
    </div>
</body>
</html>
"""

@app.route('/', methods=['GET'])
def index():
    """主页，显示题库统计和题库列表"""
    db = get_db()
    try:
        # 获取基本统计信息
        total_questions = db.query(Question).count()
        total_papers = db.query(Paper).count()
        total_banks = db.query(QuestionBank).count()
        
        # 获取题库列表及其统计信息
        banks = []
        for bank in db.query(QuestionBank).order_by(QuestionBank.题库名称).all():
            # 获取该题库的题目数量
            question_count = db.query(Question).filter(Question.question_bank_id == bank.id).count()
            
            # 获取该题库的题型种类
            question_types = db.query(Question.question_type_code).filter(
                Question.question_bank_id == bank.id
            ).distinct().all()
            question_types = [qt[0] for qt in question_types]
            
            banks.append({
                'id': bank.id,
                'name': bank.题库名称,
                'question_count': question_count,
                'question_types': question_types
            })
        
        return render_template_string(
            index_template, 
            total_questions=total_questions,
            total_papers=total_papers,
            total_banks=total_banks,
            banks=banks
        )
    except Exception as e:
        flash(f"加载页面失败：{e}", "error")
        return render_template_string(
            index_template,
            total_questions=0,
            total_papers=0,
            total_banks=0,
            banks=[]
        )
    finally:
        close_db(db)

@app.route('/import-json', methods=['GET'])
def handle_import_json():
    """处理从JSON文件导入样例题库的请求"""
    db = get_db()
    json_file_path = os.path.join(os.path.dirname(__file__), 'questions_sample.json')
    
    if not os.path.exists(json_file_path):
        flash(f"错误：样例题库文件 'questions_sample.json' 不存在。", 'error')
        return redirect(url_for('index'))
    
    try:
        success_count, fail_count = import_questions_from_json(json_file_path, db)
        if success_count > 0:
            flash(f"成功导入 {success_count} 道新的样例题目！", 'success')
        else:
            flash("没有新的样例题目需要导入，或所有题目ID已存在。", 'warning')
        if fail_count > 0:
            flash(f"有 {fail_count} 道题目导入失败，请检查服务器日志。", 'error')

    except Exception as e:
        flash(f"导入过程中发生未知错误: {e}", 'error')
    finally:
        close_db(db)
        
    return redirect(url_for('index'))

@app.route('/import-sample', methods=['GET'])
def handle_import_sample():
    """重定向到导入Excel页面，因为功能已整合"""
    return redirect(url_for('handle_import_excel'))


@app.route('/import-excel', methods=['GET', 'POST'])
def handle_import_excel():
    """处理Excel导入"""
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('没有文件部分', 'error')
            return redirect(request.url)
        file = request.files['file']
        if not file or not file.filename:
            flash('未选择文件', 'warning')
            return redirect(request.url)
        
        if allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            
            # 获取覆盖选项
            overwrite_duplicates = request.form.get('overwrite_duplicates') == '1'
            
            db_session = get_db()
            try:
                file.save(filepath)
                # 现在这个函数会直接处理数据库提交
                questions_added, errors = import_questions_from_excel(filepath, db_session, overwrite_duplicates=overwrite_duplicates)
                
                if errors:
                    error_report_path = export_error_report(errors, filename)
                    error_link = f'<a href="/download_error_report/{os.path.basename(error_report_path)}" target="_blank">点击查看报告</a>'
                    if questions_added:
                        flash(f'成功导入 {len(questions_added)} 条题目，但有部分数据出错。{error_link}', 'warning')
                    else:
                        flash(f'导入失败，所有条目均有问题。{error_link}', 'error')

                elif questions_added:
                    flash(f'成功导入 {len(questions_added)} 条题目！', 'success')
                else:
                    flash('未在文件中找到可导入的新题目。', 'info')
                
            except Exception as e:
                # 现在的导入函数会自己回滚，这里主要捕获文件保存等其他错误
                flash(f'处理文件时发生严重错误: {e}', 'error')
            finally:
                close_db(db_session)
            
            return redirect(url_for('index'))

    return render_template_string(import_form_template)

@app.route('/download-template', methods=['GET'])
def download_template():
    """下载题库模板"""
    try:
        # 检查模板文件是否存在
        template_path = os.path.join(os.getcwd(), 'templates', '题库模板.xlsx')
        
        if not os.path.exists(template_path):
            # 如果模板文件不存在，则生成一个
            from create_template import create_question_bank_template
            template_path = create_question_bank_template()
        
        # 返回文件
        return send_file(
            template_path,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='题库模板.xlsx'
        )
        
    except Exception as e:
        flash(f'下载模板文件失败: {e}', 'error')
        return redirect(url_for('index'))

@app.route('/download_error_report/<filename>')
def download_error_report(filename):
    """下载错误报告"""
    if not filename:
        flash("无效的文件名。", "error")
        return redirect(url_for('index'))
    try:
        # 安全地构建文件路径
        safe_filename = secure_filename(filename)
        # 错误报告存储在error_reports目录
        error_reports_dir = os.path.join(os.getcwd(), 'error_reports')
        return send_from_directory(error_reports_dir, safe_filename, as_attachment=True)
    except FileNotFoundError:
        flash("错误报告文件未找到。", "error")
        return redirect(url_for('index'))
    except Exception as e:
        flash(f"下载错误报告失败: {e}", "error")
        return redirect(url_for('index'))

# 组卷功能路由
@app.route('/papers')
def papers():
    """试卷列表页面"""
    db_session = None
    papers_list = []
    
    try:
        db_session = get_db()
        papers_list = db_session.query(Paper).order_by(Paper.name).all()
    except Exception as e:
        flash(f"获取试卷列表失败: {e}", "error")
    finally:
        close_db(db_session)
    
    return render_template_string(papers_template, papers=papers_list)

@app.route('/generate-paper', methods=['GET', 'POST'])
def generate_paper():
    """生成试卷页面"""
    if request.method == 'POST':
        try:
            db_session = get_db()
            generator = PaperGenerator(db_session)
            
            # 获取表单数据
            paper_name = request.form.get('paper_name', '').strip()
            paper_description = request.form.get('paper_description', '').strip()
            total_score = float(request.form.get('total_score', 100))
            duration = int(request.form.get('duration', 120))
            difficulty_level = request.form.get('difficulty_level', '中等')
            
            # 验证必填字段
            if not paper_name:
                flash("试卷名称不能为空", "error")
                return redirect(url_for('generate_paper'))
            
            # 获取组卷规则
            rules = []
            rule_count = int(request.form.get('rule_count', 0))
            
            for i in range(rule_count):
                question_type = request.form.get(f'rule_{i}_type')
                difficulty = request.form.get(f'rule_{i}_difficulty')
                count = int(request.form.get(f'rule_{i}_count', 1))
                score = float(request.form.get(f'rule_{i}_score', 5.0))
                section_name = request.form.get(f'rule_{i}_section', '')
                
                if question_type and difficulty and count > 0:
                    rules.append({
                        'question_type': question_type,
                        'difficulty': difficulty,
                        'count': count,
                        'score_per_question': score,
                        'section_name': section_name
                    })
            
            # 生成试卷
            if rules:
                paper = generator.generate_paper_by_rules(
                    paper_name=paper_name,
                    paper_description=paper_description,
                    total_score=total_score,
                    duration=duration,
                    difficulty_level=difficulty_level,
                    rules=rules
                )
                flash(f"试卷 '{paper.name}' 生成成功！", "success")
                return redirect(url_for('view_paper', paper_id=paper.id))
            else:
                flash("请至少添加一条组卷规则", "error")
                
        except ValueError as e:
            flash(f"参数错误: {e}", "error")
        except Exception as e:
            flash(f"生成试卷失败: {e}", "error")
        finally:
            close_db(db_session)
    
    return render_template_string(generate_paper_template)

@app.route('/paper/<paper_id>')
def view_paper(paper_id):
    """查看试卷详情"""
    db_session = None
    paper = None
    paper_questions = []
    stats = {}
    
    try:
        db_session = get_db()
        paper = db_session.query(Paper).filter(Paper.id == paper_id).first()
        
        if not paper:
            flash("试卷不存在", "error")
            return redirect(url_for('papers'))
        
        # 获取试卷题目
        paper_questions = db_session.query(PaperQuestion).filter(
            PaperQuestion.paper_id == paper_id
        ).order_by(PaperQuestion.question_order).all()
        
        # 获取统计信息
        generator = PaperGenerator(db_session)
        stats = generator.get_paper_statistics(paper_id)
        
    except Exception as e:
        flash(f"获取试卷详情失败: {e}", "error")
    finally:
        close_db(db_session)
    
    return render_template_string(
        view_paper_template, 
        paper=paper, 
        paper_questions=paper_questions,
        stats=stats
    )

@app.route('/paper/<paper_id>/level3_code_stats')
def paper_level3_code_stats(paper_id):
    """显示试卷的三级代码统计信息"""
    db_session = None
    paper = None
    level3_stats = {}
    
    try:
        db_session = get_db()
        paper = db_session.query(Paper).filter(Paper.id == paper_id).first()
        
        if not paper:
            flash("试卷不存在", "error")
            return redirect(url_for('papers'))
        
        # 获取三级代码统计信息
        generator = PaperGenerator(db_session)
        level3_stats = generator.get_level3_code_statistics(paper_id)
        
    except Exception as e:
        flash(f"获取三级代码统计失败: {e}", "error")
    finally:
        close_db(db_session)
    
    # 尝试从最近的组卷规则文件中读取知识点分布配置
    knowledge_distribution = {}
    try:
        import pandas as pd
        import glob
        # 查找最近的组卷规则文件（包括uploads和templates文件夹），排除临时文件
        rule_files = [f for f in glob.glob('uploads/*.xlsx') + glob.glob('templates/*.xlsx') if not os.path.basename(f).startswith('~$')]
        if rule_files:
            # 优先选择组卷规则模板文件
            template_file = None
            for f in rule_files:
                if '组卷规则模板.xlsx' in f:
                    template_file = f
                    break
            latest_file = template_file if template_file else max(rule_files, key=os.path.getctime)
            try:
                df2 = pd.read_excel(latest_file, sheet_name='知识点分布', dtype=str, engine='openpyxl')
                # 处理列名
                df2.columns = [col.split('\n')[0].strip().replace(' ', '').replace('　', '') if isinstance(col, str) and '\n' in col else str(col).strip().replace(' ', '').replace('　', '') for col in df2.columns]
                
                # 标准化列名
                header_map = {
                    '1级代码': ['1级代码', '一级代码', '1级 代码'],
                    '1级比重(%)': ['1级比重(%)', '一级比重(%)', '1级比重%', '一级比重%'],
                    '2级代码': ['2级代码', '二级代码', '2级 代码'],
                    '2级比重(%)': ['2级比重(%)', '二级比重(%)', '2级比重%', '二级比重%'],
                    '3级代码': ['3级代码', '三级代码', '3级 代码'],
                    '3级比重(%)': ['3级比重(%)', '三级比重(%)', '3级比重%', '三级比重%'],
                }
                
                new_columns = {}
                for std_col, aliases in header_map.items():
                    for col in df2.columns:
                        if col in aliases:
                            new_columns[col] = std_col
                df2 = df2.rename(columns=new_columns)
                
                if not df2.empty:
                    for _, row in df2.iterrows():
                        if row.isnull().all():
                            continue
                        
                        # 安全地获取各级代码，处理nan值
                        try:
                            l3_code_raw = str(row.get('3级代码', '')).strip()
                            l3r = row.get('3级比重(%)', 0)
                            
                            # 跳过表头行
                            if l3_code_raw in ['Level 3 Code', '3级代码', 'Level3Code']:
                                continue
                            
                            # 检查三级代码和比重的有效性
                            if (l3_code_raw and l3_code_raw != 'nan' and l3_code_raw != '' and
                                l3r and not pd.isna(l3r)):
                                
                                try:
                                    l3r_float = float(l3r)
                                    if l3r_float > 0:
                                        # 如果三级代码已经是完整格式（如'A-A-A'），直接使用
                                        # 如果不是，则需要从其他列构建
                                        if '-' in l3_code_raw and len(l3_code_raw.split('-')) == 3:
                                            # 已经是完整的三级代码格式
                                            full_l3_code = l3_code_raw
                                        else:
                                            # 尝试从各级代码列构建
                                            l1 = str(row.get('1级代码', '')).strip()
                                            l2 = str(row.get('2级代码', '')).strip()
                                            l3 = l3_code_raw
                                            
                                            if (l1 and l1 != 'nan' and l1 != '' and
                                                l2 and l2 != 'nan' and l2 != '' and
                                                l3 and l3 != 'nan' and l3 != ''):
                                                full_l3_code = f"{l1}-{l2}-{l3}"
                                            else:
                                                continue
                                        
                                        knowledge_distribution[full_l3_code] = l3r_float
                                except (ValueError, TypeError):
                                    # 跳过无法转换为数字的比重值
                                    continue
                        except (ValueError, TypeError) as e:
                            # 跳过无效数据行
                            continue
            except Exception as e:
                print(f"读取组卷规则文件失败: {e}")
    except Exception as e:
        print(f"查找组卷规则文件失败: {e}")
    
    # 只构建第三级代码的统计数据
    stats_table = []
    
    # 定义固定的层级结构
    level1_codes = ['A', 'B', 'C', 'D']
    level2_codes = ['A', 'B', 'C', 'D']
    level3_codes = ['A', 'B', 'C', 'D', 'E', 'F']
    
    # 计算总题目数
    total_questions = sum(level3_stats.values()) if level3_stats else 1
    
    # 只处理第三级代码
    for l1_code in level1_codes:
        for l2_code in level2_codes:
            for l3_code in level3_codes:
                # 构建完整的三级代码
                full_l3_code = f"{l1_code}-{l2_code}-{l3_code}"
                
                # 获取实际统计和需求数据
                l3_actual_count = level3_stats.get(full_l3_code, 0)
                l3_actual_proportion = round((l3_actual_count / total_questions) * 100, 1) if total_questions > 0 else 0
                l3_required_proportion = knowledge_distribution.get(full_l3_code, 0)
                
                # 只有当有实际数据或需求数据时才添加
                if l3_actual_count > 0 or l3_required_proportion > 0:
                    stats_table.append({
                        "code": full_l3_code,
                        "actual_proportion": l3_actual_proportion,
                        "required_proportion": l3_required_proportion,
                        "has_requirement": l3_required_proportion > 0
                    })
    
    return render_template_string(
        """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>三级代码统计 - {{ paper.name }} - 题库管理系统</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 15px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                    overflow: hidden;
                    padding: 30px;
                }
                .header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                    margin: -30px -30px 30px -30px;
                }
                .header h1 {
                    margin: 0;
                    font-size: 2.5em;
                    font-weight: 300;
                }
                .nav {
                    background: #f8f9fa;
                    padding: 15px 30px;
                    border-bottom: 1px solid #e9ecef;
                    margin: -30px -30px 30px -30px;
                }
                .nav a {
                    color: #495057;
                    text-decoration: none;
                    margin-right: 20px;
                    padding: 8px 16px;
                    border-radius: 20px;
                    transition: all 0.3s ease;
                }
                .nav a:hover {
                    background: #007bff;
                    color: white;
                }
                .card {
                    border: 1px solid #e9ecef;
                    border-radius: 10px;
                    margin-bottom: 20px;
                }
                .card-header {
                    background: #f8f9fa;
                    padding: 15px 20px;
                    border-bottom: 1px solid #e9ecef;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .card-body {
                    padding: 20px;
                }
                .btn {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 20px;
                    cursor: pointer;
                    text-decoration: none;
                    display: inline-block;
                    transition: all 0.3s ease;
                }
                .btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }
                .btn-outline-secondary {
                    background: transparent;
                    border: 1px solid #6c757d;
                    color: #6c757d;
                }
                .btn-outline-secondary:hover {
                    background: #6c757d;
                    color: white;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                }
                th, td {
                    border: 1px solid #e9ecef;
                    padding: 12px;
                    text-align: left;
                }
                th {
                    background: #f8f9fa;
                    font-weight: 600;
                }
                tr:nth-child(even) {
                    background: #f8f9fa;
                }
                tr:hover {
                    background: #e9ecef;
                }
                .requirement-row {
                    background-color: #f8f9fa;
                }
                .requirement-cell {
                    background-color: #e3f2fd;
                    font-weight: 600;
                    color: #1976d2;
                }
                .positive-diff {
                    background-color: #e8f5e8;
                    color: #2e7d32;
                    font-weight: 600;
                }
                .negative-diff {
                    background-color: #ffebee;
                    color: #c62828;
                    font-weight: 600;
                }
                .zero-diff {
                    background-color: #f3e5f5;
                    color: #7b1fa2;
                    font-weight: 600;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📊 三级代码统计</h1>
                    <p>{{ paper.name }}</p>
                </div>
                
                <div class="nav">
                    <a href="/">🏠 首页</a>
                    <a href="/papers">📋 试卷管理</a>
                    <a href="/paper/{{ paper.id }}">📝 返回试卷详情</a>
                </div>
                
                <div class="content">
                    <h2>试卷三级代码统计 - {{ paper.name }}</h2>
                    <p>此页面显示试卷中题目的三级代码分布情况，可用于与组卷模板进行比对。</p>
                    
                    <div class="alert alert-info" style="background: #e3f2fd; border: 1px solid #bbdefb; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #1976d2;">📊 颜色说明</h4>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li><span style="background: #e3f2fd; color: #1976d2; padding: 2px 6px; border-radius: 3px; font-weight: 600;">蓝色背景</span>：组题需求的三级代码值</li>
                            <li><span style="background: #e8f5e8; color: #2e7d32; padding: 2px 6px; border-radius: 3px; font-weight: 600;">绿色</span>：实际比重高于需求比重</li>
                            <li><span style="background: #ffebee; color: #c62828; padding: 2px 6px; border-radius: 3px; font-weight: 600;">红色</span>：实际比重低于需求比重</li>
                            <li><span style="background: #f3e5f5; color: #7b1fa2; padding: 2px 6px; border-radius: 3px; font-weight: 600;">紫色</span>：实际比重等于需求比重</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 style="margin: 0;">三级代码分布</h3>
                            <a href="/paper/{{ paper.id }}" class="btn btn-outline-secondary">返回试卷详情</a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>3级代码<br>Level 3 Code</th>
                                            <th>实际比重(%)<br>Actual Proportion (%)</th>
                                            <th>组题需求(%)<br>Required Proportion (%)</th>
                                            <th>差异<br>Difference</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in stats_table %}
                                        <tr {% if item.has_requirement %}class="requirement-row"{% endif %}>
                                            <td>{{ item.code }}</td>
                                            <td>{{ item.actual_proportion }}</td>
                                            <td {% if item.has_requirement %}class="requirement-cell"{% endif %}>
                                                {% if item.required_proportion > 0 %}
                                                    {{ item.required_proportion }}
                                                {% else %}
                                                    0
                                                {% endif %}
                                            </td>
                                            <td class="{% if item.required_proportion > 0 %}{% if item.actual_proportion > item.required_proportion %}positive-diff{% elif item.actual_proportion < item.required_proportion %}negative-diff{% else %}zero-diff{% endif %}{% endif %}">
                                                {% if item.required_proportion > 0 %}
                                                    {{ "%.1f"|format(item.actual_proportion - item.required_proportion) }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        """, 
        paper=paper,
        stats_table=stats_table
    )

@app.route('/paper/<paper_id>/export', methods=['GET', 'POST'])
def export_paper(paper_id):
    """导出试卷为 Word 文档"""
    db_session = None
    try:
        db_session = get_db()
        generator = PaperGenerator(db_session)
        
        docx_buffer = generator.export_paper_to_docx(paper_id)
        
        paper = db_session.query(Paper).filter(Paper.id == paper_id).first()
        
        # 确保即使paper_name为空，也能提供一个安全的文件名
        if paper is not None and paper.name is not None:
            paper_name = str(paper.name)
        else:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            paper_name = f"无标题试卷_{timestamp}"

        safe_paper_name = secure_filename(paper_name)

        return send_file(
            docx_buffer,
            as_attachment=True,
            download_name=f'{safe_paper_name}.docx',
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
    except Exception as e:
        flash(f"导出试卷失败: {e}", "error")
        return redirect(url_for('view_paper', paper_id=paper_id))
    finally:
        close_db(db_session)

@app.route('/paper/<paper_id>/delete', methods=['POST'])
def delete_paper(paper_id):
    """删除试卷"""
    db_session = None
    
    try:
        db_session = get_db()
        paper = db_session.query(Paper).filter(Paper.id == paper_id).first()
        
        if not paper:
            flash("试卷不存在", "error")
            return redirect(url_for('papers'))
        
        db_session.delete(paper)
        db_session.commit()
        flash(f"试卷 '{paper.name}' 删除成功", "success")
        
    except Exception as e:
        flash(f"删除试卷失败: {e}", "error")
    finally:
        close_db(db_session)
    
    return redirect(url_for('papers'))

@app.route('/quick-generate', methods=['GET', 'POST'])
def quick_generate():
    """快速生成试卷"""
    if request.method == 'POST':
        try:
            db_session = get_db()
            generator = PaperGenerator(db_session)
            
            # 获取表单数据
            paper_name = request.form.get('paper_name', '').strip()
            difficulty_distribution = request.form.get('difficulty_distribution', 'balanced')
            
            if not paper_name:
                flash("试卷名称不能为空", "error")
                return redirect(url_for('quick_generate'))
            
            # 设置难度分布
            if difficulty_distribution == 'easy':
                distribution = {"1": 0.3, "2": 0.4, "3": 0.2, "4": 0.1, "5": 0.0}
            elif difficulty_distribution == 'hard':
                distribution = {"1": 0.0, "2": 0.1, "3": 0.2, "4": 0.4, "5": 0.3}
            else:  # balanced
                distribution = {"1": 0.1, "2": 0.2, "3": 0.4, "4": 0.2, "5": 0.1}
            
            # 生成试卷
            paper = generator.generate_paper_by_difficulty_distribution(
                paper_name=paper_name,
                paper_description=f"快速生成的{paper_name}",
                difficulty_distribution=distribution
            )
            
            flash(f"试卷 '{paper.name}' 生成成功！", "success")
            return redirect(url_for('view_paper', paper_id=paper.id))
            
        except Exception as e:
            flash(f"快速生成试卷失败: {e}", "error")
        finally:
            close_db(db_session)
    
    return render_template_string(quick_generate_template)

@app.route('/upload-paper-rule', methods=['GET', 'POST'])
def upload_paper_rule():
    """上传试卷规则Excel并自动组卷"""
    import pandas as pd
    from werkzeug.utils import secure_filename
    if request.method == 'POST':
        file = request.files.get('file')
        if not file or not file.filename:
            flash('请上传Excel文件或文件无名称', 'error')
            return redirect(url_for('upload_paper_rule'))
        filename = secure_filename(file.filename)
        filepath = os.path.join('uploads', filename)
        file.save(filepath)
        
        # 新增：获取多套组卷参数
        num_sets = int(request.form.get('num_sets', 1))
        if num_sets < 1:
            num_sets = 1
        if num_sets > 10:
            num_sets = 10
            
        # 获取试题ID重复率限制参数
        repeat_rate_limit = float(request.form.get('repeat_rate_limit', 0.09))
        if repeat_rate_limit < 0:
            repeat_rate_limit = 0
        if repeat_rate_limit > 1:
            repeat_rate_limit = 1
        
        try:
            # 解析Sheet1（题型分布）- 适应新的中英文双行表头格式
            # 读取Excel时使用前两行作为多级表头（第一行中文，第二行英文）
            df1 = pd.read_excel(filepath, sheet_name='题型分布', header=[0, 1], engine='openpyxl')
            
            # 处理多级表头，只使用中文部分作为列名，并去除换行符
            df1.columns = [col[0].split("\n")[0].strip().replace(" ", "").replace("　", "") if isinstance(col[0], str) and "\n" in col[0] else col[0].strip().replace(" ", "").replace("　", "") for col in df1.columns]

            # 定义标准列名和可能的别名
            column_aliases = {
                "题库名称": ["题库名称", "题库名称Question Bank Name", "Question Bank Name", "题库代码", "题库代码/Bank Code"],
                "题型": ["题型", "题型Question Type Code", "Question Type Code", "题型代码", "题型代码/Question Type", "题型\nQuestion Type", "题型代码\nQuestion Type Code"],
                "题量": ["题量", "题量Question Count", "Question Count", "题目数量", "题目数量/Question Count"],
                "分值": ["分值", "分值Score Value", "Score Value", "分数", "分数/Score"]
            }

            # 构建列名映射
            column_mapping = {}
            for std_col, aliases in column_aliases.items():
                for col in df1.columns:
                    if col in aliases:
                        column_mapping[col] = std_col

            # 重命名列
            if column_mapping:
                df1 = df1.rename(columns=column_mapping)

            # 检查必需的列
            required_cols_s1 = {'题库名称', '题型', '题量', '分值'}
            if not required_cols_s1.issubset(df1.columns):
                missing_cols = required_cols_s1 - set(df1.columns)
                flash(f'题型分布表缺少必需列: {", ".join(missing_cols)}', 'error')
                return redirect(url_for('upload_paper_rule'))

            paper_structure = []

            # 从第三行开始读取数据（跳过表头两行）
            for _, row in df1.iterrows():
                if pd.isna(row.get('题型')) or pd.isna(row.get('题量')) or pd.isna(row.get('分值')) or pd.isna(row.get('题库名称')):
                    continue
                qtype = str(row['题型']).split('（')[0]
                paper_structure.append({
                    'question_bank_name': str(row['题库名称']).strip(),
                    'question_type': qtype,
                    'count': int(row['题量']),
                    'score_per_question': float(row['分值'])
                })

            # 解析Sheet2（知识点分布）- 适应新的中英文双行表头格式
            # 读取Excel时使用前两行作为多级表头（第一行中文，第二行英文）
            df2 = pd.read_excel(filepath, sheet_name='知识点分布', header=[0, 1], engine='openpyxl')
            
            # 处理多级表头，只使用中文部分作为列名，并去除换行符
            df2.columns = [col[0].split("\n")[0].strip().replace(" ", "").replace("　", "") if isinstance(col[0], str) and "\n" in col[0] else col[0].strip().replace(" ", "").replace("　", "") for col in df2.columns]
            
            # 定义标准列名和可能的别名
            knowledge_column_aliases = {
                "1级代码": ["1级代码", "1级代码\nLevel 1 Code", "Level 1 Code"],
                "1级比重(%)": ["1级比重(%)", "1级比重(%)\nLevel 1 Proportion (%)", "Level 1 Proportion (%)"],
                "2级代码": ["2级代码", "2级代码\nLevel 2 Code", "Level 2 Code"],
                "2级比重(%)": ["2级比重(%)", "2级比重(%)\nLevel 2 Proportion (%)", "Level 2 Proportion (%)"],
                "3级代码": ["3级代码", "3级代码\nLevel 3 Code", "Level 3 Code"],
                "3级比重(%)": ["3级比重(%)", "3级比重(%)\nLevel 3 Proportion (%)", "Level 3 Proportion (%)"]
            }
            
            # 构建列名映射
            knowledge_column_mapping = {}
            for std_col, aliases in knowledge_column_aliases.items():
                for col in df2.columns:
                    if col in aliases:
                        knowledge_column_mapping[col] = std_col
            
            # 重命名列
            if knowledge_column_mapping:
                df2 = df2.rename(columns=knowledge_column_mapping)
            knowledge_distribution = {}
            if not df2.empty:
                required_cols_s2 = {'1级代码', '1级比重(%)', '2级代码', '2级比重(%)', '3级代码', '3级比重(%)'}
                if not required_cols_s2.issubset(df2.columns):
                    missing_cols = required_cols_s2 - set(df2.columns)
                    flash(f'知识点分布表缺少必需列: {", ".join(missing_cols)}', 'error')
                    return redirect(url_for('upload_paper_rule'))

                # 用于跟踪当前的1级和2级代码
                current_l1 = None
                current_l1r = None
                current_l2 = None
                current_l2r = None
                
                for _, row in df2.iterrows():
                    if row.isnull().all():
                        continue
                    
                    # 更新当前1级代码和比重
                    if pd.notna(row['1级代码']) and pd.notna(row['1级比重(%)']):
                        current_l1 = str(row['1级代码']).strip()
                        try:
                            current_l1r = float(row['1级比重(%)'])
                        except (ValueError, TypeError):
                            continue
                    
                    # 更新当前2级代码和比重
                    if pd.notna(row['2级代码']) and pd.notna(row['2级比重(%)']):
                        current_l2 = str(row['2级代码']).strip()
                        try:
                            current_l2r = float(row['2级比重(%)'])
                        except (ValueError, TypeError):
                            continue
                    
                    # 处理3级代码（必须有值）- 直接使用Excel中的完整三级代码
                    if pd.notna(row['3级代码']) and pd.notna(row['3级比重(%)']):
                        level3_code = str(row['3级代码']).strip()
                        try:
                            level3_ratio = float(row['3级比重(%)'])
                        except (ValueError, TypeError):
                            continue
                        
                        # 直接使用三级代码作为键值，不构建嵌套结构
                        knowledge_distribution[level3_code] = level3_ratio
            
            # 从表单或Excel获取试卷名称
            paper_name = request.form.get('paper_name')
            if not paper_name:
                if paper_structure:
                    paper_name = f"{paper_structure[0]['question_bank_name']} - 自动组卷"
                else:
                    paper_name = f"自动组卷_{int(time.time())}"

            db_session = get_db()
            generator = PaperGenerator(db_session)
            paper_ids = []
            for i in range(num_sets):
                this_paper_name = paper_name if num_sets == 1 else f"{paper_name}_第{i+1}套"
                paper = generator.generate_paper_by_knowledge_distribution(
                    paper_name=this_paper_name,
                    paper_structure=paper_structure,
                    knowledge_distribution=knowledge_distribution,
                    repeat_rate_limit=repeat_rate_limit
                )
                paper_ids.append(paper.id)
            flash(f'成功生成 {num_sets} 套试卷！', 'success')
            return redirect(url_for('papers'))
        except FileNotFoundError:
            flash("上传的文件未找到，请重试。", "error")
        except ValueError as e:
            flash(f"组卷失败，请检查规则配置：{e}", "error")
        except KeyError as e:
            flash(f"Excel文件中缺少必需的列名: {e}，请使用模板文件。", "error")
        except Exception as e:
            flash(f"处理文件时发生未知错误: {e}", "error")
        return redirect(url_for('upload_paper_rule')
    )

    # GET请求返回上传页面，增加多套组卷输入框和重复率限制输入框
    return render_template_string('''
    <h2>上传组卷规则Excel</h2>
    <div class="nav-tabs">
        <a href="/" class="nav-link">🏠 首页</a>
        <a href="/papers" class="nav-link">📋 试卷管理</a>
        <a href="/quick-generate" class="nav-link">⚡ 快速生成</a>
        <a href="/generate-paper" class="nav-link">🎯 自定义组卷</a>
        <a href="/upload-paper-rule" class="nav-link active">🗂️ 上传组卷规则</a>
        <a href="/banks" class="nav-link">📚 题库管理</a>
    </div>
    <div style="margin-bottom: 20px;">
        <a href="/download-paper-rule-template" class="btn btn-success" style="margin-bottom:16px;display:inline-block;">📥 下载组卷规则模板</a>
    </div>
    <style>
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }
        .nav-tabs .nav-link.active {
            color: #007bff;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
    </style>
    <form method="post" enctype="multipart/form-data">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <div class="form-group" style="margin-bottom: 1rem;">
            <label for="paper_name">试卷名称 (可选, 默认为题库名)</label>
            <input type="text" id="paper_name" name="paper_name" class="form-control" style="width:100%; padding:8px; border-radius:4px; border:1px solid #ccc;">
        </div>
        <div class="form-group" style="margin-bottom: 1rem;">
            <label for="num_sets">生成套数</label>
            <input type="number" id="num_sets" name="num_sets" min="1" max="10" value="1" style="width:100px;">
        </div>
        <div class="form-group" style="margin-bottom: 1rem;">
            <label for="repeat_rate_limit">试题ID重复率限制 (0-1之间，默认0.09)</label>
             <input type="number" id="repeat_rate_limit" name="repeat_rate_limit" min="0" max="1" step="0.01" value="0.09" style="width:100px;">
             <small style="display:block;color:#666;margin-top:5px;">基于试题ID计算重复率，确保任意两套试卷之间的题目重复率低于设定阈值。设置为0表示不允许重复，设置为1表示允许完全重复</small>
        </div>
        <div class="form-group">
            <label for="file">上传组卷规则文件</label>
            <input type="file" id="file" name="file" accept=".xlsx" required>
        </div>
        <button type="submit" style="padding:10px 20px; border-radius:5px; border:none; background-color:#007bff; color:white; cursor:pointer; margin-top:10px;">上传并自动组卷</button>
    </form>
    ''')

# 组卷功能模板
papers_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>试卷管理 - 题库管理系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }
        .nav-tabs .nav-link.active {
            color: #007bff;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
        .content {
            padding: 30px;
        }
        .actions {
            margin-bottom: 30px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            text-align: center;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .papers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        .paper-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .paper-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .paper-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        .paper-info {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 15px;
        }
        .paper-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 0.9em;
        }
        .paper-actions {
            display: flex;
            gap: 10px;
        }
        .btn-sm {
            padding: 6px 12px;
            font-size: 14px;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        .empty-state h3 {
            margin-bottom: 10px;
            color: #495057;
        }
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-message {
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .flash-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .flash-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 试卷管理</h1>
            <p>管理和生成试卷</p>
        </div>
        
        <div class="nav-tabs">
            <a href="/" class="nav-link">🏠 首页</a>
            <a href="/papers" class="nav-link active">📋 试卷管理</a>
            <a href="/quick-generate" class="nav-link">⚡ 快速生成</a>
            <a href="/generate-paper" class="nav-link">🎯 自定义组卷</a>
            <a href="/upload-paper-rule" class="nav-link">📤 上传组卷规则</a>
            <a href="/banks" class="nav-link">📚 题库管理</a>
        </div>
        
        <div class="content">
            <div class="flash-messages">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="flash-message flash-{{ category }}">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>
            
            <div class="actions">
                <form id="batchForm" method="post" style="display:inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <input type="hidden" name="paper_ids" id="batchPaperIds">
                    <button type="button" class="btn btn-success" onclick="batchExportExcel()">📊 批量导出Excel</button>
                    <button type="button" class="btn btn-primary" onclick="batchExportWord()">📄 批量导出Word</button>
                    <button type="button" class="btn btn-danger" onclick="batchDelete()">🗑️ 批量删除</button>
                </form>
                <a href="/quick-generate" class="btn btn-success">⚡ 快速生成</a>
                <a href="/generate-paper" class="btn btn-primary">🎯 自定义组卷</a>
            </div>
            <script>
            function getCheckedPaperIds() {
                let ids = [];
                document.querySelectorAll('.paper-checkbox:checked').forEach(cb => ids.push(cb.value));
                return ids;
            }
            function batchExportExcel() {
                let ids = getCheckedPaperIds();
                if(ids.length===0){alert('请先选择试卷');return;}
                let form = document.getElementById('batchForm');
                form.action = '/export_papers_excel';
                document.getElementById('batchPaperIds').value = ids.join(',');
                form.submit();
            }
            function batchExportWord() {
                let ids = getCheckedPaperIds();
                if(ids.length===0){alert('请先选择试卷');return;}
                let form = document.getElementById('batchForm');
                form.action = '/export_papers_word';
                document.getElementById('batchPaperIds').value = ids.join(',');
                form.submit();
            }
            function batchDelete() {
                let ids = getCheckedPaperIds();
                if(ids.length===0){alert('请先选择试卷');return;}
                if(!confirm('确定要批量删除选中的试卷吗？'))return;
                let form = document.getElementById('batchForm');
                form.action = '/delete_papers';
                document.getElementById('batchPaperIds').value = ids.join(',');
                form.submit();
            }
            function toggleAllPapers(cb){
                document.querySelectorAll('.paper-checkbox').forEach(x=>x.checked=cb.checked);
            }
            </script>
            <div style="margin-bottom:10px;text-align:right;">
                <input type="checkbox" id="checkAll" onclick="toggleAllPapers(this)"> <label for="checkAll">全选</label>
            </div>
            {% if papers %}
            <div class="papers-grid">
                {% for paper in papers %}
                <div class="paper-card">
                    <input type="checkbox" class="paper-checkbox" value="{{ paper.id }}" style="float:right;transform:scale(1.3);margin-top:2px;">
                    <div class="paper-title">{{ paper.name }}</div>
                    <div class="paper-info">
                        {{ paper.description or '暂无描述' }}
                    </div>
                    <div class="paper-stats">
                        <span>📊 总分: {{ paper.total_score }}分</span>
                        <span>⏱️ 时长: {{ paper.duration }}分钟</span>
                    </div>
                    <div class="paper-stats">
                        <span>🎯 难度: {{ paper.difficulty_level or '未设置' }}</span>
                    </div>
                    <div class="paper-actions">
                        <a href="/paper/{{ paper.id }}" class="btn btn-primary btn-sm">👁️ 查看</a>
                        <form method="GET" action="/paper/{{ paper.id }}/export" style="display: inline;">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-success btn-sm">📥 导出</button>
                        </form>
                        <form method="GET" action="/paper/{{ paper.id }}/export_excel" style="display: inline;">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-success btn-sm">📊 Excel导出</button>
                        </form>
                        <form method="POST" action="/paper/{{ paper.id }}/delete" style="display: inline;" onsubmit="return confirm('确定要删除这个试卷吗？')">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-danger btn-sm">🗑️ 删除</button>
                        </form>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">
                <h3>📭 暂无试卷</h3>
                <p>还没有生成任何试卷，点击上方按钮开始创建吧！</p>
            </div>
            {% endif %}
        </div>
    </div>
</body>
</html>
"""

generate_paper_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自定义组卷 - 题库管理系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .nav-tabs {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .nav-tabs a {
            color: #495057;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px 5px 0 0;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            border-bottom: none;
            position: relative;
            background-color: #f8f9fa;
        }
        .nav-tabs a:hover {
            background: #e9ecef;
            color: #007bff;
            border-color: #dee2e6;
        }
        .nav-tabs a.active {
            background: #fff;
            color: #007bff;
            border-color: #dee2e6;
            border-bottom: 2px solid #fff;
            margin-bottom: -1px;
            font-weight: 500;
        }
        .content {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            text-align: center;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .rules-container {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .rule-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .rule-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .rule-title {
            font-weight: 600;
            color: #333;
        }
        .remove-rule {
            background: #ff416c;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 16px;
        }
        .rule-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-message {
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .flash-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .flash-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 自定义组卷</h1>
            <p>根据规则自动生成试卷</p>
        </div>
        
        <div class="nav-tabs">
            <a href="/">🏠 首页</a>
            <a href="/papers">📋 试卷管理</a>
            <a href="/quick-generate">⚡ 快速生成</a>
            <a href="/generate-paper" class="active">🎯 自定义组卷</a>
            <a href="/upload-paper-rule">📤 上传组卷规则</a>
            <a href="/banks">📚 题库管理</a>
        </div>
        
        <div class="content">
            <div class="flash-messages">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="flash-message flash-{{ category }}">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>
            
            <form method="POST" id="generateForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="form-row">
                    <div class="form-group">
                        <label for="paper_name">试卷名称 *</label>
                        <input type="text" id="paper_name" name="paper_name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="difficulty_level">试卷难度</label>
                        <select id="difficulty_level" name="difficulty_level" class="form-control">
                            <option value="简单">简单</option>
                            <option value="中等" selected>中等</option>
                            <option value="困难">困难</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="total_score">试卷总分</label>
                        <input type="number" id="total_score" name="total_score" class="form-control" value="100" min="1" max="200">
                    </div>
                    <div class="form-group">
                        <label for="duration">考试时长（分钟）</label>
                        <input type="number" id="duration" name="duration" class="form-control" value="120" min="30" max="300">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="paper_description">试卷描述</label>
                    <textarea id="paper_description" name="paper_description" class="form-control" rows="3" placeholder="可选：试卷的详细描述"></textarea>
                </div>
                
                <div class="rules-container">
                    <h3>📋 组卷规则</h3>
                    <div id="rulesList">
                        <!-- 规则项将在这里动态添加 -->
                    </div>
                    <button type="button" class="btn btn-success" onclick="addRule()">➕ 添加规则</button>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">🚀 生成试卷</button>
                    <a href="/papers" class="btn btn-danger">❌ 取消</a>
                </div>
                
                <input type="hidden" id="rule_count" name="rule_count" value="0">
            </form>
        </div>
    </div>
    
    <script>
        let ruleIndex = 0;
        
        function addRule() {
            const rulesList = document.getElementById('rulesList');
            const ruleDiv = document.createElement('div');
            ruleDiv.className = 'rule-item';
            ruleDiv.innerHTML = `
                <div class="rule-header">
                    <span class="rule-title">规则 ${ruleIndex + 1}</span>
                    <button type="button" class="remove-rule" onclick="removeRule(this)">×</button>
                </div>
                <div class="rule-grid">
                    <div class="form-group">
                        <label>题型</label>
                        <select name="rule_${ruleIndex}_type" class="form-control" required>
                            <option value="">请选择题型</option>
                            <option value="B">B（单选题）</option>
                            <option value="G">G（多选题）</option>
                            <option value="C">C（判断题）</option>
                            <option value="T">T（填空题）</option>
                            <option value="D">D（简答题）</option>
                            <option value="U">U（计算题）</option>
                            <option value="W">W（论述题）</option>
                            <option value="E">E（案例分析题）</option>
                            <option value="F">F（综合题）</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>难度</label>
                        <select name="rule_${ruleIndex}_difficulty" class="form-control" required>
                            <option value="">请选择难度</option>
                            <option value="1">1（很简单）</option>
                            <option value="2">2（简单）</option>
                            <option value="3">3（中等）</option>
                            <option value="4">4（困难）</option>
                            <option value="5">5（很难）</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>题目数量</label>
                        <input type="number" name="rule_${ruleIndex}_count" class="form-control" value="1" min="1" max="50" required>
                    </div>
                    <div class="form-group">
                        <label>每题分值</label>
                        <input type="number" name="rule_${ruleIndex}_score" class="form-control" value="5.0" min="0.5" max="50" step="0.5" required>
                    </div>
                    <div class="form-group">
                        <label>章节名称</label>
                        <input type="text" name="rule_${ruleIndex}_section" class="form-control" placeholder="如：单选题、多选题等">
                    </div>
                </div>
            `;
            rulesList.appendChild(ruleDiv);
            ruleIndex++;
            document.getElementById('rule_count').value = ruleIndex;
        }
        
        function removeRule(button) {
            button.parentElement.parentElement.remove();
            updateRuleNumbers();
        }
        
        function updateRuleNumbers() {
            const rules = document.querySelectorAll('.rule-item');
            rules.forEach((rule, index) => {
                rule.querySelector('.rule-title').textContent = `规则 ${index + 1}`;
            });
            ruleIndex = rules.length;
            document.getElementById('rule_count').value = ruleIndex;
        }
        
        // 页面加载时添加一个默认规则
        window.onload = function() {
            addRule();
        };
    </script>
</body>
</html>
"""

view_paper_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ paper.name }} - 题库管理系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .nav-tabs {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .nav-tabs a {
            color: #495057;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px 5px 0 0;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            border-bottom: none;
            position: relative;
            background-color: #f8f9fa;
        }
        .nav-tabs a:hover {
            background: #e9ecef;
            color: #007bff;
            border-color: #dee2e6;
        }
        .nav-tabs a.active {
            background: #fff;
            color: #007bff;
            border-color: #dee2e6;
            border-bottom: 2px solid #fff;
            margin-bottom: -1px;
            font-weight: 500;
        }
        .content {
            padding: 30px;
        }
        .paper-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .paper-title {
            font-size: 2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        .paper-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
        }
        .paper-description {
            color: #666;
            font-style: italic;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .questions-section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 1.5em;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        .question-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
        }
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .question-number {
            font-weight: 600;
            color: #667eea;
            font-size: 1.1em;
        }
        .question-score {
            background: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.9em;
        }
        .question-stem {
            color: #333;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        .question-options {
            margin-left: 20px;
        }
        .option {
            margin-bottom: 8px;
            color: #666;
        }
        .actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            text-align: center;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-message {
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .flash-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .flash-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 {{ paper.name }}</h1>
            <p>试卷详情</p>
        </div>
        
        <div class="nav-tabs">
            <a href="/">🏠 首页</a>
            <a href="/papers">📋 试卷管理</a>
            <a href="/view_paper" class="active">📄 查看试卷</a>
        </div>
        
        <div class="content">
            <div class="flash-messages">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="flash-message flash-{{ category }}">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>
            
            <div class="actions" style="margin-bottom: 20px;">
                <a href="/paper/{{ paper.id }}/export" class="btn btn-success">📥 导出试卷</a>
                <a href="/paper/{{ paper.id }}/verification-report" class="btn btn-success">📋 下载校核报告</a>
                <a href="/paper/{{ paper.id }}/level3_code_stats" class="btn btn-primary">📊 查看三级代码统计</a>
                <a href="/papers" class="btn btn-primary">📋 返回列表</a>
                <form method="POST" action="/paper/{{ paper.id }}/delete" style="display: inline;" onsubmit="return confirm('确定要删除这个试卷吗？')">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">🗑️ 删除试卷</button>
                </form>
            </div>
            
            <div class="paper-info">
                <div class="paper-title">{{ paper.name }}</div>
                <div class="paper-meta">
                    <div class="meta-item">
                        <span>📊 总分:</span>
                        <span>{{ paper.total_score }}分</span>
                    </div>
                    <div class="meta-item">
                        <span>⏱️ 时长:</span>
                        <span>{{ paper.duration }}分钟</span>
                    </div>
                    <div class="meta-item">
                        <span>🎯 难度:</span>
                        <span>{{ paper.difficulty_level or '未设置' }}</span>
                    </div>

                </div>
                {% if paper.description %}
                <div class="paper-description">{{ paper.description }}</div>
                {% endif %}
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{{ stats.total_questions or 0 }}</div>
                    <div class="stat-label">总题目数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ stats.total_score or 0 }}</div>
                    <div class="stat-label">实际总分</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ stats.question_types|length or 0 }}</div>
                    <div class="stat-label">题型种类</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ stats.sections|length or 0 }}</div>
                    <div class="stat-label">章节数量</div>
                </div>
            </div>
            
            <div class="questions-section">
                <div class="section-title">📝 题目列表</div>
                {% if paper_questions %}
                    {% for pq in paper_questions %}
                    <div class="question-item">
                        <div class="question-header">
                            <span class="question-number">第{{ pq.question_order }}题</span>
                            <span class="question-score">{{ pq.score }}分</span>
                        </div>
                        <div class="question-stem">{{ pq.question.question_stem }}</div>
                        {% if pq.question.option_a or pq.question.option_b or pq.question.option_c or pq.question.option_d or pq.question.option_e %}
                        <div class="question-options">
                            {% if pq.question.option_a %}<div class="option">A. {{ pq.question.option_a }}</div>{% endif %}
                            {% if pq.question.option_b %}<div class="option">B. {{ pq.question.option_b }}</div>{% endif %}
                            {% if pq.question.option_c %}<div class="option">C. {{ pq.question.option_c }}</div>{% endif %}
                            {% if pq.question.option_d %}<div class="option">D. {{ pq.question.option_d }}</div>{% endif %}
                            {% if pq.question.option_e %}<div class="option">E. {{ pq.question.option_e }}</div>{% endif %}
                        </div>
                        {% endif %}
                        <div style="margin-top: 10px; color: #666; font-size: 0.9em;">
                            <span>题型: {{ pq.question.question_type_code }}</span> | 
                            <span>难度: {{ pq.question.difficulty_code }}</span>
                            {% if pq.section_name %} | <span>章节: {{ pq.section_name }}</span>{% endif %}
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div style="text-align: center; padding: 40px; color: #6c757d;">
                        <h3>📭 暂无题目</h3>
                        <p>这个试卷还没有添加任何题目。</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
"""

quick_generate_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速生成试卷 - 题库管理系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .nav {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #e9ecef;
        }
        .nav a {
            color: #495057;
            text-decoration: none;
            margin-right: 20px;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        .nav a:hover {
            background: #007bff;
            color: white;
        }
        .nav a.active {
            background: #007bff;
            color: white;
        }
        .content {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }
        .difficulty-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        .difficulty-option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        .difficulty-option:hover {
            border-color: #667eea;
            background: #e3f2fd;
        }
        .difficulty-option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        .difficulty-option input[type="radio"] {
            display: none;
        }
        .difficulty-title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        .difficulty-desc {
            font-size: 0.9em;
            opacity: 0.8;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            text-align: center;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-message {
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .flash-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .flash-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ 快速生成试卷</h1>
            <p>一键生成标准试卷</p>
        </div>
        
        <div class="nav-tabs">
            <a href="/">🏠 首页</a>
            <a href="/papers">📋 试卷管理</a>
            <a href="/quick-generate" class="active">⚡ 快速生成</a>
            <a href="/generate-paper">🎯 自定义组卷</a>
            <a href="/upload-paper-rule">📤 上传组卷规则</a>
            <a href="/banks">📚 题库管理</a>
        </div>
        
        <div class="content">
            <div class="flash-messages">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="flash-message flash-{{ category }}">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>
            
            <form method="POST">
                <div class="form-group">
                    <label for="paper_name">试卷名称 *</label>
                    <input type="text" id="paper_name" name="paper_name" class="form-control" required placeholder="如：2024年春季考试试卷">
                </div>
                
                <div class="form-group">
                    <label>难度分布</label>
                    <div class="difficulty-options">
                        <label class="difficulty-option" onclick="selectDifficulty('easy')">
                            <input type="radio" name="difficulty_distribution" value="easy">
                            <div class="difficulty-title">😊 简单</div>
                            <div class="difficulty-desc">适合基础测试</div>
                        </label>
                        <label class="difficulty-option selected" onclick="selectDifficulty('balanced')">
                            <input type="radio" name="difficulty_distribution" value="balanced" checked>
                            <div class="difficulty-title">⚖️ 平衡</div>
                            <div class="difficulty-desc">标准难度分布</div>
                        </label>
                        <label class="difficulty-option" onclick="selectDifficulty('hard')">
                            <input type="radio" name="difficulty_distribution" value="hard">
                            <div class="difficulty-title">😰 困难</div>
                            <div class="difficulty-desc">适合挑战性测试</div>
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">🚀 生成试卷</button>
                    <a href="/papers" class="btn btn-danger">❌ 取消</a>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        function selectDifficulty(value) {
            // 移除所有选中状态
            document.querySelectorAll('.difficulty-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // 选中当前选项
            event.currentTarget.classList.add('selected');
            
            // 设置radio值
            document.querySelector(`input[value="${value}"]`).checked = true;
        }
    </script>
</body>
</html>
"""



@app.route('/download-paper-rule-template', methods=['GET'])
def download_paper_rule_template():
    """生成并下载组卷规则Excel模板"""
    import pandas as pd
    output = BytesIO()
    # Sheet1: 题型分布
    df1 = pd.DataFrame([
        ['BWGL-3-LL', 'B（单选题）', 10, 2]
    ])
    df1.columns = ['题库名称\nquestion_bank_name', '题型代码\nquestion_type_code', '题量\nquestion_count', '分值\nscore_value']
    # Sheet2: 知识点分布
    df2 = pd.DataFrame([
        ['A', 50, 'B', 60, 'C', 100]
    ])
    df2.columns = ['1级代码\nlevel_1_code', '1级比重(%)\nlevel_1_weight', '2级代码\nlevel_2_code', '2级比重(%)\nlevel_2_weight', '3级代码\nlevel_3_code', '3级比重(%)\nlevel_3_weight']
    with pd.ExcelWriter(output, engine='openpyxl') as writer: # type: ignore
        df1.to_excel(writer, index=False, sheet_name='题型分布')
        df2.to_excel(writer, index=False, sheet_name='知识点分布')
    output.seek(0)
    return send_file(output, as_attachment=True, download_name='组卷规则模板.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

@app.route('/paper/<paper_id>/verification-report', methods=['GET'])
def generate_paper_verification_report(paper_id):
    """生成试卷校核报告，返回与组卷规则模板相同格式的Excel文件"""
    import pandas as pd
    from utils import parse_question_id
    
    db_session = None
    try:
        db_session = get_db()
        
        # 获取试卷信息
        paper = db_session.query(Paper).filter(Paper.id == paper_id).first()
        if not paper:
            flash("试卷不存在", "error")
            return redirect(url_for('papers'))
        
        # 获取试卷题目
        paper_questions = db_session.query(PaperQuestion).filter(
            PaperQuestion.paper_id == paper_id
        ).order_by(PaperQuestion.question_order).all()
        
        if not paper_questions:
            flash("试卷中没有题目", "warning")
            return redirect(url_for('paper_detail', paper_id=paper_id))
        
        # 统计题型分布
        question_type_stats = {}
        total_score = 0
        
        for pq in paper_questions:
            q = pq.question
            qtype = q.question_type_code or 'Unknown'
            score = pq.score or 0
            
            if qtype not in question_type_stats:
                question_type_stats[qtype] = {'count': 0, 'total_score': 0, 'score_per_question': score}
            
            question_type_stats[qtype]['count'] += 1
            question_type_stats[qtype]['total_score'] += score
            total_score += score
        
        # 构建题型分布数据
        type_distribution_data = []
        for qtype, stats in question_type_stats.items():
            # 获取题型名称
            type_name_map = {
                'A': 'A（判断题）',
                'B': 'B（单选题）', 
                'C': 'C（多选题）',
                'D': 'D（填空题）',
                'E': 'E（简答题）',
                'F': 'F（论述题）',
                'G': 'G（计算题）',
                'H': 'H（案例分析题）'
            }
            type_display = type_name_map.get(qtype, f'{qtype}（未知题型）')
            
            type_distribution_data.append([
                paper.name,  # 使用试卷名称作为题库名称
                type_display,
                stats['count'],
                stats['score_per_question']
            ])
        
        # 统计三级代码分布
        level3_code_stats = {}
        level2_code_stats = {}
        level1_code_stats = {}
        total_questions = len(paper_questions)
        
        for pq in paper_questions:
            question = pq.question
            try:
                # 解析题目ID，提取层级代码
                parsed = parse_question_id(question.id)
                level_codes = parsed.get("level_codes", [])
                
                if len(level_codes) >= 3:
                    l1, l2, l3 = level_codes[0], level_codes[1], level_codes[2]
                    
                    # 统计各级代码
                    if l1 not in level1_code_stats:
                        level1_code_stats[l1] = 0
                    level1_code_stats[l1] += 1
                    
                    if l2 not in level2_code_stats:
                        level2_code_stats[l2] = 0
                    level2_code_stats[l2] += 1
                    
                    if l3 not in level3_code_stats:
                        level3_code_stats[l3] = 0
                    level3_code_stats[l3] += 1
                    
            except Exception as e:
                print(f"解析题目ID失败: {question.id}, 错误: {e}")
                continue
        
        # 构建知识点分布数据 - 与需求模板格式完全一致
        knowledge_distribution_data = []
        
        # 统计试卷中实际出现的三级代码组合
        actual_level3_combinations = {}
        for pq in paper_questions:
            try:
                parsed = parse_question_id(pq.question.id)
                level_codes = parsed.get("level_codes", [])
                if len(level_codes) >= 3:
                    l1, l2, l3 = level_codes[0], level_codes[1], level_codes[2]
                    combination = f"{l1}-{l2}-{l3}"
                    if combination not in actual_level3_combinations:
                        actual_level3_combinations[combination] = {
                            'l1': l1, 'l2': l2, 'l3': l3, 'count': 0
                        }
                    actual_level3_combinations[combination]['count'] += 1
            except:
                continue
        
        # 按照需求模板的格式生成数据：每行包含一个完整的三级代码组合
        # 格式：1级代码, 1级比重(%), 2级代码, 2级比重(%), 3级代码, 3级比重(%)
        for combination, data in actual_level3_combinations.items():
            l1, l2, l3 = data['l1'], data['l2'], data['l3']
            count = data['count']
            
            # 计算各级代码的比重
            l1_count = sum(1 for pq in paper_questions 
                          if parse_question_id(pq.question.id).get("level_codes", [])[:1] == [l1])
            l2_count = sum(1 for pq in paper_questions 
                          if parse_question_id(pq.question.id).get("level_codes", [])[:2] == [l1, l2])
            l3_count = count
            
            l1_proportion = round((l1_count / total_questions) * 100, 1) if total_questions > 0 else 0
            l2_proportion = round((l2_count / total_questions) * 100, 1) if total_questions > 0 else 0
            l3_proportion = round((l3_count / total_questions) * 100, 1) if total_questions > 0 else 0
            
            knowledge_distribution_data.append([
                l1, l1_proportion, l2, l2_proportion, l3, l3_proportion
            ])
        
        # 如果没有数据，添加一行示例数据以保持格式一致
        if not knowledge_distribution_data:
            knowledge_distribution_data.append([
                'A', 0.0, 'A', 0.0, 'A', 0.0
            ])
        
        # 创建Excel文件
        output = BytesIO()
        
        # 创建题型分布DataFrame
        df1 = pd.DataFrame(type_distribution_data)
        df1.columns = ['题库名称\nquestion_bank_name', '题型代码\nquestion_type_code', '题量\nquestion_count', '分值\nscore_value']
        
        # 创建知识点分布DataFrame
        df2 = pd.DataFrame(knowledge_distribution_data)
        df2.columns = ['1级代码\nlevel_1_code', '1级比重(%)\nlevel_1_weight', 
                      '2级代码\nlevel_2_code', '2级比重(%)\nlevel_2_weight', 
                      '3级代码\nlevel_3_code', '3级比重(%)\nlevel_3_weight']
        
        # 写入Excel文件
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df1.to_excel(writer, index=False, sheet_name='题型分布')
            df2.to_excel(writer, index=False, sheet_name='知识点分布')
        
        output.seek(0)
        
        # 生成文件名
        filename = f'{paper.name}_校核报告.xlsx'
        
        return send_file(
            output, 
            as_attachment=True, 
            download_name=filename, 
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
    except Exception as e:
        flash(f"生成校核报告失败: {e}", "error")
        return redirect(url_for('paper_detail', paper_id=paper_id))
    finally:
        close_db(db_session)

@app.route('/paper/<paper_id>/export_excel', methods=['GET', 'POST'])
def export_paper_excel(paper_id):
    """导出单套试卷为Excel，结构与题库导入模板一致"""
    import pandas as pd
    db_session = None
    try:
        db_session = get_db()
        paper = db_session.query(Paper).filter(Paper.id == paper_id).first()
        if not paper:
            flash("试卷不存在", "error")
            return redirect(url_for('papers'))
        paper_questions = db_session.query(PaperQuestion).filter(PaperQuestion.paper_id == paper_id).order_by(PaperQuestion.question_order).all()
        # 构造DataFrame
        data = []
        for pq in paper_questions:
            q = pq.question
            data.append({
                '题库名称\nquestion_bank_name': q.question_bank.name if q.question_bank else '',
                '试题ID\nquestion_id': q.id,
                '序号\nserial_number': str(pq.question_order) if pq.question_order else '',
                '认定点代码\nidentification_point_code': '',
                '题型代码\nquestion_type_code': q.question_type_code,
                '题号\nquestion_number': '',
                '试题（题干）\nquestion_stem': q.question_stem,
                '试题（选项 A）\nquestion_option_a': q.option_a,
                '试题（选项 B）\nquestion_option_b': q.option_b,
                '试题（选项 C）\nquestion_option_c': q.option_c,
                '试题（选项 D）\nquestion_option_d': q.option_d,
                '试题（选项 E）\nquestion_option_e': q.option_e,
                '【图】及位置\nimage_and_position': q.image_location,
                '正确答案\ncorrect_answer': q.correct_answer,
                '难度代码\ndifficulty_code': q.difficulty_code,
                '一致性代码\nconsistency_code': q.consistency_code,
                '解析\nexplanation': q.analysis,
                '分值\nscore_value': pq.score
            })
        df = pd.DataFrame(data)
        
        # 创建一个临时文件来存储Excel
        temp_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_export')
        os.makedirs(temp_dir, exist_ok=True)
        temp_file = os.path.join(temp_dir, 'temp_paper.xlsx')
        
        # 使用统一的样式模板
        from excel_exporter import apply_excel_template_style
        wb = apply_excel_template_style(df, temp_file, sheet_name='题库模板')
        
        # 读取文件内容到内存
        with open(temp_file, 'rb') as f:
            output = BytesIO(f.read())
        
        # 删除临时文件
        os.remove(temp_file)
        
        # 返回文件
        output.seek(0)
        paper_name_str = str(paper.name) if paper.name is not None else ''
        safe_paper_name = secure_filename(paper_name_str if paper_name_str else f"试卷_{paper_id}")
        return send_file(
            output,
            as_attachment=True,
            download_name=f'{safe_paper_name}.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        flash(f"导出Excel失败: {e}", "error")
        return redirect(url_for('view_paper', paper_id=paper_id))
    finally:
        close_db(db_session)

@app.route('/import-paper', methods=['GET', 'POST'])
def import_paper():
    """从Excel文件导入试卷"""
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('没有文件部分', 'error')
            return redirect(request.url)
        file = request.files['file']
        if not file or not file.filename:
            flash('未选择文件', 'warning')
            return redirect(request.url)
        
        if allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            
            db_session = get_db()
            try:
                file.save(filepath)
                # 导入试卷
                paper_name = request.form.get('paper_name', '')
                if not paper_name:
                    paper_name = os.path.splitext(filename)[0]  # 使用文件名作为试卷名称
                
                # 创建新试卷
                paper = Paper(
                    name=paper_name,
                    description=request.form.get('description', ''),
                    total_score=float(request.form.get('total_score', 100)),
                    duration=int(request.form.get('duration', 120)),
                    difficulty_level=request.form.get('difficulty_level', '中等')
                )
                db_session.add(paper)
                db_session.flush()  # 获取paper.id
                
                # 读取Excel文件
                import pandas as pd
                try:
                    # 使用header=[0, 1]读取两行表头
                    df = pd.read_excel(filepath, dtype=str, engine='openpyxl', header=[0, 1])
                    df = df.fillna('')
                    
                    # 处理多级表头，合并中英文列名
                    if isinstance(df.columns, pd.MultiIndex):
                        new_columns = []
                        for col in df.columns:
                            if pd.isna(col[1]) or col[1] == '' or 'Unnamed:' in str(col[1]):
                                # 如果第二行为空或是Unnamed，只使用第一行
                                new_columns.append(col[0])
                            else:
                                # 合并两行，格式为"中文\n英文"
                                new_columns.append(f"{col[0]}\n{col[1]}")
                        df.columns = new_columns
                    
                    # 过滤掉第一行数据（可能是列名重复）
                    if not df.empty:
                        first_row = df.iloc[0]
                        # 检查第一行是否包含列名信息（中英文都检查）
                        invalid_values = ['question_bank_name', 'question_id', 'question_type_code', 
                                        '题库名称', '试题ID', '题型代码', 'bank_name', 'question_content', '题目内容']
                        # 检查第一行的所有值
                        first_row_values = [str(val).strip().lower() for val in first_row.values if str(val).strip()]
                        invalid_values_lower = [v.lower() for v in invalid_values]
                        
                        if any(val in invalid_values_lower for val in first_row_values):
                            df = df.iloc[1:].reset_index(drop=True)
                            print(f"检测到并跳过了第一行数据（包含列名信息）: {first_row_values}")
                    
                    # 标准化列名
                    standardized_columns = {}
                    for col in df.columns:
                        for std_col, aliases in EXPECTED_COLUMNS.items():
                            if col in aliases:
                                standardized_columns[col] = std_col
                                break
                    
                    # 重命名列
                    df = df.rename(columns=standardized_columns)
                    
                    # 导入题目
                    question_count = 0
                    for idx, row in df.iterrows():
                        question_id = row.get('试题ID\nQuestion ID', '')
                        if not question_id:
                            continue
                        
                        # 查找题目
                        question = db_session.query(Question).filter(Question.id == question_id).first()
                        if not question:
                            # 如果题目不存在，创建新题目
                            bank_name = row.get('题库名称\nQuestion Bank Name', '导入试卷题库')
                            # 查找或创建题库
                            bank = db_session.query(QuestionBank).filter(QuestionBank.题库名称 == bank_name).first()
                            if not bank:
                                bank = QuestionBank(题库名称=bank_name)
                                db_session.add(bank)
                                db_session.flush()
                            
                            # 创建新题目
                            question = Question(
                                id=question_id,
                                question_bank_id=bank.id,
                                question_type_code=row.get('题型代码\nQuestion Type Code', ''),
                                stem=row.get('试题（题干）\nQuestion Stem', ''),
                                option_a=row.get('试题（选项A）\nOption A', ''),
                                option_b=row.get('试题（选项B）\nOption B', ''),
                                option_c=row.get('试题（选项C）\nOption C', ''),
                                option_d=row.get('试题（选项D）\nOption D', ''),
                                option_e=row.get('试题（选项E）\nOption E', ''),
                                image_info=row.get('【图】及位置\nImage Location', ''),
                                correct_answer=row.get('正确答案\nCorrect Answer', ''),
                                difficulty_code=row.get('难度代码\nDifficulty Code', '3'),
                                consistency_code=row.get('一致性代码\nConsistency Code', ''),
                                analysis=row.get('解析\nAnalysis', '')
                            )
                            db_session.add(question)
                        
                        # 添加到试卷
                        question_order = int(row.get('序号\nSerial Number', question_count + 1))
                        paper_question = PaperQuestion(
                            paper_id=paper.id,
                            question_id=question.id,
                            question_order=question_order,
                            score=5.0,  # 默认分值
                            section_name=''
                        )
                        db_session.add(paper_question)
                        question_count += 1
                    
                    db_session.commit()
                    flash(f'成功导入试卷，包含 {question_count} 道题目！', 'success')
                    return redirect(url_for('view_paper', paper_id=paper.id))
                except Exception as e:
                    db_session.rollback()
                    flash(f'解析Excel文件失败: {e}', 'error')
            except Exception as e:
                flash(f'处理文件时发生错误: {e}', 'error')
            finally:
                close_db(db_session)
            
            return redirect(url_for('papers'))
    
    # GET请求，显示导入表单
    import_paper_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导入试卷</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
        }
        input[type="text"],
        input[type="number"],
        textarea,
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .btn {
            display: inline-block;
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-secondary {
            background: #95a5a6;
        }
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .alert-info {
            background-color: #d9edf7;
            border-color: #bce8f1;
            color: #31708f;
        }
        .alert-warning {
            background-color: #fcf8e3;
            border-color: #faebcc;
            color: #8a6d3b;
        }
        .nav {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .nav a {
            margin: 0 10px;
            text-decoration: none;
            color: #3498db;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📥 导入试卷</h1>
        
        <div class="nav">
            <a href="/">🏠 返回首页</a>
            <a href="/papers">📋 试卷管理</a>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="alert alert-info">
            <p>请上传符合格式的Excel文件，文件结构应与题库导入模板相同。</p>
            <p>您可以先<a href="{{ url_for('download_template') }}" target="_blank">下载题库模板</a>查看格式，或者从<a href="{{ url_for('papers') }}">试卷管理</a>页面导出现有试卷作为参考。</p>
        </div>
        
        <form method="POST" enctype="multipart/form-data">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            
            <div class="form-group">
                <label for="file">选择Excel文件 *</label>
                <input type="file" id="file" name="file" accept=".xlsx,.xls" required>
            </div>
            
            <div class="form-group">
                <label for="paper_name">试卷名称</label>
                <input type="text" id="paper_name" name="paper_name" placeholder="留空将使用文件名">
            </div>
            
            <div class="form-group">
                <label for="description">试卷描述</label>
                <textarea id="description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-group">
                <label for="total_score">总分</label>
                <input type="number" id="total_score" name="total_score" value="100" min="0" step="0.5">
            </div>
            
            <div class="form-group">
                <label for="duration">考试时长（分钟）</label>
                <input type="number" id="duration" name="duration" value="120" min="1">
            </div>
            
            <div class="form-group">
                <label for="difficulty_level">难度等级</label>
                <select id="difficulty_level" name="difficulty_level">
                    <option value="简单">简单</option>
                    <option value="中等" selected>中等</option>
                    <option value="困难">困难</option>
                </select>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <button type="submit" class="btn">📥 导入试卷</button>
                <a href="/" class="btn btn-secondary">取消</a>
            </div>
        </form>
    </div>
</body>
</html>
"""
    return render_template_string(import_paper_template)

@app.route('/export_papers_excel', methods=['POST'])
def export_papers_excel():
    """批量导出多套试卷到一个Excel文件（多Sheet）"""
    import pandas as pd
    from excel_exporter import apply_excel_template_style
    db_session = None
    try:
        db_session = get_db()
        paper_ids = request.form.get('paper_ids', '')
        if not paper_ids:
            flash('未选择试卷', 'error')
            return redirect(url_for('papers'))
        paper_ids = [pid.strip() for pid in paper_ids.split(',') if pid.strip()]
        if not paper_ids:
            flash('未选择试卷', 'error')
            return redirect(url_for('papers'))
        
        # 创建一个临时文件来存储Excel
        temp_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_export')
        os.makedirs(temp_dir, exist_ok=True)
        temp_file = os.path.join(temp_dir, 'temp_papers.xlsx')
        
        # 创建工作簿
        wb = None
        
        for idx, pid in enumerate(paper_ids):
            paper = db_session.query(Paper).filter(Paper.id == pid).first()
            if not paper:
                continue
            paper_questions = db_session.query(PaperQuestion).filter(PaperQuestion.paper_id == pid).order_by(PaperQuestion.question_order).all()
            data = []
            for pq in paper_questions:
                q = pq.question
                data.append({
                    '题库名称\nquestion_bank_name': q.question_bank.name if q.question_bank else '',
                    '试题ID\nquestion_id': q.id,
                    '序号\nserial_number': str(pq.question_order) if pq.question_order else '',
                    '认定点代码\nidentification_point_code': '',
                    '题型代码\nquestion_type_code': q.question_type_code,
                    '题号\nquestion_number': '',
                    '试题（题干）\nquestion_stem': q.question_stem,
                    '试题（选项 A）\nquestion_option_a': q.option_a,
                    '试题（选项 B）\nquestion_option_b': q.option_b,
                    '试题（选项 C）\nquestion_option_c': q.option_c,
                    '试题（选项 D）\nquestion_option_d': q.option_d,
                    '试题（选项 E）\nquestion_option_e': q.option_e,
                    '【图】及位置\nimage_and_position': q.image_location,
                    '正确答案\ncorrect_answer': q.correct_answer,
                    '难度代码\ndifficulty_code': q.difficulty_code,
                    '一致性代码\nconsistency_code': q.consistency_code,
                    '解析\nexplanation': q.analysis,
                    '分值\nscore_value': pq.score
                })
            if data:
                df = pd.DataFrame(data)
                sheet_name = paper.name if paper.name else f"试卷{idx+1}"
                # Excel sheet名不能超过31字符
                sheet_name = sheet_name[:31]
                # 使用统一的样式模板，复用同一个工作簿
                wb = apply_excel_template_style(df, None, sheet_name=sheet_name, workbook=wb)
        
        if wb:
            # 保存到临时文件
            wb.save(temp_file)
            
            # 读取文件内容到内存
            with open(temp_file, 'rb') as f:
                output = BytesIO(f.read())
            
            # 删除临时文件
            os.remove(temp_file)
            
            # 返回文件
            output.seek(0)
            return send_file(
                output,
                as_attachment=True,
                download_name='批量导出试卷.xlsx',
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
        else:
            flash('没有可导出的试卷数据', 'error')
            return redirect(url_for('papers'))
            
    except Exception as e:
        flash(f'批量导出Excel失败: {e}', 'error')
        return redirect(url_for('papers'))
    finally:
        close_db(db_session)

@app.route('/banks', methods=['GET', 'POST'])
def manage_banks():
    """题库管理页面，显示题库列表和处理新增题库"""
    db_session = get_db()
    try:
        if request.method == 'POST':
            bank_name = request.form.get('bank_name', '').strip()
            if not bank_name:
                flash('题库名称不能为空', 'error')
            else:
                # 检查题库名称是否已存在
                existing_bank = db_session.query(QuestionBank).filter_by(题库名称=bank_name).first()
                if existing_bank:
                    flash(f'题库名称 "{bank_name}" 已存在', 'error')
                else:
                    # 创建新题库
                    new_bank = QuestionBank(题库名称=bank_name)
                    db_session.add(new_bank)
                    db_session.commit()
                    flash(f'题库 "{bank_name}" 创建成功', 'success')
        
        # 获取题库列表及其统计信息
        banks = []
        all_banks = db_session.query(QuestionBank).order_by(QuestionBank.题库名称).all()
        print(f"Debug: Found {len(all_banks)} banks in database")
        
        for bank in all_banks:
            try:
                print(f"Debug: Processing bank with id: {bank.id}")
                # 获取该题库的题目数量
                question_count = db_session.query(Question).filter(Question.question_bank_id == bank.id).count()
                print(f"Debug: Question count for bank {bank.id}: {question_count}")
                
                # 构建题库信息字典
                bank_info = {
                    'id': bank.id,
                    'name': bank.题库名称,
                    'question_count': question_count
                }
                banks.append(bank_info)
                print(f"Debug: Added bank {bank.题库名称} with {question_count} questions")
            except Exception as e:
                print(f"Debug: Error processing bank {bank.id}: {e}")
                continue
        
        print(f"Debug: Final banks list length: {len(banks)}")
        print(f"Debug: Banks data: {banks}")
        return render_template_string(banks_template, banks=banks)
    except Exception as e:
        db_session.rollback()
        flash(f'操作失败：{e}', 'error')
        return render_template_string(banks_template, banks=[])
    finally:
        close_db(db_session)

@app.route('/bank/<bank_id>/delete', methods=['POST'])
def delete_bank(bank_id):
    """删除题库及其下所有题目"""
    db_session = get_db()
    try:
        bank = db_session.query(QuestionBank).filter_by(id=bank_id).first()
        if not bank:
            flash('题库不存在。', 'error')
        else:
            db_session.delete(bank)
            db_session.commit()
            flash(f'题库 "{bank.题库名称}" 及其下所有题目已删除。', 'success')
    except Exception as e:
        db_session.rollback()
        flash(f'删除题库失败：{e}', 'error')
    finally:
        close_db(db_session)
    return redirect(url_for('manage_banks'))

@app.route('/bank/<bank_id>/questions', methods=['GET', 'POST'])
def manage_bank_questions(bank_id):
    """管理题库中的试题，包括查看、添加、编辑和删除试题"""
    db_session = get_db()
    try:
        # 查找题库
        bank = db_session.query(QuestionBank).filter_by(id=bank_id).first()
        if not bank:
            flash('题库不存在', 'error')
            return redirect(url_for('manage_banks'))
        
        if request.method == 'POST':
            action = request.form.get('action')
            
            if action == 'add':
                # 添加新试题
                question_type_code = request.form.get('题型代码')
                question_stem = request.form.get('question_stem')
                correct_answer = request.form.get('correct_answer')
                analysis = request.form.get('analysis')
                assessment_code = request.form.get('assessment_code')
                difficulty_code = request.form.get('difficulty_code')
                
                if not all([question_type_code, question_stem]):
                    flash('题型代码和题目内容不能为空', 'error')
                else:
                    new_question = Question(
                        question_bank_id=bank_id,
                        question_type_code=question_type_code,
                        question_stem=question_stem,
                        correct_answer=correct_answer or '',
                        analysis=analysis,
                        assessment_code=assessment_code,
                        difficulty_code=difficulty_code or '3'
                    )
                    db_session.add(new_question)
                    db_session.commit()
                    flash('试题添加成功', 'success')
            
            elif action == 'edit':
                # 编辑试题
                question_id = request.form.get('question_id')
                question = db_session.query(Question).filter_by(id=question_id, question_bank_id=bank_id).first()
                
                if not question:
                    flash('试题不存在', 'error')
                else:
                    question.question_type_code = request.form.get('题型代码')
                    question.question_stem = request.form.get('question_stem')
                    question.correct_answer = request.form.get('correct_answer')
                    question.analysis = request.form.get('analysis')
                    question.assessment_code = request.form.get('assessment_code')
                    question.difficulty_code = request.form.get('difficulty_code')
                    
                    db_session.commit()
                    flash('试题更新成功', 'success')
            
            elif action == 'delete':
                # 删除试题
                question_id = request.form.get('question_id')
                question = db_session.query(Question).filter_by(id=question_id, question_bank_id=bank_id).first()
                
                if not question:
                    flash('试题不存在', 'error')
                else:
                    db_session.delete(question)
                    db_session.commit()
                    flash('试题删除成功', 'success')
        
        # 分页参数
        page = request.args.get('page', 1, type=int)
        per_page = 10  # 每页显示10道题目
        
        # 获取该题库下的试题总数
        total_questions = db_session.query(Question).filter_by(question_bank_id=bank_id).count()
        
        # 计算分页信息
        total_pages = (total_questions + per_page - 1) // per_page
        offset = (page - 1) * per_page
        
        # 获取当前页的试题
        questions = db_session.query(Question).filter_by(question_bank_id=bank_id).offset(offset).limit(per_page).all()
        
        # 分页信息
        pagination = {
            'page': page,
            'per_page': per_page,
            'total': total_questions,
            'total_pages': total_pages,
            'has_prev': page > 1,
            'has_next': page < total_pages,
            'prev_num': page - 1 if page > 1 else None,
            'next_num': page + 1 if page < total_pages else None
        }
        
        return render_template_string(bank_questions_template, bank=bank, questions=questions, pagination=pagination)
    except Exception as e:
        db_session.rollback()
        flash(f'操作失败：{e}', 'error')
        return redirect(url_for('manage_banks'))
    finally:
        close_db(db_session)

@app.route('/bank/<bank_id>/question/<question_id>', methods=['GET'])
def view_question(bank_id, question_id):
    """查看单个试题详情"""
    db_session = get_db()
    try:
        # 查找题库和试题
        bank = db_session.query(QuestionBank).filter_by(id=bank_id).first()
        question = db_session.query(Question).filter_by(id=question_id, question_bank_id=bank_id).first()
        
        if not bank or not question:
            flash('题库或试题不存在', 'error')
            return redirect(url_for('manage_banks'))
        
        return render_template_string(question_detail_template, bank=bank, question=question)
    except Exception as e:
        flash(f'操作失败：{e}', 'error')
        return redirect(url_for('manage_bank_questions', bank_id=bank_id))
    finally:
        close_db(db_session)

@app.route('/export_papers_word', methods=['POST'])
def export_papers_word():
    """批量导出多套试卷到一个Word文件（合并）"""
    from docx import Document
    db_session = None
    try:
        db_session = get_db()
        paper_ids = request.form.get('paper_ids', '')
        if not paper_ids:
            flash('未选择试卷', 'error')
            return redirect(url_for('papers'))
        paper_ids = [pid.strip() for pid in paper_ids.split(',') if pid.strip()]
        if not paper_ids:
            flash('未选择试卷', 'error')
            return redirect(url_for('papers'))
        doc = Document()
        for idx, pid in enumerate(paper_ids):
            generator = PaperGenerator(db_session)
            try:
                sub_doc = generator.export_paper_to_docx(pid)
                sub_doc.seek(0)
                sub = Document(sub_doc)
                if idx > 0:
                    doc.add_page_break()
                for element in sub.element.body:
                    doc.element.body.append(element)
            except Exception as e:
                flash(f'导出试卷 {pid} 失败: {e}', 'error')
        output = BytesIO()
        doc.save(output)
        output.seek(0)
        return send_file(
            output,
            as_attachment=True,
            download_name='批量导出试卷.docx',
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
    except Exception as e:
        flash(f'批量导出Word失败: {e}', 'error')
        return redirect(url_for('papers'))
    finally:
        close_db(db_session)

@app.route('/delete_papers', methods=['POST'])
def delete_papers():
    """批量删除试卷"""
    db_session = None
    try:
        db_session = get_db()
        paper_ids = request.form.get('paper_ids', '')
        if not paper_ids:
            flash('未选择试卷', 'error')
            return redirect(url_for('papers'))
        paper_ids = [pid.strip() for pid in paper_ids.split(',') if pid.strip()]
        if not paper_ids:
            flash('未选择试卷', 'error')
            return redirect(url_for('papers'))
        deleted = 0
        for pid in paper_ids:
            paper = db_session.query(Paper).filter(Paper.id == pid).first()
            if paper:
                db_session.delete(paper)
                deleted += 1
        db_session.commit()
        flash(f'成功删除 {deleted} 套试卷', 'success')
    except Exception as e:
        flash(f'批量删除失败: {e}', 'error')
    finally:
        close_db(db_session)
    return redirect(url_for('papers'))

@app.route('/export-excel', methods=['GET'])
def handle_export_excel():
    """导出题库为Excel文件"""
    db = get_db()
    try:
        # 生成唯一的文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = os.path.join(app.config['UPLOAD_FOLDER'], f'questions_export_{timestamp}.xlsx')
        
        # 导出题库
        count = export_db_questions_to_excel(db, output_path)
        
        if count > 0:
            # 返回文件下载
            return send_file(
                output_path,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f'题库导出_{timestamp}.xlsx'
            )
        else:
            flash("题库中没有题目可导出。", "warning")
            return redirect(url_for('index'))
            
    except Exception as e:
        flash(f"导出题库失败: {e}", "error")
        return redirect(url_for('index'))
    finally:
        close_db(db)

# ==================== 重复题目检测API ====================

# 全局变量存储检测API实例
detection_api = None

def get_detection_api():
    """获取重复检测API实例"""
    global detection_api
    if detection_api is None:
        detection_api = DuplicateDetectionAPI()
    return detection_api

# 已删除重复的start_duplicate_detection函数

# 已删除重复的get_detection_status函数

# 已删除重复的get_detection_results函数

# 已删除重复的handle_duplicate_action函数

# 已删除重复的cancel_detection函数

# 已删除重复的get_detection_history函数

# ==================== 新的重复检测API (匹配模板路径) ====================

@app.route('/api/duplicate-detection/start', methods=['POST'])
@csrf.exempt
def start_duplicate_detection_new():
    """启动重复题目检测任务 (新版本)"""
    try:
        data = request.get_json()
        bank_id = data.get('bank_id')
        threshold = data.get('threshold', 0.8)
        algorithm = data.get('algorithm', 'edit_distance')  # 获取算法类型
        
        if not bank_id:
            return jsonify({'success': False, 'message': '请提供题库ID'}), 400
            
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建检测任务
        detection_tasks[task_id] = {
            'status': 'running',
            'progress': 0,
            'message': '正在检测重复题目...',
            'bank_id': bank_id,
            'threshold': threshold,
            'algorithm': algorithm,  # 保存算法类型
            'created_at': datetime.datetime.now()
        }
        
        # 启动后台检测任务
        threading.Thread(target=simulate_detection_task, args=(task_id, bank_id, threshold)).start()
        
        return jsonify({
            'success': True,
            'task_id': task_id,
            'message': '重复检测任务已启动'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/duplicate-detection/status/<task_id>', methods=['GET'])
@csrf.exempt
def get_duplicate_detection_status(task_id):
    """获取重复检测任务状态"""
    try:
        if task_id not in detection_tasks:
            return jsonify({'error': '任务不存在'}), 404
            
        task = detection_tasks[task_id]
        return jsonify({
            'status': task['status'],
            'progress': task['progress'],
            'message': task['message']
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/duplicate-detection/results/<task_id>', methods=['GET'])
@csrf.exempt
def get_duplicate_detection_results(task_id):
    """获取重复检测结果"""
    try:
        if task_id not in detection_results:
            return jsonify({'success': False, 'message': '结果不存在或任务未完成'}), 404
            
        results = detection_results[task_id]
        return jsonify({
            'success': True,
            'results': results
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/duplicate-detection/action', methods=['POST'])
@csrf.exempt
def handle_duplicate_detection_action():
    """处理重复题目操作"""
    try:
        data = request.get_json()
        action = data.get('action')
        question_ids = data.get('question_ids', [])
        
        if not action or not question_ids:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
            
        db = get_db()
        try:
            if action == 'delete':
                # 删除指定的题目
                deleted_count = 0
                for question_id in question_ids:
                    question = db.query(Question).filter(Question.id == question_id).first()
                    if question:
                        db.delete(question)
                        deleted_count += 1
                
                db.commit()
                return jsonify({
                    'success': True,
                    'message': f'成功删除 {deleted_count} 道题目'
                })
            else:
                return jsonify({'success': False, 'message': '不支持的操作类型'}), 400
                
        finally:
            close_db(db)
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/duplicate-detection/cancel/<task_id>', methods=['POST'])
@csrf.exempt
def cancel_duplicate_detection(task_id):
    """取消重复检测任务"""
    try:
        if task_id in detection_tasks:
            detection_tasks[task_id]['status'] = 'cancelled'
            detection_tasks[task_id]['message'] = '任务已取消'
            return jsonify({'success': True, 'message': '任务已取消'})
        else:
            return jsonify({'success': False, 'message': '任务不存在'}), 404
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

def simulate_detection_task(task_id, bank_id, threshold):
    """模拟重复检测任务"""
    try:
        import time
        
        # 模拟检测过程
        for progress in range(0, 101, 10):
            if task_id not in detection_tasks or detection_tasks[task_id]['status'] == 'cancelled':
                return
                
            detection_tasks[task_id]['progress'] = progress
            detection_tasks[task_id]['message'] = f'检测进度: {progress}%'
            time.sleep(0.5)
        
        # 模拟检测结果
        db = get_db()
        try:
            # 获取题库中的题目进行简单的重复检测
            questions = db.query(Question).filter(Question.question_bank_id == bank_id).limit(10).all()
            
            # 简单的重复检测逻辑：查找题干相似的题目
            duplicate_groups = []
            processed_questions = set()
            
            for i, q1 in enumerate(questions):
                if q1.id in processed_questions:
                    continue
                    
                similar_questions = [q1]
                processed_questions.add(q1.id)
                
                for j, q2 in enumerate(questions[i+1:], i+1):
                    if q2.id in processed_questions:
                        continue
                        
                    # 使用指定算法进行文本相似度检测
                    if q1.question_stem and q2.question_stem:
                        # 从任务参数中获取算法类型，默认为编辑距离
                        algorithm = detection_tasks[task_id].get('algorithm', 'edit_distance')
                        similarity = calculate_similarity(q1.question_stem, q2.question_stem, algorithm)
                        if similarity >= threshold:
                            similar_questions.append(q2)
                            processed_questions.add(q2.id)
                
                if len(similar_questions) > 1:
                    # 计算组内最高相似度
                    max_similarity = 0.0
                    algorithm = detection_tasks[task_id].get('algorithm', 'edit_distance')
                    
                    for i in range(len(similar_questions)):
                        for j in range(i + 1, len(similar_questions)):
                            sim = calculate_similarity(
                                similar_questions[i].question_stem, 
                                similar_questions[j].question_stem, 
                                algorithm
                            )
                            max_similarity = max(max_similarity, sim)
                    
                    group = {
                        'similarity': round(max_similarity, 3),  # 真实相似度
                        'algorithm': algorithm,
                        'questions': []
                    }
                    
                    for q in similar_questions:
                        group['questions'].append({
                            'id': q.id,
                            'question_stem': q.question_stem[:100] + '...' if len(q.question_stem) > 100 else q.question_stem,
                            'correct_answer': q.correct_answer or '',
                            'difficulty': getattr(q, 'difficulty', '未知'),
                            'question_type': getattr(q, 'question_type', '未知')
                        })
                    
                    duplicate_groups.append(group)
            
            # 保存结果
            detection_results[task_id] = duplicate_groups
            detection_tasks[task_id]['status'] = 'completed'
            detection_tasks[task_id]['progress'] = 100
            detection_tasks[task_id]['message'] = f'检测完成，发现 {len(duplicate_groups)} 组重复题目'
            
        finally:
            close_db(db)
            
    except Exception as e:
        if task_id in detection_tasks:
            detection_tasks[task_id]['status'] = 'failed'
            detection_tasks[task_id]['message'] = f'检测失败: {str(e)}'

def calculate_edit_distance(text1, text2):
    """计算编辑距离相似度"""
    if not text1 or not text2:
        return 0.0
    
    text1, text2 = text1.lower().strip(), text2.lower().strip()
    if text1 == text2:
        return 1.0
    
    # 动态规划计算编辑距离
    m, n = len(text1), len(text2)
    dp = [[0] * (n + 1) for _ in range(m + 1)]
    
    for i in range(m + 1):
        dp[i][0] = i
    for j in range(n + 1):
        dp[0][j] = j
    
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if text1[i-1] == text2[j-1]:
                dp[i][j] = dp[i-1][j-1]
            else:
                dp[i][j] = 1 + min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])
    
    max_len = max(m, n)
    return 1.0 - (dp[m][n] / max_len) if max_len > 0 else 0.0

def calculate_cosine_similarity(text1, text2):
    """计算余弦相似度"""
    if not text1 or not text2:
        return 0.0
    
    import re
    import math
    from collections import Counter
    
    # 分词处理
    def tokenize(text):
        return re.findall(r'\w+', text.lower())
    
    tokens1 = tokenize(text1)
    tokens2 = tokenize(text2)
    
    if not tokens1 or not tokens2:
        return 0.0
    
    # 计算词频向量
    counter1 = Counter(tokens1)
    counter2 = Counter(tokens2)
    
    # 获取所有唯一词汇
    all_words = set(counter1.keys()) | set(counter2.keys())
    
    # 构建向量
    vector1 = [counter1.get(word, 0) for word in all_words]
    vector2 = [counter2.get(word, 0) for word in all_words]
    
    # 计算余弦相似度
    dot_product = sum(a * b for a, b in zip(vector1, vector2))
    magnitude1 = math.sqrt(sum(a * a for a in vector1))
    magnitude2 = math.sqrt(sum(a * a for a in vector2))
    
    if magnitude1 == 0 or magnitude2 == 0:
        return 0.0
    
    return dot_product / (magnitude1 * magnitude2)

def calculate_jaccard_similarity(text1, text2):
    """计算Jaccard相似度"""
    if not text1 or not text2:
        return 0.0
    
    import re
    
    # 分词处理
    def tokenize(text):
        return set(re.findall(r'\w+', text.lower()))
    
    tokens1 = tokenize(text1)
    tokens2 = tokenize(text2)
    
    if not tokens1 or not tokens2:
        return 0.0
    
    # 计算交集和并集
    intersection = tokens1 & tokens2
    union = tokens1 | tokens2
    
    return len(intersection) / len(union) if union else 0.0

def calculate_similarity(text1, text2, algorithm='edit_distance'):
    """根据指定算法计算文本相似度"""
    if algorithm == 'edit_distance':
        return calculate_edit_distance(text1, text2)
    elif algorithm == 'cosine':
        return calculate_cosine_similarity(text1, text2)
    elif algorithm == 'jaccard':
        return calculate_jaccard_similarity(text1, text2)
    else:
        return calculate_edit_distance(text1, text2)  # 默认使用编辑距离

# ==================== 备用题目管理API ====================

# 全局变量存储备用管理实例
backup_manager = None

def get_backup_manager():
    """获取备用题目管理实例"""
    global backup_manager
    if backup_manager is None:
        backup_manager = BackupQuestionManager()
    return backup_manager

# 备用题目轮换规则API已移至 /api/backup-management/rules
# 备用题目状态API已移至 /api/backup-management/status/<bank_id>

# ==================== 批量编辑API ====================

# 全局变量存储批量编辑管理实例
edit_manager = None

def get_edit_manager():
    """获取批量编辑管理实例"""
    global edit_manager
    if edit_manager is None:
        edit_manager = BatchEditManager()
    return edit_manager

# 旧的批量编辑API已移至 /api/batch-edit/*

# ==================== 前端界面路由 ====================

@app.route('/api/duplicate-detection/<bank_id>/interface')
def duplicate_detection_interface(bank_id):
    """重复题目检测界面"""
    try:
        # 获取题库信息
        db = get_db()
        try:
            bank = db.query(QuestionBank).filter(QuestionBank.id == bank_id).first()
            if not bank:
                flash(f"题库不存在: {bank_id}", "error")
                return redirect(url_for('manage_bank_questions', bank_id=bank_id))
            return render_template_string(duplicate_detection_template, bank=bank)
        finally:
            close_db(db)
    except Exception as e:
        print(f"重复检测界面错误: {e}")  # 添加调试信息
        flash(f"加载重复检测界面失败: {e}", "error")
        return redirect(url_for('manage_bank_questions', bank_id=bank_id))

@app.route('/api/backup-management/<bank_id>/interface')
def backup_management_interface(bank_id):
    """备用题目管理界面"""
    try:
        # 获取题库信息
        bank = QuestionBank.query.get_or_404(bank_id)
        return render_template_string(backup_management_template, bank=bank)
    except Exception as e:
        flash(f"加载备用题目管理界面失败: {e}", "error")
        return redirect(url_for('manage_bank_questions', bank_id=bank_id))

@app.route('/api/backup-management/status/<bank_id>')
def get_backup_status(bank_id):
    """获取题库备用题目状态"""
    db_session = None
    try:
        db_session = get_db()
        bank = db_session.query(QuestionBank).filter_by(id=bank_id).first()
        if not bank:
            return jsonify({'success': False, 'message': '题库不存在'}), 404
        
        questions = db_session.query(Question).filter_by(question_bank_id=bank_id).all()
        
        total_questions = len(questions)
        # 处理status字段可能为None的情况，默认为'active'
        active_questions = len([q for q in questions if (q.status or 'active') == 'active'])
        backup_questions = len([q for q in questions if (q.status or 'active') == 'backup'])
        archived_questions = len([q for q in questions if (q.status or 'active') == 'archived'])
        
        return jsonify({
            'success': True,
            'status': {
                'total_questions': total_questions,
                'active_questions': active_questions,
                'backup_questions': backup_questions,
                'archived_questions': archived_questions
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500
    finally:
        if db_session:
            close_db(db_session)

@app.route('/api/backup-management/rules', methods=['GET', 'POST', 'DELETE'])
@csrf.exempt
def manage_backup_rules():
    """管理备用题目轮换规则"""
    db_session = None
    try:
        db_session = get_db()
        
        if request.method == 'GET':
            bank_id = request.args.get('bank_id')
            if not bank_id:
                return jsonify({'success': False, 'message': '缺少bank_id参数'}), 400
            
            # 从数据库获取轮换规则
            rules = db_session.query(BackupRule).filter_by(question_bank_id=bank_id).all()
            rules_data = []
            for rule in rules:
                rules_data.append({
                    'id': rule.id,
                    'question_bank_id': rule.question_bank_id,
                    'rule_name': rule.rule_name,
                    'standard_count': rule.standard_count,
                    'total_count': rule.total_count,
                    'rotation_percentage': rule.rotation_percentage,
                    'rotation_strategy': rule.rotation_strategy,
                    'rotation_frequency': rule.rotation_frequency,
                    'is_active': rule.is_active,
                    'created_at': rule.created_at.isoformat() if rule.created_at else None,
                    'last_rotation_date': rule.last_rotation_date.isoformat() if rule.last_rotation_date else None
                })
            
            return jsonify({'success': True, 'rules': rules_data})
        
        elif request.method == 'POST':
            data = request.get_json()
            bank_id = data.get('bank_id')
            standard_count = data.get('standard_count')
            rotation_percentage = data.get('rotation_percentage')
            rotation_strategy = data.get('rotation_strategy')
            rotation_frequency = data.get('rotation_frequency', 'manual')
            
            if not all([bank_id, standard_count, rotation_percentage, rotation_strategy]):
                return jsonify({'success': False, 'message': '缺少必要参数'}), 400
            
            # 验证题库是否存在
            bank = db_session.query(QuestionBank).filter_by(id=bank_id).first()
            if not bank:
                return jsonify({'success': False, 'message': '题库不存在'}), 404
            
            # 创建新的轮换规则
            new_rule = BackupRule(
                question_bank_id=bank_id,
                rule_name=f'轮换规则_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}',
                standard_count=standard_count,
                total_count=standard_count + int(standard_count * rotation_percentage / 100),
                rotation_percentage=rotation_percentage,
                rotation_strategy=rotation_strategy,
                rotation_frequency='manual',
                is_active=True
            )
            
            db_session.add(new_rule)
            db_session.commit()
            
            return jsonify({
                'success': True, 
                'rule': {
                    'id': new_rule.id,
                    'question_bank_id': new_rule.question_bank_id,
                    'rule_name': new_rule.rule_name,
                    'standard_count': new_rule.standard_count,
                    'total_count': new_rule.total_count,
                    'rotation_percentage': new_rule.rotation_percentage,
                    'rotation_strategy': new_rule.rotation_strategy,
                    'rotation_frequency': new_rule.rotation_frequency,
                    'is_active': new_rule.is_active,
                    'created_at': new_rule.created_at.isoformat()
                }, 
                'message': '轮换规则创建成功'
            })
        
        elif request.method == 'DELETE':
            rule_id = request.args.get('rule_id')
            if not rule_id:
                return jsonify({'success': False, 'message': '缺少rule_id参数'}), 400
            
            # 删除轮换规则
            rule = db_session.query(BackupRule).filter_by(id=rule_id).first()
            if not rule:
                return jsonify({'success': False, 'message': '规则不存在'}), 404
            
            db_session.delete(rule)
            db_session.commit()
            
            return jsonify({'success': True, 'message': f'规则 {rule_id} 删除成功'})
    
    except Exception as e:
        if db_session:
            db_session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500
    finally:
        if db_session:
            close_db(db_session)

@app.route('/api/backup-management/execute/<rule_id>', methods=['POST'])
@csrf.exempt
def execute_backup_rule(rule_id):
    """执行备用题目轮换规则"""
    db_session = None
    try:
        db_session = get_db()
        
        # 获取轮换规则
        rule = db_session.query(BackupRule).filter_by(id=rule_id).first()
        if not rule:
            return jsonify({'success': False, 'message': '轮换规则不存在'}), 404
        
        if not rule.is_active:
            return jsonify({'success': False, 'message': '轮换规则已禁用'}), 400
        
        # 获取题库中的题目
        questions = db_session.query(Question).filter_by(question_bank_id=rule.question_bank_id).all()
        if not questions:
            return jsonify({'success': False, 'message': '题库中没有题目'}), 400
        
        # 计算需要轮换的题目数量
        rotation_count = int(len(questions) * rule.rotation_percentage / 100)
        if rotation_count == 0:
            return jsonify({'success': False, 'message': '轮换比例过小，没有题目需要轮换'}), 400
        
        # 根据轮换策略选择题目
        import random
        rotated_questions = []
        
        if rule.rotation_strategy == 'random':
            # 随机选择题目进行轮换
            active_questions = [q for q in questions if q.status == 'active']
            backup_questions = [q for q in questions if q.status == 'backup']
            
            # 将一些活跃题目设为备用
            if len(active_questions) > rule.standard_count:
                to_backup = random.sample(active_questions, min(rotation_count, len(active_questions) - rule.standard_count))
                for q in to_backup:
                    q.status = 'backup'
                    rotated_questions.append({'id': q.id, 'action': 'moved_to_backup'})
            
            # 将一些备用题目设为活跃
            if len(backup_questions) > 0:
                to_active = random.sample(backup_questions, min(rotation_count, len(backup_questions)))
                for q in to_active:
                    q.status = 'active'
                    rotated_questions.append({'id': q.id, 'action': 'moved_to_active'})
        
        elif rule.rotation_strategy == 'difficulty_based':
            # 基于难度平衡进行轮换
            active_questions = [q for q in questions if q.status == 'active']
            backup_questions = [q for q in questions if q.status == 'backup']
            
            # 统计活跃题目的难度分布
            difficulty_counts = {'easy': 0, 'medium': 0, 'hard': 0}
            for q in active_questions:
                if q.difficulty in difficulty_counts:
                    difficulty_counts[q.difficulty] += 1
            
            # 找出需要平衡的难度
            target_per_difficulty = rule.standard_count // 3
            for difficulty in ['easy', 'medium', 'hard']:
                current_count = difficulty_counts[difficulty]
                if current_count > target_per_difficulty:
                    # 移除多余的题目到备用
                    excess_questions = [q for q in active_questions if q.difficulty == difficulty]
                    to_backup = random.sample(excess_questions, min(current_count - target_per_difficulty, len(excess_questions)))
                    for q in to_backup:
                        q.status = 'backup'
                        rotated_questions.append({'id': q.id, 'action': 'moved_to_backup', 'reason': f'difficulty_balance_{difficulty}'})
                elif current_count < target_per_difficulty:
                    # 从备用中添加题目
                    available_backup = [q for q in backup_questions if q.difficulty == difficulty]
                    to_active = random.sample(available_backup, min(target_per_difficulty - current_count, len(available_backup)))
                    for q in to_active:
                        q.status = 'active'
                        rotated_questions.append({'id': q.id, 'action': 'moved_to_active', 'reason': f'difficulty_balance_{difficulty}'})
        
        # 更新规则的最后执行时间
        rule.last_rotation_date = datetime.datetime.now()
        
        # 创建执行记录
        execution_record = RotationHistory(
            backup_rule_id=rule_id,
            rotation_date=datetime.datetime.now(),
            questions_rotated_in=[q['id'] for q in rotated_questions if q['action'] == 'moved_to_active'],
            questions_rotated_out=[q['id'] for q in rotated_questions if q['action'] == 'moved_to_backup'],
            rotation_count=len(rotated_questions),
            strategy_used=rule.rotation_strategy,
            execution_status='completed',
            execution_details={'rotated_questions': rotated_questions, 'strategy': rule.rotation_strategy}
        )
        
        db_session.add(execution_record)
        db_session.commit()
        
        return jsonify({
            'success': True,
            'execution': {
                'id': execution_record.id,
                'rule_id': rule_id,
                'executed_at': execution_record.rotation_date.isoformat(),
                'rotated_count': len(rotated_questions),
                'status': 'completed',
                'details': execution_record.execution_details,
                'rotated_questions': rotated_questions
            },
            'message': f'轮换规则执行成功，共轮换了 {len(rotated_questions)} 道题目'
        })
    
    except Exception as e:
        if db_session:
            db_session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500
    finally:
        if db_session:
            close_db(db_session)

@app.route('/api/backup-management/history')
def get_backup_history():
    """获取备用题目轮换历史"""
    db_session = None
    try:
        db_session = get_db()
        
        bank_id = request.args.get('bank_id')
        if not bank_id:
            return jsonify({'success': False, 'message': '缺少bank_id参数'}), 400
        
        # 验证题库是否存在
        bank = db_session.query(QuestionBank).filter_by(id=bank_id).first()
        if not bank:
            return jsonify({'success': False, 'message': '题库不存在'}), 404
        
        # 获取该题库的轮换规则
        rules = db_session.query(BackupRotationRule).filter_by(bank_id=bank_id).all()
        rule_ids = [rule.id for rule in rules]
        
        if not rule_ids:
            return jsonify({'success': True, 'history': []})
        
        # 获取执行历史记录
        executions = db_session.query(BackupRotationExecution).filter(
            BackupRotationExecution.rule_id.in_(rule_ids)
        ).order_by(BackupRotationExecution.executed_at.desc()).limit(50).all()
        
        history = []
        for execution in executions:
            # 获取对应的规则信息
            rule = db_session.query(BackupRotationRule).filter_by(id=execution.rule_id).first()
            
            history.append({
                'id': execution.id,
                'rule_id': execution.rule_id,
                'executed_at': execution.executed_at.isoformat() if execution.executed_at else None,
                'rotation_strategy': rule.rotation_strategy if rule else 'unknown',
                'rotated_count': execution.rotated_count,
                'execution_status': execution.execution_status,
                'details': execution.details
            })
        
        return jsonify({'success': True, 'history': history})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500
    finally:
        if db_session:
            close_db(db_session)

@app.route('/api/batch-edit/<bank_id>/interface')
def batch_edit_interface(bank_id):
    """批量编辑界面"""
    db_session = None
    try:
        db_session = get_db()
        # 获取题库信息
        bank = db_session.query(QuestionBank).filter_by(id=bank_id).first()
        if not bank:
            flash("题库不存在", "error")
            return redirect(url_for('index'))
        # 获取题库中的所有题目
        questions = db_session.query(Question).filter_by(question_bank_id=bank_id).all()
        return render_template_string(batch_edit_template, bank=bank, questions=questions)
    except Exception as e:
        flash(f"加载批量编辑界面失败: {e}", "error")
        return redirect(url_for('manage_bank_questions', bank_id=bank_id))
    finally:
        if db_session:
            close_db(db_session)

# 全局变量存储批量编辑会话
batch_edit_sessions = {}

@app.route('/api/batch-edit/session', methods=['POST'])
@csrf.exempt
def create_batch_edit_session():
    """创建批量编辑会话"""
    db_session = None
    try:
        db_session = get_db()
        data = request.get_json()
        bank_id = data.get('bank_id')
        question_ids = data.get('question_ids', [])
        
        if not bank_id or not question_ids:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
        
        # 验证题库和题目是否存在
        bank = db_session.query(QuestionBank).filter_by(id=bank_id).first()
        if not bank:
            return jsonify({'success': False, 'message': '题库不存在'}), 404
            
        questions = db_session.query(Question).filter(
            Question.question_bank_id == bank_id,
            Question.id.in_(question_ids)
        ).all()
        
        if len(questions) != len(question_ids):
            return jsonify({'success': False, 'message': '部分题目不存在'}), 400
        
        # 创建会话
        session_id = str(uuid.uuid4())
        batch_edit_sessions[session_id] = {
            'bank_id': bank_id,
            'question_ids': question_ids,
            'questions': [{
                'id': q.id,
                'question_text': q.question_stem,
                'options': {
                    'A': q.option_a,
                    'B': q.option_b,
                    'C': q.option_c,
                    'D': q.option_d,
                    'E': q.option_e
                },
                'correct_answer': q.correct_answer,
                'difficulty': q.difficulty_code,
                'question_type': q.question_type_code
            } for q in questions],
            'created_at': datetime.datetime.now().isoformat(),
            'changes': []
        }
        
        return jsonify({
            'success': True,
            'session_id': session_id,
            'question_count': len(questions),
            'message': f'编辑会话创建成功，包含 {len(questions)} 道题目'
        })
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500
    finally:
        if db_session:
            close_db(db_session)

@app.route('/api/batch-edit/preview', methods=['POST'])
@csrf.exempt
def preview_batch_changes():
    """预览批量编辑变更"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        operation = data.get('operation')
        params = data.get('params', {})
        
        if not session_id or session_id not in batch_edit_sessions:
            return jsonify({'success': False, 'message': '无效的会话ID'}), 400
        
        session = batch_edit_sessions[session_id]
        questions = session['questions']
        preview_changes = []
        
        # 根据操作类型生成预览
        if operation == 'reorder':
            order_type = params.get('order', 'alphabetical')
            if order_type == 'alphabetical':
                preview_changes.append({
                    'question_id': 'all',
                    'change_type': 'modify',
                    'field': '题目顺序',
                    'old_value': '原始顺序',
                    'new_value': '按字母顺序排列'
                })
        
        elif operation == 'shuffle':
            preview_changes.append({
                'question_id': 'all',
                'change_type': 'modify',
                'field': '题目顺序',
                'old_value': '原始顺序',
                'new_value': '随机打乱'
            })
        
        elif operation == 'replace_text':
            search_text = params.get('search_text', '')
            replace_text = params.get('replace_text', '')
            target_field = params.get('target_field', 'question_stem')
            
            for question in questions:
                # 映射字段名到会话中的字段名
                if target_field == 'question_stem':
                    field_value = question.get('question_text', '')
                else:
                    field_value = question.get(target_field, '')
                    
                if search_text in field_value:
                    preview_changes.append({
                        'question_id': question['id'],
                        'change_type': 'modify',
                        'field': target_field,
                        'old_value': field_value,
                        'new_value': field_value.replace(search_text, replace_text)
                    })
        
        elif operation == 'update_difficulty':
            new_difficulty = params.get('new_difficulty')
            for question in questions:
                if question['difficulty'] != new_difficulty:
                    preview_changes.append({
                        'question_id': question['id'],
                        'change_type': 'modify',
                        'field': 'difficulty',
                        'old_value': question['difficulty'],
                        'new_value': new_difficulty
                    })
        
        # 保存变更到会话
        session['changes'] = preview_changes
        session['operation'] = operation
        session['params'] = params
        
        return jsonify({
            'success': True,
            'preview': preview_changes,
            'change_count': len(preview_changes)
        })
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/batch-edit/apply', methods=['POST'])
@csrf.exempt
def apply_batch_changes():
    """应用批量编辑变更"""
    db_session = None
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        
        if not session_id or session_id not in batch_edit_sessions:
            return jsonify({'success': False, 'message': '无效的会话ID'}), 400
        
        session = batch_edit_sessions[session_id]
        changes = session.get('changes', [])
        operation = session.get('operation')
        params = session.get('params', {})
        
        if not changes:
            return jsonify({'success': False, 'message': '没有待应用的变更'}), 400
        
        # 获取数据库会话
        db_session = get_db()
        modified_count = 0
        
        # 应用变更到数据库
        if operation == 'replace_text':
            search_text = params.get('search_text', '')
            replace_text = params.get('replace_text', '')
            target_field = params.get('target_field', 'question_stem')
            
            for change in changes:
                if change['question_id'] != 'all':
                    question = db_session.query(Question).filter(Question.id == change['question_id']).first()
                    if question:
                        current_value = getattr(question, target_field, '')
                        new_value = current_value.replace(search_text, replace_text)
                        setattr(question, target_field, new_value)
                        modified_count += 1
        
        elif operation == 'update_difficulty':
            new_difficulty = params.get('new_difficulty')
            for change in changes:
                if change['question_id'] != 'all':
                    question = db_session.query(Question).filter(Question.id == change['question_id']).first()
                    if question:
                        question.difficulty_code = new_difficulty
                        modified_count += 1
        
        # 提交数据库变更
        try:
            db_session.commit()
        except Exception as e:
            db_session.rollback()
            return jsonify({'success': False, 'message': f'数据库更新失败: {str(e)}'}), 500
        
        # 清理会话
        del batch_edit_sessions[session_id]
        
        return jsonify({
            'success': True,
            'modified_count': modified_count,
            'message': f'成功修改了 {modified_count} 道题目'
        })
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500
    finally:
        if db_session:
            close_db(db_session)

# ==================== 采用率统计API ====================

# 全局变量存储统计API实例
usage_api = None
usage_algorithms = None

def get_usage_api():
    """获取使用统计API实例"""
    global usage_api
    if usage_api is None:
        usage_api = UsageStatisticsAPI()
    return usage_api

def get_usage_algorithms():
    """获取使用统计算法实例"""
    global usage_algorithms
    if usage_algorithms is None:
        usage_algorithms = UsageStatisticsAlgorithms()
    return usage_algorithms

@app.route('/usage-statistics')
def usage_statistics_page():
    """采用率统计主页面"""
    db_session = None
    try:
        print("Debug: Starting usage_statistics_page")
        db_session = get_db()
        # 获取所有题库
        banks = db_session.query(QuestionBank).all()
        print(f"Debug: Found {len(banks)} banks")
        return render_template_string(usage_statistics_template, banks=banks)
    except Exception as e:
        print(f"Error in usage_statistics_page: {e}")
        import traceback
        traceback.print_exc()
        flash(f"加载采用率统计页面失败: {e}", "error")
        return redirect(url_for('index'))
    finally:
        if db_session:
            close_db(db_session)

@app.route('/api/usage/record', methods=['POST'])
@csrf.exempt
def record_question_usage():
    """记录题目使用情况"""
    try:
        data = request.get_json()
        question_id = data.get('question_id')
        paper_id = data.get('paper_id')
        
        if not all([question_id, paper_id]):
            return jsonify({'error': '缺少必要参数'}), 400
            
        api = get_usage_api()
        result = api.record_usage(question_id, paper_id)
        
        return jsonify({
            'success': True,
            'record_id': result,
            'message': '使用记录创建成功'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/usage/batch-record', methods=['POST'])
@csrf.exempt
def batch_record_usage():
    """批量记录题目使用情况"""
    try:
        data = request.get_json()
        usage_records = data.get('usage_records', [])
        
        if not usage_records:
            return jsonify({'error': '缺少使用记录数据'}), 400
            
        api = get_usage_api()
        result = api.batch_record_usage(usage_records)
        
        return jsonify({
            'success': True,
            'recorded_count': result,
            'message': f'成功记录 {result} 条使用记录'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/usage/statistics/<int:question_id>', methods=['GET'])
def get_question_statistics(question_id):
    """获取单个题目的统计信息"""
    db_session = None
    try:
        db_session = get_db()
        
        # 获取题目信息
        question = db_session.query(Question).filter_by(id=question_id).first()
        if not question:
            return jsonify({'error': '题目不存在'}), 404
        
        # 计算总试卷数
        total_papers = db_session.query(Paper).count()
        
        # 计算采用率
        adoption_rate = (question.usage_count / total_papers * 100) if total_papers > 0 else 0
        
        # 获取使用历史（最近10次）
        usage_history = db_session.query(UsageRecord).filter_by(
            question_id=question_id
        ).order_by(UsageRecord.used_at.desc()).limit(10).all()
        
        stats = {
            'usage_count': question.usage_count,
            'adoption_rate': round(adoption_rate, 2),
            'last_used': usage_history[0].used_at.strftime('%Y-%m-%d %H:%M:%S') if usage_history else None,
            'first_used': usage_history[-1].used_at.strftime('%Y-%m-%d %H:%M:%S') if usage_history else None,
            'recent_usage': [{
                'used_at': record.used_at.strftime('%Y-%m-%d %H:%M:%S'),
                'paper_id': record.paper_id
            } for record in usage_history]
        }
        
        return jsonify({
            'question_id': question_id,
            'statistics': stats
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        if db_session:
            close_db(db_session)

@app.route('/api/usage/statistics/<bank_id>', methods=['GET'])
def get_bank_statistics(bank_id):
    """获取题库的统计信息"""
    db_session = None
    try:
        db_session = get_db()
        
        # 验证题库是否存在
        bank = db_session.query(QuestionBank).filter_by(id=bank_id).first()
        if not bank:
            return jsonify({'error': '题库不存在'}), 404
        
        # 获取题库中的题目统计
        questions = db_session.query(Question).filter_by(question_bank_id=bank_id).all()
        total_questions = len(questions)
        
        if total_questions == 0:
            return jsonify({
                'bank_id': bank_id,
                'statistics': {
                    'total_questions': 0,
                    'total_usage': 0,
                    'average_adoption_rate': 0,
                    'most_used_question': None,
                    'least_used_question': None
                }
            })
        
        # 计算总使用次数
        total_usage = sum(q.usage_count for q in questions)
        
        # 计算总试卷数
        total_papers = db_session.query(Paper).count()
        
        # 计算平均采用率
        if total_papers > 0:
            adoption_rates = [(q.usage_count / total_papers * 100) for q in questions]
            average_adoption_rate = sum(adoption_rates) / len(adoption_rates)
        else:
            average_adoption_rate = 0
        
        # 找出使用最多和最少的题目
        most_used = max(questions, key=lambda q: q.usage_count)
        least_used = min(questions, key=lambda q: q.usage_count)
        
        stats = {
            'total_questions': total_questions,
            'total_usage': total_usage,
            'average_adoption_rate': round(average_adoption_rate, 2),
            'most_used_question': {
                 'id': most_used.id,
                 'content': most_used.question_stem[:50] + '...' if len(most_used.question_stem) > 50 else most_used.question_stem,
                 'usage_count': most_used.usage_count
             },
             'least_used_question': {
                 'id': least_used.id,
                 'content': least_used.question_stem[:50] + '...' if len(least_used.question_stem) > 50 else least_used.question_stem,
                 'usage_count': least_used.usage_count
             }
        }
        
        return jsonify({
            'bank_id': bank_id,
            'statistics': stats
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        if db_session:
            close_db(db_session)

@app.route('/api/usage/overview/<bank_id>', methods=['GET'])
def get_bank_usage_overview(bank_id):
    """获取题库使用概览"""
    try:
        api = get_usage_api()
        overview = api.get_bank_overview(bank_id)
        
        return jsonify({
            'bank_id': bank_id,
            'overview': overview
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/usage/report', methods=['POST'])
@csrf.exempt
def generate_usage_report():
    """生成使用统计报告"""
    try:
        data = request.get_json()
        report_type = data.get('report_type', 'overview')
        bank_id = data.get('bank_id')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        api = get_usage_api()
        report = api.generate_report(
            report_type=report_type,
            bank_id=bank_id,
            start_date=start_date,
            end_date=end_date
        )
        
        return jsonify({
            'success': True,
            'report': report
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/questions/<bank_id>', methods=['GET'])
def get_questions_api(bank_id):
    """获取题库中的题目列表API"""
    db_session = None
    try:
        db_session = get_db()
        
        # 验证题库是否存在
        bank = db_session.query(QuestionBank).filter_by(id=bank_id).first()
        if not bank:
            return jsonify({'error': '题库不存在', 'success': False}), 404
        
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        # 查询题目
        query = db_session.query(Question).filter_by(question_bank_id=bank_id)
        total = query.count()
        
        # 分页
        questions_data = query.offset((page - 1) * per_page).limit(per_page).all()
        
        questions = []
        for question in questions_data:
            questions.append({
                'id': question.id,
                'question_stem': question.question_stem,
                'question_type_code': question.question_type_code,
                'correct_answer': question.correct_answer,
                'difficulty_code': question.difficulty_code,
                'assessment_code': question.assessment_code,
                'analysis': question.analysis
            })
        
        # 计算分页信息
        pages = (total + per_page - 1) // per_page
        
        return jsonify({
            'success': True,
            'questions': questions,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': pages,
                'has_prev': page > 1,
                'has_next': page < pages
            }
        })
        
    except Exception as e:
        return jsonify({'error': str(e), 'success': False}), 500
    finally:
        if db_session:
            close_db(db_session)

@app.route('/api/usage/questions/<bank_id>', methods=['GET'])
def get_bank_questions_usage(bank_id):
    """获取题库中所有题目的使用统计"""
    db_session = None
    try:
        db_session = get_db()
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        sort_by = request.args.get('sort_by', 'usage_count')
        order = request.args.get('order', 'desc')
        
        # 获取题库中的题目
        query = db_session.query(Question).filter_by(question_bank_id=bank_id)
        
        # 排序
        if sort_by == 'usage_count':
            if order == 'desc':
                query = query.order_by(Question.usage_count.desc())
            else:
                query = query.order_by(Question.usage_count.asc())
        elif sort_by == 'question_id':
            if order == 'desc':
                query = query.order_by(Question.id.desc())
            else:
                query = query.order_by(Question.id.asc())
        
        # 获取总数和分页数据
        total = query.count()
        questions_data = query.offset((page - 1) * per_page).limit(per_page).all()
        
        # 计算总试卷数
        total_papers = db_session.query(Paper).count()
        
        questions = []
        for question in questions_data:
            # 计算采用率
            adoption_rate = (question.usage_count / total_papers * 100) if total_papers > 0 else 0
            
            questions.append({
                'question_id': question.id,
                'content': question.question_stem[:100] + '...' if len(question.question_stem) > 100 else question.question_stem,
                'type': question.question_type_code,
                'difficulty': question.difficulty_code,
                'usage_count': question.usage_count,
                'adoption_rate': round(adoption_rate, 2)
            })
        
        # 计算分页信息
        pages = (total + per_page - 1) // per_page
        has_prev = page > 1
        has_next = page < pages
        
        return jsonify({
            'questions': questions,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': pages,
                'has_prev': has_prev,
                'has_next': has_next
            }
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        if db_session:
            close_db(db_session)

# ==================== 采用率统计页面模板 ====================

usage_statistics_template = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题目采用率统计 - 题库管理系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            color: #666;
        }
        .nav-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        .nav-tab:hover:not(.active) {
            background: #f8f9ff;
            color: #667eea;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        .stat-card h3 {
            margin: 0 0 15px 0;
            color: #667eea;
            font-size: 1.2em;
            font-weight: 600;
        }
        .stat-value {
            font-size: 2.5em;
            font-weight: 700;
            color: #333;
            margin: 10px 0;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .chart-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .chart-container h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.4em;
            font-weight: 600;
        }
        .controls {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .controls h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.3em;
            font-weight: 600;
        }
        .form-group {
            display: flex;
            gap: 20px;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .form-group label {
            font-weight: 500;
            color: #555;
            min-width: 100px;
        }
        .form-group select, .form-group input {
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            min-width: 150px;
        }
        .form-group select:focus, .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            padding: 12px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }
        .btn-secondary:hover {
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }
        .questions-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 25px;
        }
        .table-header h3 {
            margin: 0;
            font-size: 1.3em;
            font-weight: 600;
        }
        .table-content {
            padding: 25px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }
        th {
            background: #f8f9ff;
            font-weight: 600;
            color: #555;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background: #e8ecff;
        }
        tr:hover {
            background: #f8f9ff;
        }
        .usage-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 500;
        }
        .usage-high {
            background: #d4edda;
            color: #155724;
        }
        .usage-medium {
            background: #fff3cd;
            color: #856404;
        }
        .usage-low {
            background: #f8d7da;
            color: #721c24;
        }
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 25px;
        }
        .pagination button {
            padding: 8px 15px;
            border: 2px solid #e1e5e9;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .pagination button:hover {
            border-color: #667eea;
            color: #667eea;
        }
        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        .back-link:hover {
            color: #764ba2;
        }
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            .header {
                padding: 20px;
            }
            .header h1 {
                font-size: 2em;
            }
            .stats-grid {
                grid-template-columns: 1fr;
            }
            .form-group {
                flex-direction: column;
                align-items: flex-start;
            }
            .form-group label {
                min-width: auto;
            }
            table {
                font-size: 0.9em;
            }
            th, td {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-link">← 返回主页</a>
        
        <div class="header">
            <h1>📊 题目采用率统计</h1>
            <p>分析题库中题目的使用情况，优化组卷策略</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">📈 统计概览</button>
            <button class="nav-tab" onclick="showTab('questions')">📝 题目列表</button>
            <button class="nav-tab" onclick="showTab('analysis')">🔍 详细分析</button>
            <button class="nav-tab" onclick="showTab('reports')">📋 报告生成</button>
        </div>

        <!-- 统计概览标签页 -->
        <div id="overview" class="tab-content active">
            <div class="controls">
                <h3>选择题库</h3>
                <div class="form-group">
                    <label for="bankSelect">题库：</label>
                    <select id="bankSelect" onchange="loadBankOverview()">
                        <option value="">请选择题库</option>
                        {% for bank in banks %}
                        <option value="{{ bank.题库ID }}">{{ bank.题库名称 }}</option>
                        {% endfor %}
                    </select>
                    <button class="btn" onclick="loadBankOverview()">刷新数据</button>
                </div>
            </div>

            <div id="overviewStats" class="stats-grid" style="display: none;">
                <div class="stat-card">
                    <h3>总题目数</h3>
                    <div class="stat-value" id="totalQuestions">-</div>
                    <div class="stat-label">道题目</div>
                </div>
                <div class="stat-card">
                    <h3>已使用题目</h3>
                    <div class="stat-value" id="usedQuestions">-</div>
                    <div class="stat-label">道题目</div>
                </div>
                <div class="stat-card">
                    <h3>平均采用率</h3>
                    <div class="stat-value" id="avgAdoptionRate">-</div>
                    <div class="stat-label">%</div>
                </div>
                <div class="stat-card">
                    <h3>总试卷数</h3>
                    <div class="stat-value" id="totalPapers">-</div>
                    <div class="stat-label">套试卷</div>
                </div>
            </div>

            <div class="chart-container" style="display: none;" id="overviewCharts">
                <h3>使用情况分布</h3>
                <canvas id="usageDistributionChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- 题目列表标签页 -->
        <div id="questions" class="tab-content">
            <div class="controls">
                <h3>筛选和排序</h3>
                <div class="form-group">
                    <label for="questionBankSelect">题库：</label>
                    <select id="questionBankSelect" onchange="loadQuestionsList()">
                        <option value="">请选择题库</option>
                        {% for bank in banks %}
                        <option value="{{ bank.题库ID }}">{{ bank.题库名称 }}</option>
                        {% endfor %}
                    </select>
                    
                    <label for="sortBy">排序：</label>
                    <select id="sortBy" onchange="loadQuestionsList()">
                        <option value="usage_count">使用次数</option>
                        <option value="question_id">题目ID</option>
                    </select>
                    
                    <label for="sortOrder">顺序：</label>
                    <select id="sortOrder" onchange="loadQuestionsList()">
                        <option value="desc">降序</option>
                        <option value="asc">升序</option>
                    </select>
                    
                    <button class="btn" onclick="loadQuestionsList()">查询</button>
                </div>
            </div>

            <div class="questions-table" id="questionsTable" style="display: none;">
                <div class="table-header">
                    <h3>题目使用统计</h3>
                </div>
                <div class="table-content">
                    <table>
                        <thead>
                            <tr>
                                <th onclick="sortTable('question_id')">题目ID</th>
                                <th onclick="sortTable('content')">题目内容</th>
                                <th onclick="sortTable('type')">题目类型</th>
                                <th onclick="sortTable('difficulty')">难度</th>
                                <th onclick="sortTable('usage_count')">使用次数</th>
                                <th onclick="sortTable('adoption_rate')">采用率</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="questionsTableBody">
                        </tbody>
                    </table>
                    <div class="pagination" id="questionsPagination"></div>
                </div>
            </div>
        </div>

        <!-- 详细分析标签页 -->
        <div id="analysis" class="tab-content">
            <div class="controls">
                <h3>分析配置</h3>
                <div class="form-group">
                    <label for="analysisBankSelect">题库：</label>
                    <select id="analysisBankSelect">
                        <option value="">请选择题库</option>
                        {% for bank in banks %}
                        <option value="{{ bank.题库ID }}">{{ bank.题库名称 }}</option>
                        {% endfor %}
                    </select>
                    
                    <label for="analysisType">分析类型：</label>
                    <select id="analysisType">
                        <option value="trend">使用趋势</option>
                        <option value="type_distribution">题型分布</option>
                        <option value="difficulty_analysis">难度分析</option>
                        <option value="correlation">相关性分析</option>
                    </select>
                    
                    <button class="btn" onclick="performAnalysis()">开始分析</button>
                </div>
            </div>

            <div class="chart-container" id="analysisChart" style="display: none;">
                <h3 id="analysisTitle">分析结果</h3>
                <canvas id="analysisCanvas" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- 报告生成标签页 -->
        <div id="reports" class="tab-content">
            <div class="controls">
                <h3>报告配置</h3>
                <div class="form-group">
                    <label for="reportBankSelect">题库：</label>
                    <select id="reportBankSelect">
                        <option value="">请选择题库</option>
                        {% for bank in banks %}
                        <option value="{{ bank.题库ID }}">{{ bank.题库名称 }}</option>
                        {% endfor %}
                    </select>
                    
                    <label for="reportType">报告类型：</label>
                    <select id="reportType">
                        <option value="overview">概览报告</option>
                        <option value="detailed">详细报告</option>
                        <option value="trend">趋势报告</option>
                        <option value="comparison">对比报告</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="startDate">开始日期：</label>
                    <input type="date" id="startDate">
                    
                    <label for="endDate">结束日期：</label>
                    <input type="date" id="endDate">
                    
                    <button class="btn" onclick="generateReport()">生成报告</button>
                </div>
            </div>

            <div class="chart-container" id="reportResult" style="display: none;">
                <h3>报告结果</h3>
                <div id="reportContent"></div>
                <button class="btn btn-secondary" onclick="downloadReport()">下载报告</button>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let currentBankId = null;
        let currentSort = 'usage_count';
        let currentOrder = 'desc';
        let charts = {};

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有标签按钮的激活状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // 加载题库概览
        async function loadBankOverview() {
            const bankId = document.getElementById('bankSelect').value;
            if (!bankId) {
                document.getElementById('overviewStats').style.display = 'none';
                document.getElementById('overviewCharts').style.display = 'none';
                return;
            }

            try {
                const response = await fetch(`/api/usage/overview/${bankId}`);
                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error);
                }

                const overview = data.overview;
                
                // 更新统计数据
                document.getElementById('totalQuestions').textContent = overview.total_questions || 0;
                document.getElementById('usedQuestions').textContent = overview.used_questions || 0;
                document.getElementById('avgAdoptionRate').textContent = (overview.avg_adoption_rate || 0).toFixed(1);
                document.getElementById('totalPapers').textContent = overview.total_papers || 0;
                
                // 显示统计区域
                document.getElementById('overviewStats').style.display = 'grid';
                document.getElementById('overviewCharts').style.display = 'block';
                
                // 绘制图表
                drawUsageDistributionChart(overview.usage_distribution || {});
                
            } catch (error) {
                console.error('加载概览数据失败:', error);
                alert('加载概览数据失败: ' + error.message);
            }
        }

        // 绘制使用分布图表
        function drawUsageDistributionChart(distribution) {
            const ctx = document.getElementById('usageDistributionChart').getContext('2d');
            
            if (charts.usageDistribution) {
                charts.usageDistribution.destroy();
            }
            
            charts.usageDistribution = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['未使用', '低频使用', '中频使用', '高频使用'],
                    datasets: [{
                        data: [
                            distribution.unused || 0,
                            distribution.low_usage || 0,
                            distribution.medium_usage || 0,
                            distribution.high_usage || 0
                        ],
                        backgroundColor: [
                            '#f8d7da',
                            '#fff3cd',
                            '#d1ecf1',
                            '#d4edda'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // 加载题目列表
        async function loadQuestionsList(page = 1) {
            const bankId = document.getElementById('questionBankSelect').value;
            if (!bankId) {
                document.getElementById('questionsTable').style.display = 'none';
                return;
            }

            const sortBy = document.getElementById('sortBy').value;
            const sortOrder = document.getElementById('sortOrder').value;
            
            currentBankId = bankId;
            currentSort = sortBy;
            currentOrder = sortOrder;
            currentPage = page;

            try {
                const response = await fetch(`/api/usage/questions/${bankId}?page=${page}&sort_by=${sortBy}&order=${sortOrder}`);
                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error);
                }

                // 更新表格内容
                const tbody = document.getElementById('questionsTableBody');
                tbody.innerHTML = '';
                
                data.questions.forEach(question => {
                    const row = document.createElement('tr');
                    
                    // 确定使用频率等级
                    let usageClass = 'usage-low';
                    if (question.usage_count > 10) usageClass = 'usage-high';
                    else if (question.usage_count > 3) usageClass = 'usage-medium';
                    
                    row.innerHTML = `
                        <td>${question.question_id}</td>
                        <td title="${question.content}">${question.content}</td>
                        <td>${question.type || '-'}</td>
                        <td>${question.difficulty || '-'}</td>
                        <td><span class="usage-badge ${usageClass}">${question.usage_count}</span></td>
                        <td>${question.adoption_rate}%</td>
                        <td>
                            <button class="btn" onclick="viewQuestionDetails(${question.question_id})">详情</button>
                        </td>
                    `;
                    
                    tbody.appendChild(row);
                });
                
                // 更新分页
                updatePagination(data.pagination);
                
                // 显示表格
                document.getElementById('questionsTable').style.display = 'block';
                
            } catch (error) {
                console.error('加载题目列表失败:', error);
                alert('加载题目列表失败: ' + error.message);
            }
        }

        // 更新分页
        function updatePagination(pagination) {
            const container = document.getElementById('questionsPagination');
            container.innerHTML = '';
            
            // 上一页按钮
            if (pagination.has_prev) {
                const prevBtn = document.createElement('button');
                prevBtn.textContent = '上一页';
                prevBtn.onclick = () => loadQuestionsList(pagination.page - 1);
                container.appendChild(prevBtn);
            }
            
            // 页码按钮
            const startPage = Math.max(1, pagination.page - 2);
            const endPage = Math.min(pagination.pages, pagination.page + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                pageBtn.onclick = () => loadQuestionsList(i);
                if (i === pagination.page) {
                    pageBtn.classList.add('active');
                }
                container.appendChild(pageBtn);
            }
            
            // 下一页按钮
            if (pagination.has_next) {
                const nextBtn = document.createElement('button');
                nextBtn.textContent = '下一页';
                nextBtn.onclick = () => loadQuestionsList(pagination.page + 1);
                container.appendChild(nextBtn);
            }
        }

        // 查看题目详情
        async function viewQuestionDetails(questionId) {
            try {
                const response = await fetch(`/api/usage/statistics/${questionId}`);
                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error);
                }

                const stats = data.statistics;
                let details = `题目ID: ${questionId}\n`;
                details += `使用次数: ${stats.usage_count || 0}\n`;
                details += `采用率: ${(stats.adoption_rate || 0).toFixed(2)}%\n`;
                details += `最近使用: ${stats.last_used || '从未使用'}\n`;
                details += `首次使用: ${stats.first_used || '从未使用'}`;
                
                alert(details);
                
            } catch (error) {
                console.error('获取题目详情失败:', error);
                alert('获取题目详情失败: ' + error.message);
            }
        }

        // 执行分析
        async function performAnalysis() {
            const bankId = document.getElementById('analysisBankSelect').value;
            const analysisType = document.getElementById('analysisType').value;
            
            if (!bankId) {
                alert('请选择题库');
                return;
            }

            try {
                const response = await fetch('/api/usage/report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        report_type: analysisType,
                        bank_id: bankId
                    })
                });
                
                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error);
                }

                // 显示分析结果
                document.getElementById('analysisChart').style.display = 'block';
                document.getElementById('analysisTitle').textContent = getAnalysisTitle(analysisType);
                
                // 绘制分析图表
                drawAnalysisChart(analysisType, data.report);
                
            } catch (error) {
                console.error('分析失败:', error);
                alert('分析失败: ' + error.message);
            }
        }

        // 获取分析标题
        function getAnalysisTitle(type) {
            const titles = {
                'trend': '使用趋势分析',
                'type_distribution': '题型分布分析',
                'difficulty_analysis': '难度分析',
                'correlation': '相关性分析'
            };
            return titles[type] || '分析结果';
        }

        // 绘制分析图表
        function drawAnalysisChart(type, data) {
            const ctx = document.getElementById('analysisCanvas').getContext('2d');
            
            if (charts.analysis) {
                charts.analysis.destroy();
            }
            
            let chartConfig = {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: []
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            };
            
            // 根据分析类型配置图表
            switch (type) {
                case 'trend':
                    chartConfig.type = 'line';
                    chartConfig.data.labels = data.trend_data?.labels || [];
                    chartConfig.data.datasets = [{
                        label: '使用次数',
                        data: data.trend_data?.values || [],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }];
                    break;
                    
                case 'type_distribution':
                    chartConfig.type = 'pie';
                    chartConfig.data.labels = Object.keys(data.type_stats || {});
                    chartConfig.data.datasets = [{
                        data: Object.values(data.type_stats || {}),
                        backgroundColor: [
                            '#667eea', '#764ba2', '#f093fb', '#f5576c',
                            '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
                        ]
                    }];
                    break;
                    
                case 'difficulty_analysis':
                    chartConfig.data.labels = Object.keys(data.difficulty_stats || {});
                    chartConfig.data.datasets = [{
                        label: '平均使用次数',
                        data: Object.values(data.difficulty_stats || {}),
                        backgroundColor: '#667eea'
                    }];
                    break;
                    
                default:
                    chartConfig.data.labels = ['数据'];
                    chartConfig.data.datasets = [{
                        label: '值',
                        data: [1],
                        backgroundColor: '#667eea'
                    }];
            }
            
            charts.analysis = new Chart(ctx, chartConfig);
        }

        // 生成报告
        async function generateReport() {
            const bankId = document.getElementById('reportBankSelect').value;
            const reportType = document.getElementById('reportType').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            if (!bankId) {
                alert('请选择题库');
                return;
            }

            try {
                const response = await fetch('/api/usage/report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        report_type: reportType,
                        bank_id: bankId,
                        start_date: startDate,
                        end_date: endDate
                    })
                });
                
                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error);
                }

                // 显示报告结果
                const reportContent = document.getElementById('reportContent');
                reportContent.innerHTML = formatReportContent(data.report);
                document.getElementById('reportResult').style.display = 'block';
                
            } catch (error) {
                console.error('生成报告失败:', error);
                alert('生成报告失败: ' + error.message);
            }
        }

        // 格式化报告内容
        function formatReportContent(report) {
            let html = '<div style="line-height: 1.6;">';
            
            if (report.summary) {
                html += `<h4>报告摘要</h4><p>${report.summary}</p>`;
            }
            
            if (report.statistics) {
                html += '<h4>统计数据</h4><ul>';
                Object.entries(report.statistics).forEach(([key, value]) => {
                    html += `<li><strong>${key}:</strong> ${value}</li>`;
                });
                html += '</ul>';
            }
            
            if (report.recommendations) {
                html += '<h4>建议</h4><ul>';
                report.recommendations.forEach(rec => {
                    html += `<li>${rec}</li>`;
                });
                html += '</ul>';
            }
            
            html += '</div>';
            return html;
        }

        // 下载报告
        function downloadReport() {
            const content = document.getElementById('reportContent').innerHTML;
            const blob = new Blob([content], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `usage_report_${new Date().toISOString().split('T')[0]}.html`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 表格排序
        function sortTable(column) {
            if (currentSort === column) {
                currentOrder = currentOrder === 'desc' ? 'asc' : 'desc';
            } else {
                currentSort = column;
                currentOrder = 'desc';
            }
            
            document.getElementById('sortBy').value = currentSort;
            document.getElementById('sortOrder').value = currentOrder;
            
            loadQuestionsList(1);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期范围（最近30天）
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);
            
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
        });
    </script>
</body>
</html>
'''



@app.route('/restart_system', methods=['POST'])
def restart_system():
    """系统重启路由"""
    try:
        import os
        import sys
        flash("系统正在重启，请稍候...", "success")
        # 使用os.execv重启当前进程
        os.execv(sys.executable, ['python'] + sys.argv)
    except Exception as e:
        flash(f"重启失败: {e}", "error")
        return redirect(url_for('index'))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
