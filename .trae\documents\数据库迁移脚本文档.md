# 题库管理系统数据库迁移脚本文档

## 1. 迁移概述

### 1.1 迁移目标
本迁移脚本旨在将现有的题库管理系统数据库升级，以支持以下新功能：
1. **重复题目检测与管理**：添加重复记录表和相关索引
2. **备用题目管理系统**：扩展题目状态管理和轮换历史记录
3. **题目批量编辑功能**：添加编辑历史和操作记录表

### 1.2 迁移策略
- **向后兼容**：确保现有功能不受影响
- **数据安全**：提供完整的备份和回滚方案
- **分步执行**：按功能模块分步进行迁移
- **性能优化**：添加必要的索引以提升查询性能

### 1.3 迁移版本
- **当前版本**：v1.0（基础题库管理）
- **目标版本**：v2.0（增强功能版本）
- **迁移脚本版本**：migration_v1.0_to_v2.0

## 2. 数据库备份脚本

### 2.1 完整备份脚本

```sql
-- ==========================================
-- 数据库完整备份脚本
-- 执行时间：迁移前必须执行
-- ==========================================

-- 创建备份目录（需要数据库管理员权限）
-- 注意：实际路径需要根据服务器环境调整

-- PostgreSQL 备份命令（在命令行执行）
/*
pg_dump -h localhost -U postgres -d question_bank_db > backup_v1.0_$(date +%Y%m%d_%H%M%S).sql
*/

-- 或者使用 SQL 命令创建表结构和数据的备份
CREATE SCHEMA IF NOT EXISTS backup_v1_0;

-- 备份现有表结构和数据
CREATE TABLE backup_v1_0.questions AS SELECT * FROM questions;
CREATE TABLE backup_v1_0.question_banks AS SELECT * FROM question_banks;
CREATE TABLE backup_v1_0.papers AS SELECT * FROM papers;
CREATE TABLE backup_v1_0.paper_questions AS SELECT * FROM paper_questions;
CREATE TABLE backup_v1_0.question_groups AS SELECT * FROM question_groups;

-- 备份索引信息
CREATE TABLE backup_v1_0.index_definitions AS 
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('questions', 'question_banks', 'papers', 'paper_questions', 'question_groups');

-- 记录备份信息
CREATE TABLE backup_v1_0.migration_log (
    id SERIAL PRIMARY KEY,
    backup_date TIMESTAMP DEFAULT NOW(),
    backup_version VARCHAR(20) DEFAULT 'v1.0',
    backup_description TEXT,
    table_count INTEGER,
    total_records INTEGER
);

INSERT INTO backup_v1_0.migration_log (backup_description, table_count, total_records)
VALUES (
    '迁移前完整备份',
    5,
    (SELECT 
        (SELECT COUNT(*) FROM questions) +
        (SELECT COUNT(*) FROM question_banks) +
        (SELECT COUNT(*) FROM papers) +
        (SELECT COUNT(*) FROM paper_questions) +
        (SELECT COUNT(*) FROM question_groups)
    )
);

COMMIT;
```

## 3. 表结构迁移脚本

### 3.1 扩展现有表结构

```sql
-- ==========================================
-- 现有表结构扩展脚本
-- ==========================================

BEGIN;

-- 3.1.1 扩展 questions 表
ALTER TABLE questions 
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'backup', 'archived'));

ALTER TABLE questions 
ADD COLUMN IF NOT EXISTS usage_count INTEGER DEFAULT 0;

ALTER TABLE questions 
ADD COLUMN IF NOT EXISTS last_used TIMESTAMP;

ALTER TABLE questions 
ADD COLUMN IF NOT EXISTS quality_score DECIMAL(3,2) DEFAULT 0.00;

ALTER TABLE questions 
ADD COLUMN IF NOT EXISTS created_by VARCHAR(100);

ALTER TABLE questions 
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(100);

ALTER TABLE questions 
ADD COLUMN IF NOT EXISTS version_number INTEGER DEFAULT 1;

-- 添加注释
COMMENT ON COLUMN questions.status IS '题目状态：active-活跃，backup-备用，archived-归档';
COMMENT ON COLUMN questions.usage_count IS '题目使用次数统计';
COMMENT ON COLUMN questions.last_used IS '最后使用时间';
COMMENT ON COLUMN questions.quality_score IS '题目质量评分（0-5分）';
COMMENT ON COLUMN questions.created_by IS '创建者';
COMMENT ON COLUMN questions.updated_by IS '最后更新者';
COMMENT ON COLUMN questions.version_number IS '版本号';

-- 3.1.2 扩展 question_banks 表
ALTER TABLE question_banks 
ADD COLUMN IF NOT EXISTS standard_count INTEGER DEFAULT 1000;

ALTER TABLE question_banks 
ADD COLUMN IF NOT EXISTS rotation_rate DECIMAL(5,2) DEFAULT 30.00;

ALTER TABLE question_banks 
ADD COLUMN IF NOT EXISTS last_rotation_date TIMESTAMP;

ALTER TABLE question_banks 
ADD COLUMN IF NOT EXISTS auto_rotation_enabled BOOLEAN DEFAULT FALSE;

ALTER TABLE question_banks 
ADD COLUMN IF NOT EXISTS rotation_strategy VARCHAR(50) DEFAULT 'random';

-- 添加注释
COMMENT ON COLUMN question_banks.standard_count IS '标准题目数量';
COMMENT ON COLUMN question_banks.rotation_rate IS '年轮换比例（百分比）';
COMMENT ON COLUMN question_banks.last_rotation_date IS '最后轮换时间';
COMMENT ON COLUMN question_banks.auto_rotation_enabled IS '是否启用自动轮换';
COMMENT ON COLUMN question_banks.rotation_strategy IS '轮换策略';

COMMIT;
```

### 3.2 创建新功能表

```sql
-- ==========================================
-- 新功能表创建脚本
-- ==========================================

BEGIN;

-- 3.2.1 重复题目记录表
CREATE TABLE IF NOT EXISTS duplicate_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question1_id UUID NOT NULL,
    question2_id UUID NOT NULL,
    similarity_score DECIMAL(5,2) NOT NULL,
    algorithm_used VARCHAR(50) NOT NULL,
    detection_date TIMESTAMP DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'dismissed', 'resolved')),
    resolved_action VARCHAR(50),
    resolved_by VARCHAR(100),
    resolved_date TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- 外键约束
    CONSTRAINT fk_duplicate_question1 FOREIGN KEY (question1_id) REFERENCES questions(id) ON DELETE CASCADE,
    CONSTRAINT fk_duplicate_question2 FOREIGN KEY (question2_id) REFERENCES questions(id) ON DELETE CASCADE,
    
    -- 唯一约束（避免重复记录）
    CONSTRAINT unique_question_pair UNIQUE (question1_id, question2_id)
);

-- 添加注释
COMMENT ON TABLE duplicate_records IS '重复题目检测记录表';
COMMENT ON COLUMN duplicate_records.similarity_score IS '相似度分数（0-100）';
COMMENT ON COLUMN duplicate_records.algorithm_used IS '使用的检测算法';
COMMENT ON COLUMN duplicate_records.status IS '处理状态';
COMMENT ON COLUMN duplicate_records.resolved_action IS '解决方案：delete_duplicate, merge_questions, keep_both';

-- 3.2.2 备用题目规则表
CREATE TABLE IF NOT EXISTS backup_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bank_id UUID NOT NULL,
    rule_name VARCHAR(100) NOT NULL,
    standard_count INTEGER NOT NULL,
    rotation_rate DECIMAL(5,2) NOT NULL,
    rotation_strategy VARCHAR(50) NOT NULL,
    rotation_frequency VARCHAR(20) DEFAULT 'yearly',
    difficulty_distribution JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- 外键约束
    CONSTRAINT fk_backup_rule_bank FOREIGN KEY (bank_id) REFERENCES question_banks(id) ON DELETE CASCADE
);

-- 添加注释
COMMENT ON TABLE backup_rules IS '备用题目管理规则表';
COMMENT ON COLUMN backup_rules.rotation_frequency IS '轮换频率：yearly, quarterly, monthly';
COMMENT ON COLUMN backup_rules.difficulty_distribution IS '难度分布配置（JSON格式）';

-- 3.2.3 轮换历史记录表
CREATE TABLE IF NOT EXISTS rotation_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bank_id UUID NOT NULL,
    rotation_date TIMESTAMP DEFAULT NOW(),
    strategy_used VARCHAR(50) NOT NULL,
    rotation_count INTEGER NOT NULL,
    rotated_in_questions TEXT, -- 逗号分隔的题目ID列表
    rotated_out_questions TEXT, -- 逗号分隔的题目ID列表
    execution_time_ms INTEGER,
    executed_by VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    
    -- 外键约束
    CONSTRAINT fk_rotation_bank FOREIGN KEY (bank_id) REFERENCES question_banks(id) ON DELETE CASCADE
);

-- 添加注释
COMMENT ON TABLE rotation_history IS '题目轮换历史记录表';
COMMENT ON COLUMN rotation_history.execution_time_ms IS '执行耗时（毫秒）';
COMMENT ON COLUMN rotation_history.rotated_in_questions IS '轮换进入的题目ID列表';
COMMENT ON COLUMN rotation_history.rotated_out_questions IS '轮换退出的题目ID列表';

-- 3.2.4 编辑历史记录表
CREATE TABLE IF NOT EXISTS edit_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question_id UUID,
    operation_type VARCHAR(50) NOT NULL,
    operation_id VARCHAR(100), -- 批量操作的统一ID
    old_data JSONB,
    new_data JSONB,
    change_summary TEXT,
    edited_by VARCHAR(100),
    edit_date TIMESTAMP DEFAULT NOW(),
    is_batch_operation BOOLEAN DEFAULT FALSE,
    batch_size INTEGER,
    
    -- 外键约束（允许为空，因为可能是批量操作）
    CONSTRAINT fk_edit_question FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE SET NULL
);

-- 添加注释
COMMENT ON TABLE edit_history IS '题目编辑历史记录表';
COMMENT ON COLUMN edit_history.operation_type IS '操作类型：option_reorder, content_replace, format_adjust';
COMMENT ON COLUMN edit_history.operation_id IS '批量操作的唯一标识符';
COMMENT ON COLUMN edit_history.old_data IS '修改前的数据（JSON格式）';
COMMENT ON COLUMN edit_history.new_data IS '修改后的数据（JSON格式）';

-- 3.2.5 相似度缓存表
CREATE TABLE IF NOT EXISTS similarity_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question1_id UUID NOT NULL,
    question2_id UUID NOT NULL,
    algorithm_type VARCHAR(50) NOT NULL,
    similarity_score DECIMAL(5,2) NOT NULL,
    calculation_date TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP DEFAULT (NOW() + INTERVAL '7 days'),
    
    -- 外键约束
    CONSTRAINT fk_cache_question1 FOREIGN KEY (question1_id) REFERENCES questions(id) ON DELETE CASCADE,
    CONSTRAINT fk_cache_question2 FOREIGN KEY (question2_id) REFERENCES questions(id) ON DELETE CASCADE,
    
    -- 唯一约束
    CONSTRAINT unique_similarity_cache UNIQUE (question1_id, question2_id, algorithm_type)
);

-- 添加注释
COMMENT ON TABLE similarity_cache IS '相似度计算结果缓存表';
COMMENT ON COLUMN similarity_cache.expires_at IS '缓存过期时间';

COMMIT;
```

## 4. 索引创建脚本

```sql
-- ==========================================
-- 性能优化索引创建脚本
-- ==========================================

BEGIN;

-- 4.1 questions 表索引
CREATE INDEX IF NOT EXISTS idx_questions_status ON questions(status);
CREATE INDEX IF NOT EXISTS idx_questions_bank_status ON questions(question_bank_id, status);
CREATE INDEX IF NOT EXISTS idx_questions_usage_count ON questions(usage_count DESC);
CREATE INDEX IF NOT EXISTS idx_questions_last_used ON questions(last_used DESC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_questions_quality_score ON questions(quality_score DESC);
CREATE INDEX IF NOT EXISTS idx_questions_created_at ON questions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_questions_difficulty ON questions(difficulty_code);

-- 复合索引
CREATE INDEX IF NOT EXISTS idx_questions_bank_difficulty_status ON questions(question_bank_id, difficulty_code, status);
CREATE INDEX IF NOT EXISTS idx_questions_status_last_used ON questions(status, last_used DESC NULLS LAST);

-- 4.2 duplicate_records 表索引
CREATE INDEX IF NOT EXISTS idx_duplicate_records_question1 ON duplicate_records(question1_id);
CREATE INDEX IF NOT EXISTS idx_duplicate_records_question2 ON duplicate_records(question2_id);
CREATE INDEX IF NOT EXISTS idx_duplicate_records_similarity ON duplicate_records(similarity_score DESC);
CREATE INDEX IF NOT EXISTS idx_duplicate_records_status ON duplicate_records(status);
CREATE INDEX IF NOT EXISTS idx_duplicate_records_detection_date ON duplicate_records(detection_date DESC);
CREATE INDEX IF NOT EXISTS idx_duplicate_records_algorithm ON duplicate_records(algorithm_used);

-- 复合索引
CREATE INDEX IF NOT EXISTS idx_duplicate_records_status_similarity ON duplicate_records(status, similarity_score DESC);

-- 4.3 backup_rules 表索引
CREATE INDEX IF NOT EXISTS idx_backup_rules_bank ON backup_rules(bank_id);
CREATE INDEX IF NOT EXISTS idx_backup_rules_active ON backup_rules(is_active);
CREATE INDEX IF NOT EXISTS idx_backup_rules_strategy ON backup_rules(rotation_strategy);

-- 4.4 rotation_history 表索引
CREATE INDEX IF NOT EXISTS idx_rotation_history_bank ON rotation_history(bank_id);
CREATE INDEX IF NOT EXISTS idx_rotation_history_date ON rotation_history(rotation_date DESC);
CREATE INDEX IF NOT EXISTS idx_rotation_history_strategy ON rotation_history(strategy_used);
CREATE INDEX IF NOT EXISTS idx_rotation_history_executed_by ON rotation_history(executed_by);

-- 复合索引
CREATE INDEX IF NOT EXISTS idx_rotation_history_bank_date ON rotation_history(bank_id, rotation_date DESC);

-- 4.5 edit_history 表索引
CREATE INDEX IF NOT EXISTS idx_edit_history_question ON edit_history(question_id);
CREATE INDEX IF NOT EXISTS idx_edit_history_operation_type ON edit_history(operation_type);
CREATE INDEX IF NOT EXISTS idx_edit_history_operation_id ON edit_history(operation_id);
CREATE INDEX IF NOT EXISTS idx_edit_history_date ON edit_history(edit_date DESC);
CREATE INDEX IF NOT EXISTS idx_edit_history_edited_by ON edit_history(edited_by);
CREATE INDEX IF NOT EXISTS idx_edit_history_batch ON edit_history(is_batch_operation);

-- 复合索引
CREATE INDEX IF NOT EXISTS idx_edit_history_question_date ON edit_history(question_id, edit_date DESC);
CREATE INDEX IF NOT EXISTS idx_edit_history_operation_batch ON edit_history(operation_id, is_batch_operation);

-- 4.6 similarity_cache 表索引
CREATE INDEX IF NOT EXISTS idx_similarity_cache_question1 ON similarity_cache(question1_id);
CREATE INDEX IF NOT EXISTS idx_similarity_cache_question2 ON similarity_cache(question2_id);
CREATE INDEX IF NOT EXISTS idx_similarity_cache_algorithm ON similarity_cache(algorithm_type);
CREATE INDEX IF NOT EXISTS idx_similarity_cache_expires ON similarity_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_similarity_cache_score ON similarity_cache(similarity_score DESC);

-- 复合索引
CREATE INDEX IF NOT EXISTS idx_similarity_cache_questions_algo ON similarity_cache(question1_id, question2_id, algorithm_type);

-- 4.7 全文搜索索引（如果需要）
CREATE INDEX IF NOT EXISTS idx_questions_stem_gin ON questions USING gin(to_tsvector('chinese', question_stem));
CREATE INDEX IF NOT EXISTS idx_questions_options_gin ON questions USING gin(to_tsvector('chinese', 
    COALESCE(option_a, '') || ' ' || 
    COALESCE(option_b, '') || ' ' || 
    COALESCE(option_c, '') || ' ' || 
    COALESCE(option_d, '') || ' ' || 
    COALESCE(option_e, '')
));

COMMIT;
```

## 5. 数据迁移脚本

```sql
-- ==========================================
-- 数据迁移和初始化脚本
-- ==========================================

BEGIN;

-- 5.1 更新现有题目状态
-- 将所有现有题目设置为活跃状态
UPDATE questions 
SET status = 'active', 
    updated_at = NOW()
WHERE status IS NULL;

-- 5.2 初始化题库配置
-- 为现有题库设置默认配置
UPDATE question_banks 
SET standard_count = (
        SELECT COUNT(*) 
        FROM questions 
        WHERE questions.question_bank_id = question_banks.id
    ),
    rotation_rate = 30.00,
    auto_rotation_enabled = FALSE,
    rotation_strategy = 'random',
    updated_at = NOW()
WHERE standard_count IS NULL;

-- 5.3 创建默认备用规则
-- 为每个题库创建默认的备用题目管理规则
INSERT INTO backup_rules (bank_id, rule_name, standard_count, rotation_rate, rotation_strategy, difficulty_distribution, created_by)
SELECT 
    id as bank_id,
    name || '_默认规则' as rule_name,
    GREATEST((
        SELECT COUNT(*) 
        FROM questions 
        WHERE questions.question_bank_id = question_banks.id
    ), 100) as standard_count,
    30.00 as rotation_rate,
    'random' as rotation_strategy,
    '{
        "easy": 30,
        "medium": 50,
        "hard": 20
    }'::jsonb as difficulty_distribution,
    'system_migration' as created_by
FROM question_banks
WHERE NOT EXISTS (
    SELECT 1 FROM backup_rules WHERE backup_rules.bank_id = question_banks.id
);

-- 5.4 初始化题目使用统计
-- 基于现有数据估算使用次数（如果有相关数据）
UPDATE questions 
SET usage_count = FLOOR(RANDOM() * 10), -- 随机初始化，实际应用中应基于真实数据
    last_used = created_at + (RANDOM() * INTERVAL '365 days'),
    quality_score = 3.0 + (RANDOM() * 2.0), -- 3-5分随机评分
    version_number = 1
WHERE usage_count IS NULL;

-- 5.5 创建系统用户记录（如果需要）
-- 为迁移操作创建系统用户记录
INSERT INTO edit_history (operation_type, operation_id, change_summary, edited_by, is_batch_operation, batch_size)
VALUES (
    'system_migration',
    'migration_v1_to_v2_' || TO_CHAR(NOW(), 'YYYYMMDD_HH24MISS'),
    '系统从v1.0迁移到v2.0，添加新功能支持',
    'system_migration',
    TRUE,
    (SELECT COUNT(*) FROM questions)
);

COMMIT;
```

## 6. 触发器和函数创建

```sql
-- ==========================================
-- 触发器和函数创建脚本
-- ==========================================

-- 6.1 自动更新时间戳函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 6.2 为相关表添加更新时间戳触发器
CREATE TRIGGER update_questions_updated_at 
    BEFORE UPDATE ON questions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_question_banks_updated_at 
    BEFORE UPDATE ON question_banks 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_duplicate_records_updated_at 
    BEFORE UPDATE ON duplicate_records 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_backup_rules_updated_at 
    BEFORE UPDATE ON backup_rules 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 6.3 题目使用统计更新函数
CREATE OR REPLACE FUNCTION update_question_usage(question_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE questions 
    SET usage_count = usage_count + 1,
        last_used = NOW()
    WHERE id = question_id;
END;
$$ LANGUAGE plpgsql;

-- 6.4 清理过期缓存函数
CREATE OR REPLACE FUNCTION cleanup_expired_cache()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM similarity_cache 
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 6.5 题库统计信息更新函数
CREATE OR REPLACE FUNCTION update_bank_statistics(bank_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE question_banks 
    SET updated_at = NOW()
    WHERE id = bank_id;
    
    -- 这里可以添加更多统计信息的更新逻辑
END;
$$ LANGUAGE plpgsql;

-- 6.6 重复题目检测触发器
CREATE OR REPLACE FUNCTION check_duplicate_on_insert()
RETURNS TRIGGER AS $$
BEGIN
    -- 当插入新题目时，可以触发重复检测
    -- 这里只是示例，实际实现可能需要异步处理
    
    -- 记录日志
    INSERT INTO edit_history (operation_type, change_summary, edited_by)
    VALUES ('question_created', '新题目创建：' || NEW.id, COALESCE(NEW.created_by, 'unknown'));
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_check_duplicate_on_insert
    AFTER INSERT ON questions
    FOR EACH ROW
    EXECUTE FUNCTION check_duplicate_on_insert();
```

## 7. 权限设置脚本

```sql
-- ==========================================
-- 数据库权限设置脚本
-- ==========================================

-- 7.1 创建角色（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'question_bank_admin') THEN
        CREATE ROLE question_bank_admin;
    END IF;
    
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'question_bank_user') THEN
        CREATE ROLE question_bank_user;
    END IF;
    
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'question_bank_readonly') THEN
        CREATE ROLE question_bank_readonly;
    END IF;
END
$$;

-- 7.2 为管理员角色授权
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO question_bank_admin;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO question_bank_admin;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO question_bank_admin;

-- 7.3 为普通用户角色授权
GRANT SELECT, INSERT, UPDATE ON questions TO question_bank_user;
GRANT SELECT, INSERT, UPDATE ON question_banks TO question_bank_user;
GRANT SELECT, INSERT, UPDATE ON papers TO question_bank_user;
GRANT SELECT, INSERT, UPDATE ON paper_questions TO question_bank_user;
GRANT SELECT, INSERT, UPDATE ON question_groups TO question_bank_user;
GRANT SELECT, INSERT, UPDATE ON duplicate_records TO question_bank_user;
GRANT SELECT, INSERT, UPDATE ON backup_rules TO question_bank_user;
GRANT SELECT, INSERT ON rotation_history TO question_bank_user;
GRANT SELECT, INSERT ON edit_history TO question_bank_user;
GRANT SELECT, INSERT, DELETE ON similarity_cache TO question_bank_user;

-- 序列权限
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO question_bank_user;

-- 函数执行权限
GRANT EXECUTE ON FUNCTION update_question_usage(UUID) TO question_bank_user;
GRANT EXECUTE ON FUNCTION update_bank_statistics(UUID) TO question_bank_user;

-- 7.4 为只读用户角色授权
GRANT SELECT ON ALL TABLES IN SCHEMA public TO question_bank_readonly;
GRANT EXECUTE ON FUNCTION cleanup_expired_cache() TO question_bank_readonly;
```

## 8. 数据验证脚本

```sql
-- ==========================================
-- 迁移后数据验证脚本
-- ==========================================

-- 8.1 表结构验证
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN (
    'questions', 'question_banks', 'duplicate_records', 
    'backup_rules', 'rotation_history', 'edit_history', 'similarity_cache'
)
ORDER BY table_name, ordinal_position;

-- 8.2 索引验证
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public'
AND tablename IN (
    'questions', 'question_banks', 'duplicate_records', 
    'backup_rules', 'rotation_history', 'edit_history', 'similarity_cache'
)
ORDER BY tablename, indexname;

-- 8.3 数据完整性验证
-- 检查外键约束
SELECT 
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
AND tc.table_schema = 'public'
AND tc.table_name IN (
    'duplicate_records', 'backup_rules', 'rotation_history', 
    'edit_history', 'similarity_cache'
);

-- 8.4 数据统计验证
SELECT 
    'questions' as table_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count,
    COUNT(CASE WHEN status = 'backup' THEN 1 END) as backup_count,
    COUNT(CASE WHEN status = 'archived' THEN 1 END) as archived_count
FROM questions

UNION ALL

SELECT 
    'question_banks' as table_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN auto_rotation_enabled = true THEN 1 END) as auto_rotation_enabled,
    COUNT(CASE WHEN standard_count > 0 THEN 1 END) as with_standard_count,
    0 as archived_count
FROM question_banks

UNION ALL

SELECT 
    'duplicate_records' as table_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
    COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_count,
    COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_count
FROM duplicate_records

UNION ALL

SELECT 
    'backup_rules' as table_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_rules,
    0 as confirmed_count,
    0 as resolved_count
FROM backup_rules;

-- 8.5 性能验证
-- 检查关键查询的执行计划
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM questions 
WHERE question_bank_id = (SELECT id FROM question_banks LIMIT 1) 
AND status = 'active' 
ORDER BY last_used DESC NULLS LAST 
LIMIT 10;

EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM duplicate_records 
WHERE status = 'pending' 
AND similarity_score >= 80.0 
ORDER BY similarity_score DESC 
LIMIT 20;
```

## 9. 回滚脚本

```sql
-- ==========================================
-- 紧急回滚脚本
-- 注意：此脚本将删除所有新功能相关的数据
-- ==========================================

-- 9.1 删除触发器
DROP TRIGGER IF EXISTS update_questions_updated_at ON questions;
DROP TRIGGER IF EXISTS update_question_banks_updated_at ON question_banks;
DROP TRIGGER IF EXISTS update_duplicate_records_updated_at ON duplicate_records;
DROP TRIGGER IF EXISTS update_backup_rules_updated_at ON backup_rules;
DROP TRIGGER IF EXISTS trigger_check_duplicate_on_insert ON questions;

-- 9.2 删除函数
DROP FUNCTION IF EXISTS update_updated_at_column();
DROP FUNCTION IF EXISTS update_question_usage(UUID);
DROP FUNCTION IF EXISTS cleanup_expired_cache();
DROP FUNCTION IF EXISTS update_bank_statistics(UUID);
DROP FUNCTION IF EXISTS check_duplicate_on_insert();

-- 9.3 删除新表
DROP TABLE IF EXISTS similarity_cache;
DROP TABLE IF EXISTS edit_history;
DROP TABLE IF EXISTS rotation_history;
DROP TABLE IF EXISTS backup_rules;
DROP TABLE IF EXISTS duplicate_records;

-- 9.4 删除新增列
ALTER TABLE questions DROP COLUMN IF EXISTS status;
ALTER TABLE questions DROP COLUMN IF EXISTS usage_count;
ALTER TABLE questions DROP COLUMN IF EXISTS last_used;
ALTER TABLE questions DROP COLUMN IF EXISTS quality_score;
ALTER TABLE questions DROP COLUMN IF EXISTS created_by;
ALTER TABLE questions DROP COLUMN IF EXISTS updated_by;
ALTER TABLE questions DROP COLUMN IF EXISTS version_number;

ALTER TABLE question_banks DROP COLUMN IF EXISTS standard_count;
ALTER TABLE question_banks DROP COLUMN IF EXISTS rotation_rate;
ALTER TABLE question_banks DROP COLUMN IF EXISTS last_rotation_date;
ALTER TABLE question_banks DROP COLUMN IF EXISTS auto_rotation_enabled;
ALTER TABLE question_banks DROP COLUMN IF EXISTS rotation_strategy;

-- 9.5 从备份恢复数据（如果需要）
/*
-- 恢复原始数据的示例命令
TRUNCATE questions;
INSERT INTO questions SELECT * FROM backup_v1_0.questions;

TRUNCATE question_banks;
INSERT INTO question_banks SELECT * FROM backup_v1_0.question_banks;

-- 其他表的恢复...
*/

-- 9.6 删除备份模式（可选）
-- DROP SCHEMA IF EXISTS backup_v1_0 CASCADE;
```

## 10. 迁移执行清单

### 10.1 迁移前检查清单
- [ ] 确认数据库连接正常
- [ ] 确认有足够的磁盘空间
- [ ] 确认数据库用户权限
- [ ] 停止应用程序服务
- [ ] 通知相关用户系统维护

### 10.2 迁移执行顺序
1. [ ] 执行数据库备份脚本
2. [ ] 执行表结构扩展脚本
3. [ ] 执行新表创建脚本
4. [ ] 执行索引创建脚本
5. [ ] 执行数据迁移脚本
6. [ ] 执行触发器和函数创建脚本
7. [ ] 执行权限设置脚本
8. [ ] 执行数据验证脚本

### 10.3 迁移后检查清单
- [ ] 验证所有表结构正确
- [ ] 验证索引创建成功
- [ ] 验证数据完整性
- [ ] 验证应用程序功能
- [ ] 性能测试
- [ ] 启动应用程序服务
- [ ] 通知用户系统恢复正常

### 10.4 监控要点
- 迁移过程中的错误日志
- 数据库性能指标
- 磁盘空间使用情况
- 应用程序响应时间
- 用户反馈和问题报告

这套完整的数据库迁移脚本确保了从现有系统到新功能的平滑升级，同时提供了完整的备份和回滚机制，保障了数据安全和系统稳定性。