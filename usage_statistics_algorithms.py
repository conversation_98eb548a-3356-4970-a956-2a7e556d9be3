# -*- coding: utf-8 -*-
"""
题目使用统计算法模块

本模块提供题目使用统计的核心算法实现，包括：
1. 使用次数统计算法
2. 采用率计算算法
3. 使用趋势分析算法
4. 相关性分析算法
5. 预测算法

作者: 系统自动生成
创建时间: 2024
"""

import math
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional, Any
from collections import defaultdict, Counter
from dataclasses import dataclass
from enum import Enum

class TrendType(Enum):
    """趋势类型枚举"""
    INCREASING = "increasing"  # 上升趋势
    DECREASING = "decreasing"  # 下降趋势
    STABLE = "stable"          # 稳定趋势
    VOLATILE = "volatile"      # 波动趋势

@dataclass
class UsageDataPoint:
    """使用数据点"""
    date: datetime
    usage_count: int
    question_id: str
    paper_id: str
    usage_type: str
    question_order: Optional[int] = None
    question_score: Optional[float] = None

@dataclass
class StatisticsResult:
    """统计结果数据类"""
    total_usage: int
    usage_rate: float
    trend: TrendType
    trend_score: float
    monthly_stats: Dict[str, int]
    type_stats: Dict[str, int]
    prediction: Dict[str, Any]
    correlation_data: Dict[str, float]

class UsageStatisticsAlgorithms:
    """
    题目使用统计算法类
    
    提供各种统计算法的实现
    """
    
    def __init__(self):
        """
        初始化算法实例
        """
        self.smoothing_factor = 0.3  # 指数平滑因子
        self.trend_threshold = 0.15  # 趋势判断阈值
        self.volatility_threshold = 0.25  # 波动性阈值
    
    # ==================== 基础统计算法 ====================
    
    def calculate_usage_statistics(self, usage_data: List[UsageDataPoint], 
                                 total_questions: int,
                                 analysis_period_days: int = 90) -> StatisticsResult:
        """
        计算题目使用统计信息
        
        参数:
            usage_data: 使用数据点列表
            total_questions: 题库总题目数
            analysis_period_days: 分析周期（天数）
            
        返回:
            StatisticsResult: 统计结果
        """
        if not usage_data:
            return StatisticsResult(
                total_usage=0,
                usage_rate=0.0,
                trend=TrendType.STABLE,
                trend_score=0.0,
                monthly_stats={},
                type_stats={},
                prediction={},
                correlation_data={}
            )
        
        # 基础统计
        total_usage = len(usage_data)
        usage_rate = (total_usage / max(total_questions, 1)) * 100
        
        # 按月份统计
        monthly_stats = self._calculate_monthly_statistics(usage_data)
        
        # 按类型统计
        type_stats = self._calculate_type_statistics(usage_data)
        
        # 趋势分析
        trend, trend_score = self._analyze_usage_trend(usage_data, analysis_period_days)
        
        # 预测分析
        prediction = self._predict_future_usage(usage_data, analysis_period_days)
        
        # 相关性分析
        correlation_data = self._analyze_correlations(usage_data)
        
        return StatisticsResult(
            total_usage=total_usage,
            usage_rate=round(usage_rate, 2),
            trend=trend,
            trend_score=round(trend_score, 3),
            monthly_stats=monthly_stats,
            type_stats=type_stats,
            prediction=prediction,
            correlation_data=correlation_data
        )
    
    def calculate_adoption_rate(self, used_questions: int, total_questions: int, 
                              usage_sessions: int, total_sessions: int) -> Dict[str, float]:
        """
        计算题目采用率
        
        参数:
            used_questions: 已使用题目数
            total_questions: 总题目数
            usage_sessions: 使用会话数
            total_sessions: 总会话数
            
        返回:
            Dict: 包含各种采用率指标
        """
        question_adoption_rate = (used_questions / max(total_questions, 1)) * 100
        session_adoption_rate = (usage_sessions / max(total_sessions, 1)) * 100
        
        # 综合采用率（加权平均）
        comprehensive_rate = (question_adoption_rate * 0.7 + session_adoption_rate * 0.3)
        
        # 采用效率（每个会话平均使用的题目数）
        adoption_efficiency = used_questions / max(usage_sessions, 1)
        
        return {
            'question_adoption_rate': round(question_adoption_rate, 2),
            'session_adoption_rate': round(session_adoption_rate, 2),
            'comprehensive_adoption_rate': round(comprehensive_rate, 2),
            'adoption_efficiency': round(adoption_efficiency, 2)
        }
    
    def calculate_usage_intensity(self, usage_data: List[UsageDataPoint], 
                                time_window_days: int = 30) -> Dict[str, float]:
        """
        计算使用强度指标
        
        参数:
            usage_data: 使用数据点列表
            time_window_days: 时间窗口（天数）
            
        返回:
            Dict: 使用强度指标
        """
        if not usage_data:
            return {
                'daily_average': 0.0,
                'peak_usage': 0.0,
                'usage_variance': 0.0,
                'intensity_score': 0.0
            }
        
        # 按日期分组统计
        daily_usage = defaultdict(int)
        for data_point in usage_data:
            date_key = data_point.date.strftime('%Y-%m-%d')
            daily_usage[date_key] += 1
        
        usage_counts = list(daily_usage.values())
        
        # 计算指标
        daily_average = np.mean(usage_counts) if usage_counts else 0
        peak_usage = max(usage_counts) if usage_counts else 0
        usage_variance = np.var(usage_counts) if len(usage_counts) > 1 else 0
        
        # 强度评分（0-100）
        intensity_score = min(100, (daily_average / max(time_window_days / 30, 1)) * 10)
        
        return {
            'daily_average': round(daily_average, 2),
            'peak_usage': peak_usage,
            'usage_variance': round(usage_variance, 2),
            'intensity_score': round(intensity_score, 2)
        }
    
    # ==================== 趋势分析算法 ====================
    
    def _analyze_usage_trend(self, usage_data: List[UsageDataPoint], 
                           analysis_period_days: int) -> Tuple[TrendType, float]:
        """
        分析使用趋势
        
        参数:
            usage_data: 使用数据点列表
            analysis_period_days: 分析周期
            
        返回:
            Tuple[TrendType, float]: 趋势类型和趋势评分
        """
        if len(usage_data) < 7:  # 数据点太少，无法分析趋势
            return TrendType.STABLE, 0.0
        
        # 按周分组统计
        weekly_usage = self._group_by_week(usage_data)
        
        if len(weekly_usage) < 3:
            return TrendType.STABLE, 0.0
        
        weeks = sorted(weekly_usage.keys())
        usage_counts = [weekly_usage[week] for week in weeks]
        
        # 计算线性回归斜率
        slope = self._calculate_linear_regression_slope(usage_counts)
        
        # 计算变异系数（衡量波动性）
        cv = self._calculate_coefficient_of_variation(usage_counts)
        
        # 判断趋势类型
        if cv > self.volatility_threshold:
            return TrendType.VOLATILE, abs(slope)
        elif slope > self.trend_threshold:
            return TrendType.INCREASING, slope
        elif slope < -self.trend_threshold:
            return TrendType.DECREASING, abs(slope)
        else:
            return TrendType.STABLE, abs(slope)
    
    def _calculate_linear_regression_slope(self, values: List[float]) -> float:
        """
        计算线性回归斜率
        
        参数:
            values: 数值列表
            
        返回:
            float: 斜率值
        """
        if len(values) < 2:
            return 0.0
        
        n = len(values)
        x = list(range(n))
        
        # 计算斜率
        x_mean = np.mean(x)
        y_mean = np.mean(values)
        
        numerator = sum((x[i] - x_mean) * (values[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return 0.0
        
        slope = numerator / denominator
        return slope
    
    def _calculate_coefficient_of_variation(self, values: List[float]) -> float:
        """
        计算变异系数
        
        参数:
            values: 数值列表
            
        返回:
            float: 变异系数
        """
        if not values or len(values) < 2:
            return 0.0
        
        mean_val = np.mean(values)
        std_val = np.std(values)
        
        if mean_val == 0:
            return 0.0
        
        return std_val / mean_val
    
    # ==================== 预测算法 ====================
    
    def _predict_future_usage(self, usage_data: List[UsageDataPoint], 
                            prediction_days: int = 30) -> Dict[str, Any]:
        """
        预测未来使用情况
        
        参数:
            usage_data: 历史使用数据
            prediction_days: 预测天数
            
        返回:
            Dict: 预测结果
        """
        if len(usage_data) < 14:  # 数据不足，无法预测
            return {
                'predicted_usage': 0,
                'confidence': 0.0,
                'trend_prediction': 'insufficient_data',
                'daily_predictions': []
            }
        
        # 按日期分组
        daily_usage = self._group_by_day(usage_data)
        
        # 使用指数平滑法预测
        predictions = self._exponential_smoothing_prediction(
            daily_usage, prediction_days
        )
        
        # 计算预测总量
        predicted_total = sum(predictions)
        
        # 计算置信度（基于历史数据的稳定性）
        confidence = self._calculate_prediction_confidence(daily_usage)
        
        # 趋势预测
        trend_prediction = self._predict_trend_direction(daily_usage)
        
        return {
            'predicted_usage': int(predicted_total),
            'confidence': round(confidence, 2),
            'trend_prediction': trend_prediction,
            'daily_predictions': [round(p, 1) for p in predictions[:7]]  # 只返回前7天
        }
    
    def _exponential_smoothing_prediction(self, daily_usage: Dict[str, int], 
                                        prediction_days: int) -> List[float]:
        """
        指数平滑预测
        
        参数:
            daily_usage: 按日期分组的使用数据
            prediction_days: 预测天数
            
        返回:
            List[float]: 预测值列表
        """
        if not daily_usage:
            return [0.0] * prediction_days
        
        # 获取最近的使用数据
        sorted_dates = sorted(daily_usage.keys())
        recent_values = [daily_usage[date] for date in sorted_dates[-30:]]  # 最近30天
        
        if not recent_values:
            return [0.0] * prediction_days
        
        # 初始化
        alpha = self.smoothing_factor
        smoothed_value = recent_values[0]
        
        # 计算平滑值
        for value in recent_values[1:]:
            smoothed_value = alpha * value + (1 - alpha) * smoothed_value
        
        # 生成预测值
        predictions = [smoothed_value] * prediction_days
        
        # 考虑季节性调整（简单的周期性调整）
        for i in range(prediction_days):
            day_of_week = (len(sorted_dates) + i) % 7
            seasonal_factor = self._get_seasonal_factor(daily_usage, day_of_week)
            predictions[i] *= seasonal_factor
        
        return predictions
    
    def _get_seasonal_factor(self, daily_usage: Dict[str, int], day_of_week: int) -> float:
        """
        获取季节性调整因子
        
        参数:
            daily_usage: 按日期分组的使用数据
            day_of_week: 星期几（0-6）
            
        返回:
            float: 季节性因子
        """
        # 计算每个星期几的平均使用量
        weekday_usage = defaultdict(list)
        
        for date_str, usage in daily_usage.items():
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            weekday = date_obj.weekday()
            weekday_usage[weekday].append(usage)
        
        # 计算平均值
        weekday_averages = {}
        for wd, usages in weekday_usage.items():
            weekday_averages[wd] = np.mean(usages) if usages else 0
        
        overall_average = np.mean(list(weekday_averages.values())) if weekday_averages else 1
        
        if overall_average == 0:
            return 1.0
        
        return weekday_averages.get(day_of_week, overall_average) / overall_average
    
    def _calculate_prediction_confidence(self, daily_usage: Dict[str, int]) -> float:
        """
        计算预测置信度
        
        参数:
            daily_usage: 按日期分组的使用数据
            
        返回:
            float: 置信度（0-1）
        """
        if len(daily_usage) < 7:
            return 0.3  # 数据不足，低置信度
        
        usage_values = list(daily_usage.values())
        
        # 基于数据稳定性计算置信度
        cv = self._calculate_coefficient_of_variation(usage_values)
        
        # 数据量因子
        data_factor = min(1.0, len(usage_values) / 30)
        
        # 稳定性因子
        stability_factor = max(0.1, 1 - cv)
        
        confidence = data_factor * stability_factor
        return min(1.0, confidence)
    
    def _predict_trend_direction(self, daily_usage: Dict[str, int]) -> str:
        """
        预测趋势方向
        
        参数:
            daily_usage: 按日期分组的使用数据
            
        返回:
            str: 趋势方向
        """
        if len(daily_usage) < 7:
            return 'stable'
        
        sorted_dates = sorted(daily_usage.keys())
        recent_values = [daily_usage[date] for date in sorted_dates[-14:]]  # 最近14天
        
        slope = self._calculate_linear_regression_slope(recent_values)
        
        if slope > 0.1:
            return 'increasing'
        elif slope < -0.1:
            return 'decreasing'
        else:
            return 'stable'
    
    # ==================== 相关性分析算法 ====================
    
    def _analyze_correlations(self, usage_data: List[UsageDataPoint]) -> Dict[str, float]:
        """
        分析使用数据的相关性
        
        参数:
            usage_data: 使用数据点列表
            
        返回:
            Dict: 相关性分析结果
        """
        if len(usage_data) < 10:
            return {}
        
        correlations = {}
        
        # 分析使用类型与使用频率的相关性
        type_frequency_corr = self._calculate_type_frequency_correlation(usage_data)
        correlations['type_frequency'] = type_frequency_corr
        
        # 分析时间与使用量的相关性
        time_usage_corr = self._calculate_time_usage_correlation(usage_data)
        correlations['time_usage'] = time_usage_corr
        
        # 分析题目顺序与使用频率的相关性
        order_frequency_corr = self._calculate_order_frequency_correlation(usage_data)
        correlations['order_frequency'] = order_frequency_corr
        
        return correlations
    
    def _calculate_type_frequency_correlation(self, usage_data: List[UsageDataPoint]) -> float:
        """
        计算使用类型与频率的相关性
        
        参数:
            usage_data: 使用数据点列表
            
        返回:
            float: 相关系数
        """
        # 统计各类型的使用频率
        type_counts = Counter(data.usage_type for data in usage_data)
        
        if len(type_counts) < 2:
            return 0.0
        
        # 简化的相关性计算（这里可以根据需要实现更复杂的算法）
        max_count = max(type_counts.values())
        min_count = min(type_counts.values())
        
        if max_count == 0:
            return 0.0
        
        # 计算分布的均匀性（作为相关性的一个指标）
        uniformity = 1 - (max_count - min_count) / max_count
        return round(uniformity, 3)
    
    def _calculate_time_usage_correlation(self, usage_data: List[UsageDataPoint]) -> float:
        """
        计算时间与使用量的相关性
        
        参数:
            usage_data: 使用数据点列表
            
        返回:
            float: 相关系数
        """
        # 按小时统计使用量
        hourly_usage = defaultdict(int)
        for data in usage_data:
            hour = data.date.hour
            hourly_usage[hour] += 1
        
        if len(hourly_usage) < 3:
            return 0.0
        
        hours = list(hourly_usage.keys())
        counts = list(hourly_usage.values())
        
        # 计算皮尔逊相关系数
        if len(hours) < 2:
            return 0.0
        
        correlation = np.corrcoef(hours, counts)[0, 1]
        return round(correlation if not np.isnan(correlation) else 0.0, 3)
    
    def _calculate_order_frequency_correlation(self, usage_data: List[UsageDataPoint]) -> float:
        """
        计算题目顺序与使用频率的相关性
        
        参数:
            usage_data: 使用数据点列表
            
        返回:
            float: 相关系数
        """
        # 过滤有顺序信息的数据
        ordered_data = [data for data in usage_data if data.question_order is not None]
        
        if len(ordered_data) < 5:
            return 0.0
        
        # 统计各顺序位置的使用频率
        order_counts = Counter(data.question_order for data in ordered_data)
        
        orders = list(order_counts.keys())
        counts = list(order_counts.values())
        
        if len(orders) < 2:
            return 0.0
        
        # 计算相关系数
        correlation = np.corrcoef(orders, counts)[0, 1]
        return round(correlation if not np.isnan(correlation) else 0.0, 3)
    
    # ==================== 辅助方法 ====================
    
    def _calculate_monthly_statistics(self, usage_data: List[UsageDataPoint]) -> Dict[str, int]:
        """
        计算按月份的统计数据
        
        参数:
            usage_data: 使用数据点列表
            
        返回:
            Dict: 按月份统计的使用次数
        """
        monthly_stats = defaultdict(int)
        for data_point in usage_data:
            month_key = data_point.date.strftime('%Y-%m')
            monthly_stats[month_key] += 1
        return dict(monthly_stats)
    
    def _calculate_type_statistics(self, usage_data: List[UsageDataPoint]) -> Dict[str, int]:
        """
        计算按类型的统计数据
        
        参数:
            usage_data: 使用数据点列表
            
        返回:
            Dict: 按类型统计的使用次数
        """
        type_stats = defaultdict(int)
        for data_point in usage_data:
            type_stats[data_point.usage_type] += 1
        return dict(type_stats)
    
    def _group_by_week(self, usage_data: List[UsageDataPoint]) -> Dict[str, int]:
        """
        按周分组统计
        
        参数:
            usage_data: 使用数据点列表
            
        返回:
            Dict: 按周统计的使用次数
        """
        weekly_usage = defaultdict(int)
        for data_point in usage_data:
            # 计算周的开始日期
            week_start = data_point.date - timedelta(days=data_point.date.weekday())
            week_key = week_start.strftime('%Y-W%U')
            weekly_usage[week_key] += 1
        return dict(weekly_usage)
    
    def _group_by_day(self, usage_data: List[UsageDataPoint]) -> Dict[str, int]:
        """
        按日分组统计
        
        参数:
            usage_data: 使用数据点列表
            
        返回:
            Dict: 按日统计的使用次数
        """
        daily_usage = defaultdict(int)
        for data_point in usage_data:
            day_key = data_point.date.strftime('%Y-%m-%d')
            daily_usage[day_key] += 1
        return dict(daily_usage)

# 使用示例
if __name__ == "__main__":
    # 创建算法实例
    algorithms = UsageStatisticsAlgorithms()
    
    # 示例数据
    sample_data = [
        UsageDataPoint(
            date=datetime.now() - timedelta(days=i),
            usage_count=1,
            question_id=f"q_{i}",
            paper_id=f"p_{i//5}",
            usage_type="paper_generation"
        ) for i in range(30)
    ]
    
    # 计算统计结果
    result = algorithms.calculate_usage_statistics(sample_data, 1000)
    print(f"统计结果: {result}")