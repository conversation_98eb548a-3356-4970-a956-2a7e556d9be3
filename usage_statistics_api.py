# -*- coding: utf-8 -*-
"""
题目采用率统计API模块

本模块提供题目使用统计相关的API接口，包括：
1. 题目使用记录管理
2. 统计数据查询
3. 报告生成
4. 数据分析

作者: 系统自动生成
创建时间: 2024
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
from collections import defaultdict, Counter
from sqlalchemy import func, and_, or_, desc, asc, text
from sqlalchemy.orm import Session
from models import (
    Question, Paper, QuestionBank, QuestionUsageRecord, 
    UsageStatistics, UsageReport, PaperQuestion
)

class UsageStatisticsAPI:
    """
    题目使用统计API类
    
    提供题目使用记录、统计计算、报告生成等功能
    """
    
    def __init__(self, db_session: Session):
        """
        初始化API实例
        
        参数:
            db_session: 数据库会话对象
        """
        self.db = db_session
    
    # ==================== 使用记录管理 ====================
    
    def record_question_usage(self, question_id: str, paper_id: str, 
                            usage_type: str = 'paper_generation',
                            question_order: Optional[int] = None,
                            question_score: Optional[float] = None,
                            section_name: Optional[str] = None,
                            usage_context: Optional[Dict] = None) -> str:
        """
        记录题目使用情况
        
        参数:
            question_id: 题目ID
            paper_id: 试卷ID
            usage_type: 使用类型 (paper_generation, practice, exam)
            question_order: 题目在试卷中的顺序
            question_score: 题目分值
            section_name: 题目所属章节
            usage_context: 使用上下文信息
            
        返回:
            str: 使用记录ID
            
        异常:
            ValueError: 当题目或试卷不存在时抛出
        """
        try:
            # 验证题目和试卷是否存在
            question = self.db.query(Question).filter(Question.id == question_id).first()
            if not question:
                raise ValueError(f"题目不存在: {question_id}")
            
            paper = self.db.query(Paper).filter(Paper.id == paper_id).first()
            if not paper:
                raise ValueError(f"试卷不存在: {paper_id}")
            
            # 创建使用记录
            usage_record = QuestionUsageRecord(
                id=str(uuid.uuid4()),
                question_id=question_id,
                paper_id=paper_id,
                question_bank_id=question.question_bank_id,
                usage_date=datetime.utcnow(),
                usage_type=usage_type,
                question_order=question_order,
                question_score=question_score,
                section_name=section_name,
                usage_context=usage_context or {}
            )
            
            self.db.add(usage_record)
            
            # 更新题目的使用次数
            question.usage_count = (question.usage_count or 0) + 1
            question.last_used_date = datetime.utcnow()
            
            # 更新或创建统计记录
            self._update_usage_statistics(question_id)
            
            self.db.commit()
            return usage_record.id
            
        except Exception as e:
            self.db.rollback()
            raise Exception(f"记录题目使用失败: {str(e)}")
    
    def batch_record_usage(self, usage_records: List[Dict]) -> List[str]:
        """
        批量记录题目使用情况
        
        参数:
            usage_records: 使用记录列表，每个记录包含题目ID、试卷ID等信息
            
        返回:
            List[str]: 创建的使用记录ID列表
        """
        record_ids = []
        try:
            for record_data in usage_records:
                record_id = self.record_question_usage(**record_data)
                record_ids.append(record_id)
            return record_ids
        except Exception as e:
            self.db.rollback()
            raise Exception(f"批量记录使用失败: {str(e)}")
    
    # ==================== 统计数据查询 ====================
    
    def get_question_usage_stats(self, question_id: str) -> Dict[str, Any]:
        """
        获取单个题目的使用统计信息
        
        参数:
            question_id: 题目ID
            
        返回:
            Dict: 包含使用次数、使用率、趋势等统计信息
        """
        try:
            # 获取题目基本信息
            question = self.db.query(Question).filter(Question.id == question_id).first()
            if not question:
                raise ValueError(f"题目不存在: {question_id}")
            
            # 获取统计记录
            stats = self.db.query(UsageStatistics).filter(
                UsageStatistics.question_id == question_id
            ).first()
            
            # 获取使用记录
            usage_records = self.db.query(QuestionUsageRecord).filter(
                QuestionUsageRecord.question_id == question_id
            ).order_by(desc(QuestionUsageRecord.usage_date)).all()
            
            # 计算实时统计
            total_usage = len(usage_records)
            unique_papers = len(set(record.paper_id for record in usage_records))
            
            # 按月份统计
            usage_by_month = self._calculate_usage_by_month(usage_records)
            
            # 按类型统计
            usage_by_type = self._calculate_usage_by_type(usage_records)
            
            # 计算使用率（相对于题库总题目数）
            total_questions = self.db.query(Question).filter(
                Question.question_bank_id == question.question_bank_id
            ).count()
            
            usage_rate = (total_usage / max(total_questions, 1)) * 100
            
            return {
                'question_id': question_id,
                'question_stem': question.question_stem[:100] + '...' if len(question.question_stem) > 100 else question.question_stem,
                'question_bank_id': question.question_bank_id,
                'total_usage_count': total_usage,
                'usage_rate': round(usage_rate, 2),
                'unique_papers_count': unique_papers,
                'last_used_date': usage_records[0].usage_date.isoformat() if usage_records else None,
                'first_used_date': usage_records[-1].usage_date.isoformat() if usage_records else None,
                'usage_by_month': usage_by_month,
                'usage_by_type': usage_by_type,
                'recent_usage': [
                    {
                        'paper_id': record.paper_id,
                        'usage_date': record.usage_date.isoformat(),
                        'usage_type': record.usage_type,
                        'question_order': record.question_order,
                        'question_score': record.question_score
                    } for record in usage_records[:10]
                ],
                'stats_last_updated': stats.last_calculated.isoformat() if stats else None
            }
            
        except Exception as e:
            raise Exception(f"获取题目统计失败: {str(e)}")
    
    def get_question_bank_usage_overview(self, question_bank_id: str, 
                                       date_range: Optional[Tuple[datetime, datetime]] = None) -> Dict[str, Any]:
        """
        获取题库使用概览统计
        
        参数:
            question_bank_id: 题库ID
            date_range: 日期范围 (开始日期, 结束日期)
            
        返回:
            Dict: 题库使用概览统计信息
        """
        try:
            # 构建查询条件
            query_conditions = [QuestionUsageRecord.question_bank_id == question_bank_id]
            if date_range:
                start_date, end_date = date_range
                query_conditions.append(QuestionUsageRecord.usage_date >= start_date)
                query_conditions.append(QuestionUsageRecord.usage_date <= end_date)
            
            # 获取使用记录
            usage_records = self.db.query(QuestionUsageRecord).filter(
                and_(*query_conditions)
            ).all()
            
            # 获取题库基本信息
            question_bank = self.db.query(QuestionBank).filter(
                QuestionBank.id == question_bank_id
            ).first()
            
            if not question_bank:
                raise ValueError(f"题库不存在: {question_bank_id}")
            
            # 获取题库总题目数
            total_questions = self.db.query(Question).filter(
                Question.question_bank_id == question_bank_id
            ).count()
            
            # 计算统计指标
            total_usage = len(usage_records)
            used_questions = len(set(record.question_id for record in usage_records))
            unique_papers = len(set(record.paper_id for record in usage_records))
            
            # 计算使用率
            question_usage_rate = (used_questions / max(total_questions, 1)) * 100
            
            # 按题型统计
            usage_by_type = self._calculate_usage_by_question_type(usage_records)
            
            # 按难度统计
            usage_by_difficulty = self._calculate_usage_by_difficulty(usage_records)
            
            # 按月份统计
            usage_by_month = self._calculate_usage_by_month(usage_records)
            
            # 最活跃题目
            most_used_questions = self._get_most_used_questions(question_bank_id, limit=10)
            
            # 最少使用题目
            least_used_questions = self._get_least_used_questions(question_bank_id, limit=10)
            
            return {
                'question_bank_id': question_bank_id,
                'question_bank_name': question_bank.name,
                'total_questions': total_questions,
                'used_questions': used_questions,
                'unused_questions': total_questions - used_questions,
                'question_usage_rate': round(question_usage_rate, 2),
                'total_usage_count': total_usage,
                'unique_papers_count': unique_papers,
                'avg_usage_per_question': round(total_usage / max(used_questions, 1), 2),
                'usage_by_type': usage_by_type,
                'usage_by_difficulty': usage_by_difficulty,
                'usage_by_month': usage_by_month,
                'most_used_questions': most_used_questions,
                'least_used_questions': least_used_questions,
                'date_range': {
                    'start': date_range[0].isoformat() if date_range else None,
                    'end': date_range[1].isoformat() if date_range else None
                }
            }
            
        except Exception as e:
            raise Exception(f"获取题库概览失败: {str(e)}")
    
    # ==================== 报告生成 ====================
    
    def generate_usage_report(self, report_type: str, 
                            question_bank_id: Optional[str] = None,
                            date_range: Optional[Tuple[datetime, datetime]] = None,
                            filters: Optional[Dict] = None,
                            report_name: Optional[str] = None) -> str:
        """
        生成使用统计报告
        
        参数:
            report_type: 报告类型 (overview, detailed, trend, comparison)
            question_bank_id: 题库ID（可选）
            date_range: 日期范围
            filters: 过滤条件
            report_name: 报告名称
            
        返回:
            str: 报告ID
        """
        try:
            # 生成报告数据
            if report_type == 'overview':
                report_data = self._generate_overview_report(question_bank_id, date_range, filters)
            elif report_type == 'detailed':
                report_data = self._generate_detailed_report(question_bank_id, date_range, filters)
            elif report_type == 'trend':
                report_data = self._generate_trend_report(question_bank_id, date_range, filters)
            elif report_type == 'comparison':
                report_data = self._generate_comparison_report(question_bank_id, date_range, filters)
            else:
                raise ValueError(f"不支持的报告类型: {report_type}")
            
            # 创建报告记录
            report = UsageReport(
                id=str(uuid.uuid4()),
                report_name=report_name or f"{report_type}_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                report_type=report_type,
                question_bank_id=question_bank_id,
                date_range_start=date_range[0] if date_range else None,
                date_range_end=date_range[1] if date_range else None,
                report_data=report_data,
                summary_stats=report_data.get('summary', {}),
                chart_data=report_data.get('charts', {}),
                filters_applied=filters or {},
                generated_at=datetime.utcnow()
            )
            
            self.db.add(report)
            self.db.commit()
            
            return report.id
            
        except Exception as e:
            self.db.rollback()
            raise Exception(f"生成报告失败: {str(e)}")
    
    def get_report(self, report_id: str) -> Dict[str, Any]:
        """
        获取报告详情
        
        参数:
            report_id: 报告ID
            
        返回:
            Dict: 报告详情
        """
        try:
            report = self.db.query(UsageReport).filter(UsageReport.id == report_id).first()
            if not report:
                raise ValueError(f"报告不存在: {report_id}")
            
            return {
                'id': report.id,
                'name': report.report_name,
                'type': report.report_type,
                'question_bank_id': report.question_bank_id,
                'date_range': {
                    'start': report.date_range_start.isoformat() if report.date_range_start else None,
                    'end': report.date_range_end.isoformat() if report.date_range_end else None
                },
                'data': report.report_data,
                'summary': report.summary_stats,
                'charts': report.chart_data,
                'filters': report.filters_applied,
                'generated_at': report.generated_at.isoformat(),
                'generated_by': report.generated_by
            }
            
        except Exception as e:
            raise Exception(f"获取报告失败: {str(e)}")
    
    # ==================== 私有辅助方法 ====================
    
    def _update_usage_statistics(self, question_id: str):
        """
        更新题目使用统计记录
        
        参数:
            question_id: 题目ID
        """
        # 获取或创建统计记录
        stats = self.db.query(UsageStatistics).filter(
            UsageStatistics.question_id == question_id
        ).first()
        
        if not stats:
            question = self.db.query(Question).filter(Question.id == question_id).first()
            stats = UsageStatistics(
                id=str(uuid.uuid4()),
                question_id=question_id,
                question_bank_id=question.question_bank_id
            )
            self.db.add(stats)
        
        # 重新计算统计数据
        usage_records = self.db.query(QuestionUsageRecord).filter(
            QuestionUsageRecord.question_id == question_id
        ).all()
        
        stats.total_usage_count = len(usage_records)
        stats.paper_count = len(set(record.paper_id for record in usage_records))
        stats.unique_papers = [record.paper_id for record in usage_records]
        
        if usage_records:
            stats.last_used_date = max(record.usage_date for record in usage_records)
            stats.first_used_date = min(record.usage_date for record in usage_records)
        
        # 计算月平均使用次数
        if usage_records and stats.first_used_date:
            months_diff = max(1, (datetime.utcnow() - stats.first_used_date).days / 30)
            stats.avg_usage_per_month = stats.total_usage_count / months_diff
        
        # 计算使用趋势
        stats.usage_trend = self._calculate_usage_trend(usage_records)
        
        # 按月份和类型统计
        stats.usage_by_month = self._calculate_usage_by_month(usage_records)
        stats.usage_by_type = self._calculate_usage_by_type(usage_records)
        
        # 计算使用率
        total_questions = self.db.query(Question).filter(
            Question.question_bank_id == stats.question_bank_id
        ).count()
        stats.usage_rate = (stats.total_usage_count / max(total_questions, 1)) * 100
        
        stats.last_calculated = datetime.utcnow()
    
    def _calculate_usage_by_month(self, usage_records: List[QuestionUsageRecord]) -> Dict[str, int]:
        """
        按月份统计使用次数
        
        参数:
            usage_records: 使用记录列表
            
        返回:
            Dict: 按月份统计的使用次数
        """
        monthly_usage = defaultdict(int)
        for record in usage_records:
            month_key = record.usage_date.strftime('%Y-%m')
            monthly_usage[month_key] += 1
        return dict(monthly_usage)
    
    def _calculate_usage_by_type(self, usage_records: List[QuestionUsageRecord]) -> Dict[str, int]:
        """
        按使用类型统计使用次数
        
        参数:
            usage_records: 使用记录列表
            
        返回:
            Dict: 按使用类型统计的使用次数
        """
        type_usage = defaultdict(int)
        for record in usage_records:
            type_usage[record.usage_type] += 1
        return dict(type_usage)
    
    def _calculate_usage_trend(self, usage_records: List[QuestionUsageRecord]) -> str:
        """
        计算使用趋势
        
        参数:
            usage_records: 使用记录列表
            
        返回:
            str: 趋势描述 (increasing, decreasing, stable)
        """
        if len(usage_records) < 6:
            return 'stable'
        
        # 按月份分组
        monthly_usage = self._calculate_usage_by_month(usage_records)
        months = sorted(monthly_usage.keys())
        
        if len(months) < 3:
            return 'stable'
        
        # 计算最近3个月和之前3个月的平均使用量
        recent_months = months[-3:]
        previous_months = months[-6:-3] if len(months) >= 6 else months[:-3]
        
        recent_avg = sum(monthly_usage[month] for month in recent_months) / len(recent_months)
        previous_avg = sum(monthly_usage[month] for month in previous_months) / len(previous_months)
        
        if recent_avg > previous_avg * 1.2:
            return 'increasing'
        elif recent_avg < previous_avg * 0.8:
            return 'decreasing'
        else:
            return 'stable'
    
    def _calculate_usage_by_question_type(self, usage_records: List[QuestionUsageRecord]) -> Dict[str, int]:
        """
        按题型统计使用次数
        
        参数:
            usage_records: 使用记录列表
            
        返回:
            Dict: 按题型统计的使用次数
        """
        type_usage = defaultdict(int)
        for record in usage_records:
            question = self.db.query(Question).filter(Question.id == record.question_id).first()
            if question:
                type_usage[question.question_type_code] += 1
        return dict(type_usage)
    
    def _calculate_usage_by_difficulty(self, usage_records: List[QuestionUsageRecord]) -> Dict[str, int]:
        """
        按难度统计使用次数
        
        参数:
            usage_records: 使用记录列表
            
        返回:
            Dict: 按难度统计的使用次数
        """
        difficulty_usage = defaultdict(int)
        for record in usage_records:
            question = self.db.query(Question).filter(Question.id == record.question_id).first()
            if question:
                difficulty_usage[question.difficulty_code] += 1
        return dict(difficulty_usage)
    
    def _get_most_used_questions(self, question_bank_id: str, limit: int = 10) -> List[Dict]:
        """
        获取最常使用的题目
        
        参数:
            question_bank_id: 题库ID
            limit: 返回数量限制
            
        返回:
            List[Dict]: 最常使用的题目列表
        """
        questions = self.db.query(Question).filter(
            Question.question_bank_id == question_bank_id
        ).order_by(desc(Question.usage_count)).limit(limit).all()
        
        return [
            {
                'question_id': q.id,
                'question_stem': q.question_stem[:100] + '...' if len(q.question_stem) > 100 else q.question_stem,
                'usage_count': q.usage_count or 0,
                'last_used_date': q.last_used_date.isoformat() if q.last_used_date else None,
                'question_type': q.question_type_code,
                'difficulty': q.difficulty_code
            } for q in questions
        ]
    
    def _get_least_used_questions(self, question_bank_id: str, limit: int = 10) -> List[Dict]:
        """
        获取最少使用的题目
        
        参数:
            question_bank_id: 题库ID
            limit: 返回数量限制
            
        返回:
            List[Dict]: 最少使用的题目列表
        """
        questions = self.db.query(Question).filter(
            Question.question_bank_id == question_bank_id
        ).order_by(asc(Question.usage_count)).limit(limit).all()
        
        return [
            {
                'question_id': q.id,
                'question_stem': q.question_stem[:100] + '...' if len(q.question_stem) > 100 else q.question_stem,
                'usage_count': q.usage_count or 0,
                'last_used_date': q.last_used_date.isoformat() if q.last_used_date else None,
                'question_type': q.question_type_code,
                'difficulty': q.difficulty_code
            } for q in questions
        ]
    
    # ==================== 报告生成私有方法 ====================
    
    def _generate_overview_report(self, question_bank_id: Optional[str], 
                                date_range: Optional[Tuple[datetime, datetime]], 
                                filters: Optional[Dict]) -> Dict[str, Any]:
        """
        生成概览报告
        
        参数:
            question_bank_id: 题库ID
            date_range: 日期范围
            filters: 过滤条件
            
        返回:
            Dict: 概览报告数据
        """
        if question_bank_id:
            overview_data = self.get_question_bank_usage_overview(question_bank_id, date_range)
        else:
            # 全局概览
            overview_data = self._get_global_usage_overview(date_range)
        
        return {
            'type': 'overview',
            'data': overview_data,
            'summary': {
                'total_questions': overview_data.get('total_questions', 0),
                'used_questions': overview_data.get('used_questions', 0),
                'usage_rate': overview_data.get('question_usage_rate', 0),
                'total_usage_count': overview_data.get('total_usage_count', 0)
            },
            'charts': {
                'usage_by_month': overview_data.get('usage_by_month', {}),
                'usage_by_type': overview_data.get('usage_by_type', {}),
                'usage_by_difficulty': overview_data.get('usage_by_difficulty', {})
            }
        }
    
    def _generate_detailed_report(self, question_bank_id: Optional[str], 
                                date_range: Optional[Tuple[datetime, datetime]], 
                                filters: Optional[Dict]) -> Dict[str, Any]:
        """
        生成详细报告
        
        参数:
            question_bank_id: 题库ID
            date_range: 日期范围
            filters: 过滤条件
            
        返回:
            Dict: 详细报告数据
        """
        # 获取详细的题目使用数据
        detailed_data = self._get_detailed_usage_data(question_bank_id, date_range, filters)
        
        return {
            'type': 'detailed',
            'data': detailed_data,
            'summary': detailed_data.get('summary', {}),
            'charts': detailed_data.get('charts', {})
        }
    
    def _generate_trend_report(self, question_bank_id: Optional[str], 
                             date_range: Optional[Tuple[datetime, datetime]], 
                             filters: Optional[Dict]) -> Dict[str, Any]:
        """
        生成趋势报告
        
        参数:
            question_bank_id: 题库ID
            date_range: 日期范围
            filters: 过滤条件
            
        返回:
            Dict: 趋势报告数据
        """
        # 获取趋势数据
        trend_data = self._get_usage_trend_data(question_bank_id, date_range, filters)
        
        return {
            'type': 'trend',
            'data': trend_data,
            'summary': trend_data.get('summary', {}),
            'charts': trend_data.get('charts', {})
        }
    
    def _generate_comparison_report(self, question_bank_id: Optional[str], 
                                  date_range: Optional[Tuple[datetime, datetime]], 
                                  filters: Optional[Dict]) -> Dict[str, Any]:
        """
        生成对比报告
        
        参数:
            question_bank_id: 题库ID
            date_range: 日期范围
            filters: 过滤条件
            
        返回:
            Dict: 对比报告数据
        """
        # 获取对比数据
        comparison_data = self._get_usage_comparison_data(question_bank_id, date_range, filters)
        
        return {
            'type': 'comparison',
            'data': comparison_data,
            'summary': comparison_data.get('summary', {}),
            'charts': comparison_data.get('charts', {})
        }
    
    def _get_global_usage_overview(self, date_range: Optional[Tuple[datetime, datetime]]) -> Dict[str, Any]:
        """
        获取全局使用概览
        
        参数:
            date_range: 日期范围
            
        返回:
            Dict: 全局概览数据
        """
        # 实现全局统计逻辑
        # 这里可以根据需要实现具体的全局统计功能
        return {
            'total_questions': 0,
            'used_questions': 0,
            'question_usage_rate': 0,
            'total_usage_count': 0
        }
    
    def _get_detailed_usage_data(self, question_bank_id: Optional[str], 
                               date_range: Optional[Tuple[datetime, datetime]], 
                               filters: Optional[Dict]) -> Dict[str, Any]:
        """
        获取详细使用数据
        
        参数:
            question_bank_id: 题库ID
            date_range: 日期范围
            filters: 过滤条件
            
        返回:
            Dict: 详细数据
        """
        # 实现详细数据获取逻辑
        return {
            'summary': {},
            'charts': {}
        }
    
    def _get_usage_trend_data(self, question_bank_id: Optional[str], 
                            date_range: Optional[Tuple[datetime, datetime]], 
                            filters: Optional[Dict]) -> Dict[str, Any]:
        """
        获取使用趋势数据
        
        参数:
            question_bank_id: 题库ID
            date_range: 日期范围
            filters: 过滤条件
            
        返回:
            Dict: 趋势数据
        """
        # 实现趋势数据获取逻辑
        return {
            'summary': {},
            'charts': {}
        }
    
    def _get_usage_comparison_data(self, question_bank_id: Optional[str], 
                                 date_range: Optional[Tuple[datetime, datetime]], 
                                 filters: Optional[Dict]) -> Dict[str, Any]:
        """
        获取使用对比数据
        
        参数:
            question_bank_id: 题库ID
            date_range: 日期范围
            filters: 过滤条件
            
        返回:
            Dict: 对比数据
        """
        # 实现对比数据获取逻辑
        return {
            'summary': {},
            'charts': {}
        }

# 使用示例
if __name__ == "__main__":
    # 这里可以添加测试代码
    pass