#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查local_dev.db数据库
"""

import sqlite3

def check_local_dev_db():
    """
    检查local_dev.db数据库
    """
    try:
        conn = sqlite3.connect('local_dev.db')
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"local_dev.db中的表 ({len(tables)} 个):")
        for table in tables:
            print(f"  - {table[0]}")
        
        # 检查question_banks表内容
        cursor.execute("SELECT * FROM question_banks")
        banks = cursor.fetchall()
        print(f"\nquestion_banks表内容 ({len(banks)} 个题库):")
        for bank in banks:
            print(f"  {bank}")
        
        # 检查questions表结构
        cursor.execute('PRAGMA table_info(questions)')
        columns = cursor.fetchall()
        
        print(f"\nquestions表的列结构 ({len(columns)} 个列):")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 检查是否有status字段
        column_names = [col[1] for col in columns]
        if 'status' in column_names:
            print("\n✅ status字段存在")
        else:
            print("\n❌ status字段不存在")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == '__main__':
    check_local_dev_db()