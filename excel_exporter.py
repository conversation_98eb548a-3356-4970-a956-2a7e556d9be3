import pandas as pd
import os
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from models import Question, QuestionBank
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows

def apply_excel_template_style(df, output_path, sheet_name="题库", workbook=None, is_paper_rule=False):
    """应用Excel模板样式"""
    if workbook is None:
        wb = Workbook()
    else:
        wb = workbook
    
    # 如果是新工作簿，使用active工作表；否则创建新工作表
    if workbook is None:
        ws = wb.active
        ws.title = sheet_name
    else:
        if sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
        else:
            ws = wb.create_sheet(sheet_name)
    
    # 设置样式
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
    
    # 分离中英文表头
    headers = df.columns.tolist()
    chinese_headers = [h.split('\n')[0] if '\n' in h else h for h in headers]
    english_headers = [h.split('\n')[1] if '\n' in h else h for h in headers]
    
    # 添加中英文表头
    ws.append(chinese_headers)
    ws.append(english_headers)
    
    # 添加数据，从第3行开始
    for index, row in df.iterrows():
        ws.append([row[col] for col in df.columns])
    
    # 设置表头样式（仅应用于第1、2行）
    for row in [ws[1], ws[2]]:
        for cell in row:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
    
    # 根据不同的模板类型设置列宽
    if is_paper_rule:
        column_widths = {
            'A': 20,  # 题库名称
            'B': 15,  # 题型代码
            'C': 15,  # 认定点代码
            'D': 10,  # 难度代码
            'E': 10,  # 题目数量
            'F': 30   # 备注
        }
    else:
        column_widths = {
            'A': 20,  # 题库名称
            'B': 20,  # ID
            'C': 10,  # 序号
            'D': 15,  # 认定点代码
            'E': 12,  # 题型代码
            'F': 10,  # 题号
            'G': 50,  # 试题（题干）
            'H': 20,  # 试题（选项A）
            'I': 20,  # 试题（选项B）
            'J': 20,  # 试题（选项C）
            'K': 20,  # 试题（选项D）
            'L': 20,  # 试题（选项E）
            'M': 15,  # 【图】及位置
            'N': 15,  # 正确答案
            'O': 12,  # 难度代码
            'P': 12,  # 一致性代码
            'Q': 30   # 解析
        }
    
    # 设置列宽
    for col, width in column_widths.items():
        ws.column_dimensions[col].width = width
    
    # 如果提供了输出路径，保存文件
    if output_path:
        wb.save(output_path)
    
    return wb

def export_questions_to_excel(questions, output_path, bank_name="样例题库"):
    """
    将题目列表导出为Excel文件
    """
    # 准备数据框架
    data = []
    for q in questions:
        # 处理选项，将选项对象转换为字符串
        options = q.get("options", [])
        option_a = next((opt['text'] for opt in options if opt['key'] == 'A'), "")
        option_b = next((opt['text'] for opt in options if opt['key'] == 'B'), "")
        option_c = next((opt['text'] for opt in options if opt['key'] == 'C'), "")
        option_d = next((opt['text'] for opt in options if opt['key'] == 'D'), "")
        option_e = next((opt['text'] for opt in options if opt['key'] == 'E'), "")
        
        # 创建行数据
        row = {
            '题库名称\nquestion_bank_name': bank_name,
            '试题ID\nquestion_id': q.get("id", ""),
            '序号\nserial_number': "",
            '认定点代码\nidentification_point_code': "",
            '题型代码\nquestion_type_code': q.get("type_name", ""),
            '题号\nquestion_number': "",
            '试题（题干）\nquestion_stem': q.get("stem", ""),
            '试题（选项 A）\nquestion_option_a': option_a,
            '试题（选项 B）\nquestion_option_b': option_b,
            '试题（选项 C）\nquestion_option_c': option_c,
            '试题（选项 D）\nquestion_option_d': option_d,
            '试题（选项 E）\nquestion_option_e': option_e,
            '【图】及位置\nimage_and_position': "",
            '正确答案\ncorrect_answer': q.get("answer", ""),
            '难度代码\ndifficulty_code': f"{int(float(q.get('difficulty', 0.5))*5)}（{'很简单' if float(q.get('difficulty', 0.5)) < 0.2 else '简单' if float(q.get('difficulty', 0.5)) < 0.4 else '中等' if float(q.get('difficulty', 0.5)) < 0.6 else '困难' if float(q.get('difficulty', 0.5)) < 0.8 else '很难'}）",
            '一致性代码\nconsistency_code': "3（中等）",
            '解析\nexplanation': q.get("explanation", "")
        }
        data.append(row)
    
    # 创建DataFrame并导出为Excel
    df = pd.DataFrame(data)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 应用Excel模板样式
    apply_excel_template_style(df, output_path)
    
    return len(data)

def export_db_questions_to_excel(db_session, output_path, bank_name=None):
    """
    从数据库导出题目到Excel文件
    """
    # 查询题目
    query = db_session.query(Question)
    if bank_name:
        bank = db_session.query(QuestionBank).filter_by(题库名称=bank_name).first()
        if bank:
            query = query.filter_by(question_bank_id=bank.id)
    
    questions = query.all()
    
    # 准备数据框架
    data = []
    for q in questions:
        # 创建行数据
        row = {
            '题库名称\nquestion_bank_name': q.question_bank.name if q.question_bank else "",
            '试题ID\nquestion_id': q.id,
            '序号\nserial_number': "",
            '认定点代码\nidentification_point_code': "",
            '题型代码\nquestion_type_code': q.question_type_code,
            '题号\nquestion_number': "",
            '试题（题干）\nquestion_stem': q.stem,
            '试题（选项 A）\nquestion_option_a': q.option_a,
            '试题（选项 B）\nquestion_option_b': q.option_b,
            '试题（选项 C）\nquestion_option_c': q.option_c,
            '试题（选项 D）\nquestion_option_d': q.option_d,
            '试题（选项 E）\nquestion_option_e': q.option_e,
            '【图】及位置\nimage_and_position': q.image_info,
            '正确答案\ncorrect_answer': q.correct_answer,
            '难度代码\ndifficulty_code': q.difficulty_code,
            '一致性代码\nconsistency_code': q.consistency_code,
            '解析\nexplanation': q.analysis
        }
        data.append(row)
    
    # 创建DataFrame并导出为Excel
    df = pd.DataFrame(data)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 应用Excel模板样式
    apply_excel_template_style(df, output_path)
    
    return len(data)

def export_paper_rules_to_excel(rules, output_path):
    """
    将组卷规则导出为Excel文件
    """
    # 准备数据框架
    data = []
    for rule in rules:
        row = {
            '题库名称\nquestion_bank_name': rule.get('bank_name', ''),
            '题型代码\nquestion_type_code': rule.get('question_type', ''),
            '认定点代码\nidentification_point_code': rule.get('cert_point', ''),
            '难度代码\ndifficulty_code': rule.get('difficulty', ''),
            '题量\nquestion_count': rule.get('count', 0),
            '分值\nscore_value': rule.get('score_per_question', 0),
            '备注\nremarks': rule.get('remarks', '')
        }
        data.append(row)
    
    # 创建DataFrame并导出为Excel
    df = pd.DataFrame(data)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 应用Excel模板样式，指定为组卷规则模板
    apply_excel_template_style(df, output_path, sheet_name="组卷规则", is_paper_rule=True)
    
    return len(data)

# 此模块用于将题目数据导出到Excel文件
# 可通过其他模块调用相关导出函数