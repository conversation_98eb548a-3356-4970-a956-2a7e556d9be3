#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库表创建脚本
"""

import os
from sqlalchemy import create_engine
from models import Base, QuestionBank, Question, Paper, PaperQuestion, QuestionGroup, DuplicateRecord

def create_tables():
    """
    创建数据库表
    """
    try:
        # 使用SQLite数据库
        database_url = 'sqlite:///local_dev.db'
        
        # 创建数据库引擎
        engine = create_engine(database_url, echo=False)
        
        print("正在创建数据库表...")
        
        # 创建所有表
        Base.metadata.create_all(engine)
        
        print("✅ 数据库表创建成功！")
        print("已创建的表:")
        print("  - question_banks (题库表)")
        print("  - questions (题目表)")
        print("  - papers (试卷表)")
        print("  - paper_questions (试卷题目关联表)")
        print("  - question_groups (题目分组表)")
        print("  - duplicate_records (重复题目检测记录表)")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库表失败: {e}")
        return False

def check_and_create_sample_data():
    """
    检查并创建示例数据
    """
    try:
        from sqlalchemy.orm import sessionmaker
        
        database_url = 'sqlite:///local_dev.db'
        engine = create_engine(database_url, echo=False)
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # 检查是否已有题库数据
        existing_banks = session.query(QuestionBank).count()
        
        if existing_banks == 0:
            print("正在创建示例题库...")
            
            # 创建示例题库
            sample_bank = QuestionBank(题库名称="示例题库")
            session.add(sample_bank)
            session.commit()
            
            print("✅ 示例题库创建成功！")
        else:
            print(f"✅ 数据库中已有 {existing_banks} 个题库")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建示例数据失败: {e}")
        return False

if __name__ == '__main__':
    print("=" * 50)
    print("数据库初始化脚本")
    print("=" * 50)
    
    # 创建表
    if create_tables():
        print("\n" + "=" * 50)
        # 创建示例数据
        check_and_create_sample_data()
        print("=" * 50)
        print("数据库初始化完成！")
    else:
        print("数据库初始化失败！")