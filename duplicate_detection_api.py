# -*- coding: utf-8 -*-
"""
重复题目检测API模块

本模块提供重复题目检测的API接口，包括：
1. 重复检测任务的创建和管理
2. 检测结果的查询和展示
3. 重复题目的批量操作（删除、编辑）
4. 检测历史记录和统计

作者：SOLO Coding
创建时间：2024
"""

import uuid
import datetime
import json
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from similarity_algorithms import QuestionSimilarityDetector, SimilarityCalculator

class DetectionStatus(Enum):
    """检测状态枚举"""
    PENDING = "pending"        # 等待中
    RUNNING = "running"        # 运行中
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"          # 失败
    CANCELLED = "cancelled"    # 已取消

class DuplicateAction(Enum):
    """重复题目处理动作枚举"""
    KEEP_FIRST = "keep_first"      # 保留第一个
    KEEP_LAST = "keep_last"        # 保留最后一个
    KEEP_BEST = "keep_best"        # 保留最佳质量
    MANUAL_SELECT = "manual_select" # 手动选择
    MERGE = "merge"                # 合并题目
    MARK_DUPLICATE = "mark_duplicate" # 标记为重复

@dataclass
class DetectionTask:
    """检测任务数据类"""
    task_id: str
    question_bank_ids: List[str]
    similarity_threshold: float
    detection_scope: str  # 'within_bank', 'cross_bank', 'all'
    algorithms: List[str]
    algorithm_weights: Dict[str, float]
    status: DetectionStatus
    created_at: datetime.datetime
    started_at: Optional[datetime.datetime] = None
    completed_at: Optional[datetime.datetime] = None
    progress: float = 0.0
    total_questions: int = 0
    processed_questions: int = 0
    duplicates_found: int = 0
    error_message: Optional[str] = None
    result_summary: Optional[Dict[str, Any]] = None

@dataclass
class DuplicateGroup:
    """重复题目组数据类"""
    group_id: str
    questions: List[Dict[str, Any]]
    similarity_score: float
    similarity_details: Dict[str, float]
    detection_algorithms: List[str]
    group_type: str  # 'exact', 'high_similarity', 'moderate_similarity'
    recommended_action: DuplicateAction
    created_at: datetime.datetime

class DuplicateDetectionAPI:
    """
    重复题目检测API类
    
    提供重复题目检测的完整API接口。
    """
    
    def __init__(self):
        """
        初始化重复检测API
        """
        self.detector = QuestionSimilarityDetector()
        self.calculator = SimilarityCalculator()
        self.active_tasks = {}  # 活跃任务字典
        self.task_results = {}  # 任务结果缓存
        self.executor = ThreadPoolExecutor(max_workers=3)  # 线程池
        self._lock = threading.Lock()  # 线程锁
    
    def create_detection_task(self, question_bank_ids: List[str],
                             similarity_threshold: float = 0.8,
                             detection_scope: str = "within_bank",
                             algorithms: Optional[List[str]] = None,
                             algorithm_weights: Optional[Dict[str, float]] = None) -> str:
        """
        创建重复检测任务
        
        Args:
            question_bank_ids: 题库ID列表
            similarity_threshold: 相似度阈值 (0.5-1.0)
            detection_scope: 检测范围 ('within_bank', 'cross_bank', 'all')
            algorithms: 使用的算法列表
            algorithm_weights: 算法权重
            
        Returns:
            str: 任务ID
            
        Raises:
            ValueError: 参数验证失败
        """
        # 参数验证
        if not question_bank_ids:
            raise ValueError("题库ID列表不能为空")
        
        if not (0.5 <= similarity_threshold <= 1.0):
            raise ValueError("相似度阈值必须在0.5-1.0之间")
        
        if detection_scope not in ['within_bank', 'cross_bank', 'all']:
            raise ValueError("检测范围必须是 'within_bank', 'cross_bank', 'all' 之一")
        
        # 默认算法配置
        if algorithms is None:
            algorithms = ['edit_distance', 'cosine_similarity', 'jaccard_similarity', 'fuzzy_match']
        
        if algorithm_weights is None:
            algorithm_weights = {
                'edit_distance': 0.25,
                'cosine_similarity': 0.3,
                'jaccard_similarity': 0.25,
                'fuzzy_match': 0.2
            }
        
        # 创建任务
        task_id = str(uuid.uuid4())
        task = DetectionTask(
            task_id=task_id,
            question_bank_ids=question_bank_ids,
            similarity_threshold=similarity_threshold,
            detection_scope=detection_scope,
            algorithms=algorithms,
            algorithm_weights=algorithm_weights,
            status=DetectionStatus.PENDING,
            created_at=datetime.datetime.utcnow()
        )
        
        with self._lock:
            self.active_tasks[task_id] = task
        
        return task_id
    
    def start_detection_task(self, task_id: str, questions_data: List[Dict[str, Any]]) -> bool:
        """
        启动检测任务
        
        Args:
            task_id: 任务ID
            questions_data: 题目数据列表
            
        Returns:
            bool: 是否成功启动
        """
        with self._lock:
            if task_id not in self.active_tasks:
                return False
            
            task = self.active_tasks[task_id]
            if task.status != DetectionStatus.PENDING:
                return False
            
            task.status = DetectionStatus.RUNNING
            task.started_at = datetime.datetime.utcnow()
            task.total_questions = len(questions_data)
        
        # 在线程池中执行检测
        future = self.executor.submit(self._execute_detection, task_id, questions_data)
        
        return True
    
    def _execute_detection(self, task_id: str, questions_data: List[Dict[str, Any]]) -> None:
        """
        执行重复检测任务
        
        Args:
            task_id: 任务ID
            questions_data: 题目数据列表
        """
        try:
            task = self.active_tasks[task_id]
            
            # 根据检测范围过滤题目
            filtered_questions = self._filter_questions_by_scope(questions_data, task)
            
            # 执行相似度检测
            duplicate_groups = []
            total_comparisons = len(filtered_questions) * (len(filtered_questions) - 1) // 2
            completed_comparisons = 0
            
            for i in range(len(filtered_questions)):
                for j in range(i + 1, len(filtered_questions)):
                    q1 = filtered_questions[i]
                    q2 = filtered_questions[j]
                    
                    # 计算相似度
                    similarity_result = self._calculate_question_similarity(
                        q1, q2, task.algorithms, task.algorithm_weights
                    )
                    
                    # 检查是否超过阈值
                    if similarity_result['combined_score'] >= task.similarity_threshold:
                        # 查找或创建重复组
                        group = self._find_or_create_duplicate_group(
                            duplicate_groups, q1, q2, similarity_result
                        )
                        if group not in duplicate_groups:
                            duplicate_groups.append(group)
                    
                    # 更新进度
                    completed_comparisons += 1
                    progress = completed_comparisons / total_comparisons
                    
                    with self._lock:
                        task.progress = progress
                        task.processed_questions = completed_comparisons
            
            # 完成检测
            with self._lock:
                task.status = DetectionStatus.COMPLETED
                task.completed_at = datetime.datetime.utcnow()
                task.duplicates_found = len(duplicate_groups)
                task.result_summary = self._generate_result_summary(duplicate_groups)
                
                # 保存结果
                self.task_results[task_id] = {
                    'duplicate_groups': duplicate_groups,
                    'statistics': task.result_summary
                }
        
        except Exception as e:
            with self._lock:
                task.status = DetectionStatus.FAILED
                task.error_message = str(e)
                task.completed_at = datetime.datetime.utcnow()
    
    def _filter_questions_by_scope(self, questions_data: List[Dict[str, Any]], 
                                  task: DetectionTask) -> List[Dict[str, Any]]:
        """
        根据检测范围过滤题目
        
        Args:
            questions_data: 题目数据列表
            task: 检测任务
            
        Returns:
            List[Dict[str, Any]]: 过滤后的题目列表
        """
        if task.detection_scope == 'all':
            return questions_data
        
        filtered = []
        target_bank_ids = set(task.question_bank_ids)
        
        for question in questions_data:
            question_bank_id = question.get('question_bank_id')
            
            if task.detection_scope == 'within_bank':
                # 只检测指定题库内的题目
                if question_bank_id in target_bank_ids:
                    filtered.append(question)
            elif task.detection_scope == 'cross_bank':
                # 检测跨题库的题目
                filtered.append(question)
        
        return filtered
    
    def _calculate_question_similarity(self, q1: Dict[str, Any], q2: Dict[str, Any],
                                     algorithms: List[str], 
                                     weights: Dict[str, float]) -> Dict[str, Any]:
        """
        计算两个题目的相似度
        
        Args:
            q1: 题目1
            q2: 题目2
            algorithms: 算法列表
            weights: 算法权重
            
        Returns:
            Dict[str, Any]: 相似度结果
        """
        # 提取题目文本
        text1 = self.detector.extract_question_text(q1)
        text2 = self.detector.extract_question_text(q2)
        
        # 计算各种相似度
        scores = {}
        
        if 'edit_distance' in algorithms:
            scores['edit_distance'] = self.calculator.edit_distance_similarity(text1, text2)
        
        if 'cosine_similarity' in algorithms:
            scores['cosine_similarity'] = self.calculator.cosine_similarity(text1, text2)
        
        if 'jaccard_similarity' in algorithms:
            scores['jaccard_similarity'] = self.calculator.jaccard_similarity(text1, text2)
        
        if 'fuzzy_match' in algorithms:
            scores['fuzzy_match'] = self.calculator.fuzzy_match_similarity(text1, text2)
        
        # 计算加权综合分数
        combined_score = 0.0
        total_weight = 0.0
        
        for algorithm, score in scores.items():
            weight = weights.get(algorithm, 0.0)
            combined_score += score * weight
            total_weight += weight
        
        if total_weight > 0:
            combined_score /= total_weight
        
        return {
            'individual_scores': scores,
            'combined_score': combined_score,
            'algorithm_weights': weights,
            'text1_length': len(text1),
            'text2_length': len(text2)
        }
    
    def _find_or_create_duplicate_group(self, existing_groups: List[DuplicateGroup],
                                       q1: Dict[str, Any], q2: Dict[str, Any],
                                       similarity_result: Dict[str, Any]) -> DuplicateGroup:
        """
        查找或创建重复题目组
        
        Args:
            existing_groups: 现有重复组列表
            q1: 题目1
            q2: 题目2
            similarity_result: 相似度结果
            
        Returns:
            DuplicateGroup: 重复题目组
        """
        # 查找是否已存在包含这些题目的组
        for group in existing_groups:
            question_ids = [q['id'] for q in group.questions]
            if q1['id'] in question_ids or q2['id'] in question_ids:
                # 添加新题目到现有组
                if q1['id'] not in question_ids:
                    group.questions.append(q1)
                if q2['id'] not in question_ids:
                    group.questions.append(q2)
                
                # 更新组的相似度信息
                group.similarity_score = max(group.similarity_score, 
                                            similarity_result['combined_score'])
                return group
        
        # 创建新的重复组
        group_type = self._determine_group_type(similarity_result['combined_score'])
        recommended_action = self._recommend_action(q1, q2, similarity_result)
        
        return DuplicateGroup(
            group_id=str(uuid.uuid4()),
            questions=[q1, q2],
            similarity_score=similarity_result['combined_score'],
            similarity_details=similarity_result['individual_scores'],
            detection_algorithms=list(similarity_result['individual_scores'].keys()),
            group_type=group_type,
            recommended_action=recommended_action,
            created_at=datetime.datetime.utcnow()
        )
    
    def _determine_group_type(self, similarity_score: float) -> str:
        """
        确定重复组类型
        
        Args:
            similarity_score: 相似度分数
            
        Returns:
            str: 组类型
        """
        if similarity_score >= 0.95:
            return 'exact'
        elif similarity_score >= 0.85:
            return 'high_similarity'
        else:
            return 'moderate_similarity'
    
    def _recommend_action(self, q1: Dict[str, Any], q2: Dict[str, Any],
                        similarity_result: Dict[str, Any]) -> DuplicateAction:
        """
        推荐处理动作
        
        Args:
            q1: 题目1
            q2: 题目2
            similarity_result: 相似度结果
            
        Returns:
            DuplicateAction: 推荐动作
        """
        score = similarity_result['combined_score']
        
        if score >= 0.95:
            # 几乎完全相同，推荐保留质量更好的
            return DuplicateAction.KEEP_BEST
        elif score >= 0.85:
            # 高度相似，推荐手动选择
            return DuplicateAction.MANUAL_SELECT
        else:
            # 中等相似，推荐标记为重复
            return DuplicateAction.MARK_DUPLICATE
    
    def _generate_result_summary(self, duplicate_groups: List[DuplicateGroup]) -> Dict[str, Any]:
        """
        生成结果摘要
        
        Args:
            duplicate_groups: 重复组列表
            
        Returns:
            Dict[str, Any]: 结果摘要
        """
        if not duplicate_groups:
            return {
                'total_groups': 0,
                'total_duplicate_questions': 0,
                'group_types': {},
                'recommended_actions': {},
                'avg_similarity_score': 0.0
            }
        
        total_questions = sum(len(group.questions) for group in duplicate_groups)
        group_types = {}
        recommended_actions = {}
        total_similarity = 0.0
        
        for group in duplicate_groups:
            # 统计组类型
            group_types[group.group_type] = group_types.get(group.group_type, 0) + 1
            
            # 统计推荐动作
            action = group.recommended_action.value
            recommended_actions[action] = recommended_actions.get(action, 0) + 1
            
            # 累计相似度
            total_similarity += group.similarity_score
        
        avg_similarity = total_similarity / len(duplicate_groups)
        
        return {
            'total_groups': len(duplicate_groups),
            'total_duplicate_questions': total_questions,
            'group_types': group_types,
            'recommended_actions': recommended_actions,
            'avg_similarity_score': avg_similarity,
            'highest_similarity': max(group.similarity_score for group in duplicate_groups),
            'lowest_similarity': min(group.similarity_score for group in duplicate_groups)
        }
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 任务状态信息
        """
        with self._lock:
            if task_id not in self.active_tasks:
                return None
            
            task = self.active_tasks[task_id]
            return {
                'task_id': task.task_id,
                'status': task.status.value,
                'progress': task.progress,
                'total_questions': task.total_questions,
                'processed_questions': task.processed_questions,
                'duplicates_found': task.duplicates_found,
                'created_at': task.created_at.isoformat(),
                'started_at': task.started_at.isoformat() if task.started_at else None,
                'completed_at': task.completed_at.isoformat() if task.completed_at else None,
                'error_message': task.error_message,
                'result_summary': task.result_summary
            }
    
    def get_detection_results(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取检测结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 检测结果
        """
        if task_id not in self.task_results:
            return None
        
        results = self.task_results[task_id]
        
        # 转换重复组为可序列化格式
        serializable_groups = []
        for group in results['duplicate_groups']:
            serializable_groups.append({
                'group_id': group.group_id,
                'questions': group.questions,
                'similarity_score': group.similarity_score,
                'similarity_details': group.similarity_details,
                'detection_algorithms': group.detection_algorithms,
                'group_type': group.group_type,
                'recommended_action': group.recommended_action.value,
                'created_at': group.created_at.isoformat()
            })
        
        return {
            'duplicate_groups': serializable_groups,
            'statistics': results['statistics']
        }
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消检测任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        with self._lock:
            if task_id not in self.active_tasks:
                return False
            
            task = self.active_tasks[task_id]
            if task.status in [DetectionStatus.COMPLETED, DetectionStatus.FAILED, DetectionStatus.CANCELLED]:
                return False
            
            task.status = DetectionStatus.CANCELLED
            task.completed_at = datetime.datetime.utcnow()
            return True
    
    def delete_duplicate_questions(self, question_ids: List[str]) -> Dict[str, Any]:
        """
        批量删除重复题目
        
        Args:
            question_ids: 要删除的题目ID列表
            
        Returns:
            Dict[str, Any]: 删除结果
        """
        # 这里应该调用数据库删除操作
        # 为了演示，返回模拟结果
        return {
            'deleted_count': len(question_ids),
            'deleted_question_ids': question_ids,
            'timestamp': datetime.datetime.utcnow().isoformat(),
            'success': True
        }
    
    def get_task_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取任务历史记录
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 任务历史列表
        """
        with self._lock:
            tasks = list(self.active_tasks.values())
        
        # 按创建时间排序
        tasks.sort(key=lambda x: x.created_at, reverse=True)
        
        # 转换为可序列化格式
        history = []
        for task in tasks[:limit]:
            history.append({
                'task_id': task.task_id,
                'question_bank_ids': task.question_bank_ids,
                'similarity_threshold': task.similarity_threshold,
                'detection_scope': task.detection_scope,
                'status': task.status.value,
                'duplicates_found': task.duplicates_found,
                'created_at': task.created_at.isoformat(),
                'completed_at': task.completed_at.isoformat() if task.completed_at else None
            })
        
        return history

# 使用示例
if __name__ == "__main__":
    # 创建检测API实例
    api = DuplicateDetectionAPI()
    
    # 创建检测任务
    task_id = api.create_detection_task(
        question_bank_ids=["bank1", "bank2"],
        similarity_threshold=0.8,
        detection_scope="cross_bank"
    )
    
    print(f"创建检测任务: {task_id}")
    
    # 模拟题目数据
    questions = [
        {'id': '1', 'question_bank_id': 'bank1', 'question_text': '什么是Python？'},
        {'id': '2', 'question_bank_id': 'bank2', 'question_text': 'Python是什么？'},
        {'id': '3', 'question_bank_id': 'bank1', 'question_text': '如何学习编程？'}
    ]
    
    # 启动检测
    api.start_detection_task(task_id, questions)
    
    # 等待完成
    time.sleep(2)
    
    # 获取结果
    status = api.get_task_status(task_id)
    print(f"任务状态: {status}")
    
    if status and status['status'] == 'completed':
        results = api.get_detection_results(task_id)
        print(f"检测结果: {results}")