from sqlalchemy import create_engine, Column, String, Text, CHAR, DateTime, Enum as <PERSON><PERSON><PERSON>, <PERSON>teger, <PERSON><PERSON><PERSON>, Float, <PERSON><PERSON><PERSON>, JSO<PERSON>
from sqlalchemy.orm import declarative_base, relationship
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.dialects.mysql import INTEGER # For MySQL specific integer types if needed
import datetime
import uuid
import enum

Base = declarative_base()

# Define ENUMs for codes if you prefer them over CHARs for better readability
# class QuestionType(SAEnum):
#     SINGLE_CHOICE = 'B'
#     MULTIPLE_CHOICE = 'G'
#     TRUE_FALSE = 'C'
#     FILL_IN_BLANK = 'T'
#     SHORT_ANSWER = 'D'
#     CALCULATION = 'U'
#     ESSAY = 'W'
#     CASE_ANALYSIS = 'E'
#     COMPOSITE = 'F'

# class DifficultyLevel(SAEnum):
#     VERY_EASY = '1'
#     EASY = '2'
#     MEDIUM = '3'
#     HARD = '4'
#     VERY_HARD = '5'

class Question(Base):
    __tablename__ = 'questions'

    id = Column(String(255), primary_key=True, comment="试题ID：题目唯一标识，格式为 'BWGL-3-LL-B-A-B-C-001-002'，共9段，以-分隔。第1-3段为题库名称代码，第4段为题型代码缩写，第5-7段为三级代码，第8段为认定点代码，第9段为顺序号")
    seq_num = Column(String(50), comment="对应Excel中的序号")
    assessment_code = Column(String(100), comment="认定点代码/考核点代码")
    
    question_type_code = Column(String(20), nullable=False, comment="题型代码: B（单选题）, G（多选题）, C（判断题）, T（填空题）, D（简答题）, U（计算题）, W（论述题）, E（案例分析题）, F（综合题）")
    
    question_number = Column(String(50), comment="题号")
    question_stem = Column(Text, nullable=False, comment="试题题干")
    option_a = Column(Text, comment="试题选项A")
    option_b = Column(Text, comment="试题选项B")
    option_c = Column(Text, comment="试题选项C")
    option_d = Column(Text, comment="试题选项D")
    option_e = Column(Text, comment="试题选项E")
    image_location = Column(String(255), comment="【图】及位置，可存储图片路径或描述")
    correct_answer = Column(Text, nullable=False, comment="正确答案")
    
    difficulty_code = Column(String(20), nullable=False, comment="难度代码: 1（很简单）, 2（简单）, 3（中等）, 4（困难）, 5（很难）")
    
    consistency_code = Column(String(20), comment="一致性代码: 1（很低）, 2（低）, 3（中等）, 4（高）, 5（很高）")
    analysis = Column(Text, comment="解析")
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    # 新增外键，关联到题库表
    question_bank_id = Column(String(36), ForeignKey('question_banks.id'), nullable=False, index=True)
    
    # 新增字段：题目状态管理
    status = Column(String(20), default='active', comment="题目状态: active（活跃）, backup（备用）, archived（归档）")
    is_backup = Column(Boolean, default=False, comment="是否为备用题目")
    backup_priority = Column(Integer, default=0, comment="备用优先级，数值越高优先级越高")
    last_used_date = Column(DateTime, comment="最后使用日期")
    usage_count = Column(Integer, default=0, comment="使用次数")
    
    # 建立关系
    question_bank = relationship("QuestionBank", back_populates="questions")
    paper_questions = relationship("PaperQuestion", back_populates="question", cascade="all, delete-orphan")
    duplicate_records = relationship("DuplicateRecord", foreign_keys="[DuplicateRecord.question1_id]", back_populates="question1")
    duplicate_records_as_q2 = relationship("DuplicateRecord", foreign_keys="[DuplicateRecord.question2_id]", back_populates="question2")
    edit_histories = relationship("EditHistory", back_populates="question", cascade="all, delete-orphan")
    usage_records = relationship("QuestionUsageRecord", back_populates="question", cascade="all, delete-orphan")
    usage_statistics = relationship("UsageStatistics", back_populates="question", uselist=False, cascade="all, delete-orphan")

    @classmethod
    def validate_id_format(cls, id_str):
        """验证ID字符串格式是否符合规范"""
        if not id_str or not isinstance(id_str, str):
            return False
        parts = id_str.split('-')
        return len(parts) == 9 and all(part for part in parts)

    def is_id_valid(self):
        """实例方法验证当前ID是否有效"""
        return self.validate_id_format(str(self.id))

    def __repr__(self):
        stem = getattr(self, 'question_stem', '')
        stem_preview = stem[:30] if stem else ''
        return f"<Question(id='{self.id}', question_stem_preview='{stem_preview}')>"

class Paper(Base):
    """试卷模型"""
    __tablename__ = 'papers'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="试卷ID")
    name = Column(String(255), nullable=False, comment="试卷名称")
    description = Column(Text, comment="试卷描述")
    total_score = Column(Float, default=100.0, comment="试卷总分")
    duration = Column(Integer, default=120, comment="考试时长（分钟）")
    difficulty_level = Column(String(50), comment="试卷难度等级")
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    # 关联试卷题目
    paper_questions = relationship("PaperQuestion", back_populates="paper", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Paper(id='{self.id}', name='{self.name}')>"

class PaperQuestion(Base):
    """试卷题目关联模型"""
    __tablename__ = 'paper_questions'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="关联ID")
    paper_id = Column(String(36), ForeignKey('papers.id'), nullable=False, comment="试卷ID")
    question_id = Column(String(255), ForeignKey('questions.id'), nullable=False, comment="题目ID")
    question_order = Column(Integer, nullable=False, comment="题目在试卷中的顺序")
    score = Column(Float, nullable=False, comment="题目分值")
    section_name = Column(String(100), comment="题目所属章节/部分")
    
    # 关联关系
    paper = relationship("Paper", back_populates="paper_questions")
    question = relationship("Question", back_populates="paper_questions")
    
    def __repr__(self):
        return f"<PaperQuestion(paper_id='{self.paper_id}', question_id='{self.question_id}', order={self.question_order})>"

class QuestionGroup(Base):
    """题目分组模型（用于组题规则）"""
    __tablename__ = 'question_groups'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="分组ID")
    name = Column(String(255), nullable=False, comment="分组名称")
    description = Column(Text, comment="分组描述")
    question_type_code = Column(String(20), comment="题型代码: B（单选题）, G（多选题）, C（判断题）, T（填空题）, D（简答题）, U（计算题）, W（论述题）, E（案例分析题）, F（综合题）")
    difficulty_code = Column(String(20), comment="难度代码: 1（很简单）, 2（简单）, 3（中等）, 4（困难）, 5（很难）")
    min_count = Column(Integer, default=1, comment="最少题目数量")
    max_count = Column(Integer, default=10, comment="最多题目数量")
    score_per_question = Column(Float, default=5.0, comment="每题分值")
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    
    def __repr__(self):
        return f"<QuestionGroup(id='{self.id}', name='{self.name}')>"

class QuestionBank(Base):
    """题库模型"""
    __tablename__ = 'question_banks'
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="题库ID")
    题库名称 = Column(String(255), unique=True, nullable=False, comment="题库名称")
    created_at = Column(DateTime, default=datetime.datetime.utcnow, comment="创建时间")

    # 新增反向关系
    questions = relationship("Question", back_populates="question_bank", cascade="all, delete-orphan")

    @hybrid_property
    def name(self):
        """兼容旧代码的题库名称属性"""
        return self.题库名称

    @name.setter
    def name(self, value):
        """设置题库名称"""
        self.题库名称 = value

    @name.expression
    def name(cls):
        """支持SQL查询的表达式"""
        return cls.题库名称

    def __repr__(self):
        return f"<QuestionBank(id='{self.id}', 题库名称='{self.题库名称}')>"

class DuplicateRecord(Base):
    """重复题目检测记录表"""
    __tablename__ = 'duplicate_records'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="记录ID")
    question1_id = Column(String(255), ForeignKey('questions.id'), nullable=False, comment="题目1 ID")
    question2_id = Column(String(255), ForeignKey('questions.id'), nullable=False, comment="题目2 ID")
    similarity_score = Column(Float, nullable=False, comment="相似度分数 (0-1)")
    similarity_percentage = Column(Float, nullable=False, comment="相似度百分比 (0-100)")
    detection_method = Column(String(50), nullable=False, comment="检测方法: edit_distance, cosine, jaccard, fuzzy, combined")
    algorithm_details = Column(JSON, comment="算法详细信息和参数")
    status = Column(String(20), default='pending', comment="处理状态: pending（待处理）, resolved（已处理）, ignored（已忽略）")
    action_taken = Column(String(50), comment="采取的操作: none, delete_duplicate, merge, edit")
    created_at = Column(DateTime, default=datetime.datetime.utcnow, comment="检测时间")
    resolved_at = Column(DateTime, comment="处理时间")
    resolved_by = Column(String(100), comment="处理人")
    notes = Column(Text, comment="备注")
    
    # 建立关系
    question1 = relationship("Question", foreign_keys=[question1_id], back_populates="duplicate_records")
    question2 = relationship("Question", foreign_keys=[question2_id], back_populates="duplicate_records_as_q2")
    
    def __repr__(self):
        return f"<DuplicateRecord(id='{self.id}', similarity={self.similarity_percentage}%)>"

class BackupRule(Base):
    """备用题目轮换规则表"""
    __tablename__ = 'backup_rules'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="规则ID")
    question_bank_id = Column(String(36), ForeignKey('question_banks.id'), nullable=False, comment="题库ID")
    rule_name = Column(String(255), nullable=False, comment="规则名称")
    standard_count = Column(Integer, nullable=False, comment="标准题目数量")
    total_count = Column(Integer, nullable=False, comment="总题目数量")
    rotation_percentage = Column(Float, nullable=False, comment="轮换比例 (0-100)")
    rotation_strategy = Column(String(50), nullable=False, comment="轮换策略: random, difficulty_based, time_based, intelligent")
    rotation_frequency = Column(String(50), comment="轮换频率: daily, weekly, monthly, yearly")
    is_active = Column(Boolean, default=True, comment="规则是否激活")
    last_rotation_date = Column(DateTime, comment="最后轮换日期")
    next_rotation_date = Column(DateTime, comment="下次轮换日期")
    strategy_config = Column(JSON, comment="策略配置参数")
    created_at = Column(DateTime, default=datetime.datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow, comment="更新时间")
    
    # 建立关系
    question_bank = relationship("QuestionBank")
    rotation_histories = relationship("RotationHistory", back_populates="backup_rule", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<BackupRule(id='{self.id}', name='{self.rule_name}')>"

class RotationHistory(Base):
    """题目轮换历史记录表"""
    __tablename__ = 'rotation_history'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="历史记录ID")
    backup_rule_id = Column(String(36), ForeignKey('backup_rules.id'), nullable=False, comment="轮换规则ID")
    rotation_date = Column(DateTime, nullable=False, comment="轮换执行日期")
    questions_rotated_in = Column(JSON, comment="轮换进入的题目ID列表")
    questions_rotated_out = Column(JSON, comment="轮换出去的题目ID列表")
    rotation_count = Column(Integer, nullable=False, comment="轮换题目数量")
    strategy_used = Column(String(50), nullable=False, comment="使用的轮换策略")
    execution_status = Column(String(20), default='completed', comment="执行状态: pending, running, completed, failed")
    execution_details = Column(JSON, comment="执行详细信息")
    error_message = Column(Text, comment="错误信息（如果有）")
    executed_by = Column(String(100), comment="执行人")
    
    # 建立关系
    backup_rule = relationship("BackupRule", back_populates="rotation_histories")
    
    def __repr__(self):
        return f"<RotationHistory(id='{self.id}', date='{self.rotation_date}')>"

class EditHistory(Base):
    """题目编辑历史记录表"""
    __tablename__ = 'edit_history'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="编辑记录ID")
    question_id = Column(String(255), ForeignKey('questions.id'), nullable=False, comment="题目ID")
    edit_type = Column(String(50), nullable=False, comment="编辑类型: option_reorder, answer_update, content_edit, batch_edit")
    operation_description = Column(Text, nullable=False, comment="操作描述")
    before_data = Column(JSON, comment="编辑前的数据")
    after_data = Column(JSON, comment="编辑后的数据")
    changes_summary = Column(JSON, comment="变更摘要")
    edit_batch_id = Column(String(36), comment="批量编辑批次ID")
    is_batch_operation = Column(Boolean, default=False, comment="是否为批量操作")
    can_undo = Column(Boolean, default=True, comment="是否可以撤销")
    undo_data = Column(JSON, comment="撤销所需的数据")
    created_at = Column(DateTime, default=datetime.datetime.utcnow, comment="编辑时间")
    edited_by = Column(String(100), comment="编辑人")
    
    # 建立关系
    question = relationship("Question", back_populates="edit_histories")
    
    def __repr__(self):
        return f"<EditHistory(id='{self.id}', type='{self.edit_type}')>"

class SimilarityCache(Base):
    """相似度计算缓存表"""
    __tablename__ = 'similarity_cache'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="缓存ID")
    question1_id = Column(String(255), nullable=False, comment="题目1 ID")
    question2_id = Column(String(255), nullable=False, comment="题目2 ID")
    cache_key = Column(String(255), nullable=False, unique=True, comment="缓存键值")
    similarity_scores = Column(JSON, nullable=False, comment="各种算法的相似度分数")
    combined_score = Column(Float, nullable=False, comment="综合相似度分数")
    algorithm_version = Column(String(50), nullable=False, comment="算法版本")
    created_at = Column(DateTime, default=datetime.datetime.utcnow, comment="缓存创建时间")
    expires_at = Column(DateTime, comment="缓存过期时间")
    
    def __repr__(self):
        return f"<SimilarityCache(id='{self.id}', score={self.combined_score})>"

# 示例：如何连接到MySQL数据库并创建表
# Replace with your actual database connection string
# DB_URL = "mysql+mysqlconnector://user:password@host/dbname"
# engine = create_engine(DB_URL, echo=True)

# To create tables in the database (run this once):
# Base.metadata.create_all(engine)

class QuestionUsageRecord(Base):
    """题目使用记录表 - 记录每次题目被采用到试卷中的详细信息"""
    __tablename__ = 'question_usage_records'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="使用记录ID")
    question_id = Column(String(255), ForeignKey('questions.id'), nullable=False, comment="题目ID")
    paper_id = Column(String(36), ForeignKey('papers.id'), nullable=False, comment="试卷ID")
    question_bank_id = Column(String(36), ForeignKey('question_banks.id'), nullable=False, comment="题库ID")
    usage_date = Column(DateTime, default=datetime.datetime.utcnow, comment="使用日期")
    usage_type = Column(String(50), default='paper_generation', comment="使用类型: paper_generation（组卷）, practice（练习）, exam（考试）")
    question_order = Column(Integer, comment="题目在试卷中的顺序")
    question_score = Column(Float, comment="题目分值")
    section_name = Column(String(100), comment="题目所属章节")
    usage_context = Column(JSON, comment="使用上下文信息")
    created_at = Column(DateTime, default=datetime.datetime.utcnow, comment="记录创建时间")
    
    # 建立关系
    question = relationship("Question", back_populates="usage_records")
    paper = relationship("Paper")
    question_bank = relationship("QuestionBank")
    
    def __repr__(self):
        return f"<QuestionUsageRecord(id='{self.id}', question_id='{self.question_id}', paper_id='{self.paper_id}')>"

class UsageStatistics(Base):
    """题目使用统计表 - 存储预计算的统计数据以提高查询性能"""
    __tablename__ = 'usage_statistics'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="统计记录ID")
    question_id = Column(String(255), ForeignKey('questions.id'), nullable=False, unique=True, comment="题目ID")
    question_bank_id = Column(String(36), ForeignKey('question_banks.id'), nullable=False, comment="题库ID")
    total_usage_count = Column(Integer, default=0, comment="总使用次数")
    usage_rate = Column(Float, default=0.0, comment="使用率 (0-100)")
    last_used_date = Column(DateTime, comment="最后使用日期")
    first_used_date = Column(DateTime, comment="首次使用日期")
    avg_usage_per_month = Column(Float, default=0.0, comment="月平均使用次数")
    usage_trend = Column(String(20), default='stable', comment="使用趋势: increasing（上升）, decreasing（下降）, stable（稳定）")
    paper_count = Column(Integer, default=0, comment="出现在试卷数量")
    unique_papers = Column(JSON, comment="出现的试卷ID列表")
    usage_by_month = Column(JSON, comment="按月份统计的使用次数")
    usage_by_type = Column(JSON, comment="按使用类型统计的次数")
    last_calculated = Column(DateTime, default=datetime.datetime.utcnow, comment="最后计算时间")
    calculation_version = Column(String(20), default='1.0', comment="计算版本")
    
    # 建立关系
    question = relationship("Question", back_populates="usage_statistics")
    question_bank = relationship("QuestionBank")
    
    def __repr__(self):
        return f"<UsageStatistics(id='{self.id}', question_id='{self.question_id}', usage_count={self.total_usage_count})>"

class UsageReport(Base):
    """使用统计报告表 - 存储生成的统计报告"""
    __tablename__ = 'usage_reports'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="报告ID")
    report_name = Column(String(255), nullable=False, comment="报告名称")
    report_type = Column(String(50), nullable=False, comment="报告类型: overview（概览）, detailed（详细）, trend（趋势）, comparison（对比）")
    question_bank_id = Column(String(36), ForeignKey('question_banks.id'), comment="题库ID（可选，全局报告时为空）")
    date_range_start = Column(DateTime, comment="统计开始日期")
    date_range_end = Column(DateTime, comment="统计结束日期")
    report_data = Column(JSON, nullable=False, comment="报告数据")
    summary_stats = Column(JSON, comment="汇总统计信息")
    chart_data = Column(JSON, comment="图表数据")
    filters_applied = Column(JSON, comment="应用的过滤条件")
    generated_at = Column(DateTime, default=datetime.datetime.utcnow, comment="生成时间")
    generated_by = Column(String(100), comment="生成人")
    is_scheduled = Column(Boolean, default=False, comment="是否为定时生成")
    schedule_config = Column(JSON, comment="定时配置")
    
    # 建立关系
    question_bank = relationship("QuestionBank")
    
    def __repr__(self):
        return f"<UsageReport(id='{self.id}', name='{self.report_name}', type='{self.report_type}')>"

# 数据库索引定义 - 优化查询性能
from sqlalchemy import Index

# 为QuestionUsageRecord表创建索引
Index('idx_usage_question_id', QuestionUsageRecord.question_id)
Index('idx_usage_paper_id', QuestionUsageRecord.paper_id)
Index('idx_usage_question_bank_id', QuestionUsageRecord.question_bank_id)
Index('idx_usage_date', QuestionUsageRecord.usage_date)
Index('idx_usage_type', QuestionUsageRecord.usage_type)
Index('idx_usage_composite', QuestionUsageRecord.question_id, QuestionUsageRecord.usage_date)

# 为UsageStatistics表创建索引
Index('idx_stats_question_id', UsageStatistics.question_id)
Index('idx_stats_question_bank_id', UsageStatistics.question_bank_id)
Index('idx_stats_usage_count', UsageStatistics.total_usage_count)
Index('idx_stats_usage_rate', UsageStatistics.usage_rate)
Index('idx_stats_last_used', UsageStatistics.last_used_date)
Index('idx_stats_trend', UsageStatistics.usage_trend)

# 为UsageReport表创建索引
Index('idx_report_type', UsageReport.report_type)
Index('idx_report_question_bank_id', UsageReport.question_bank_id)
Index('idx_report_generated_at', UsageReport.generated_at)
Index('idx_report_date_range', UsageReport.date_range_start, UsageReport.date_range_end)

# --- You would also define other tables like Papers, PaperQuestions, Users here ---