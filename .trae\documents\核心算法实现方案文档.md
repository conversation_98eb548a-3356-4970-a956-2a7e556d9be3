# 题库管理系统核心算法实现方案

## 1. 文本相似度算法实现

### 1.1 算法选择与组合策略

本系统采用多种文本相似度算法的组合方式，以提高重复检测的准确性：

#### 主要算法类型：
1. **编辑距离算法（Levenshtein Distance）**：适用于检测字符级别的相似性
2. **余弦相似度（Cosine Similarity）**：基于TF-IDF向量化，适用于语义相似性检测
3. **Jaccard相似度**：基于词汇集合的交集与并集比例
4. **模糊匹配（FuzzyWuzzy）**：结合多种字符串匹配技术

### 1.2 算法实现代码框架

```python
import difflib
import jieba
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from fuzzywuzzy import fuzz
import re

class QuestionSimilarityDetector:
    """
    题目相似度检测器
    
    支持多种算法的组合使用，提供灵活的相似度检测功能
    """
    
    def __init__(self, algorithm_weights=None):
        """
        初始化相似度检测器
        
        Args:
            algorithm_weights (dict): 各算法权重配置
                默认: {'edit_distance': 0.3, 'cosine': 0.4, 'jaccard': 0.2, 'fuzzy': 0.1}
        """
        self.algorithm_weights = algorithm_weights or {
            'edit_distance': 0.3,
            'cosine': 0.4, 
            'jaccard': 0.2,
            'fuzzy': 0.1
        }
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words=self._get_chinese_stopwords()
        )
    
    def _get_chinese_stopwords(self):
        """获取中文停用词列表"""
        return ['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个']
    
    def _preprocess_text(self, text):
        """
        文本预处理
        
        Args:
            text (str): 原始文本
            
        Returns:
            str: 预处理后的文本
        """
        if not text:
            return ""
        
        # 移除HTML标签和特殊字符
        text = re.sub(r'<[^>]+>', '', text)
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', '', text)
        
        # 转换为小写并去除多余空格
        text = text.lower().strip()
        
        return text
    
    def edit_distance_similarity(self, text1, text2):
        """
        计算编辑距离相似度
        
        Args:
            text1 (str): 文本1
            text2 (str): 文本2
            
        Returns:
            float: 相似度分数 (0-1)
        """
        text1 = self._preprocess_text(text1)
        text2 = self._preprocess_text(text2)
        
        if not text1 or not text2:
            return 0.0
        
        # 使用difflib计算序列匹配度
        similarity = difflib.SequenceMatcher(None, text1, text2).ratio()
        return similarity
    
    def cosine_similarity_score(self, text1, text2):
        """
        计算余弦相似度
        
        Args:
            text1 (str): 文本1
            text2 (str): 文本2
            
        Returns:
            float: 相似度分数 (0-1)
        """
        text1 = self._preprocess_text(text1)
        text2 = self._preprocess_text(text2)
        
        if not text1 or not text2:
            return 0.0
        
        # 中文分词
        words1 = ' '.join(jieba.cut(text1))
        words2 = ' '.join(jieba.cut(text2))
        
        try:
            # TF-IDF向量化
            tfidf_matrix = self.tfidf_vectorizer.fit_transform([words1, words2])
            
            # 计算余弦相似度
            similarity_matrix = cosine_similarity(tfidf_matrix)
            return similarity_matrix[0][1]
        except:
            return 0.0
    
    def jaccard_similarity(self, text1, text2):
        """
        计算Jaccard相似度
        
        Args:
            text1 (str): 文本1
            text2 (str): 文本2
            
        Returns:
            float: 相似度分数 (0-1)
        """
        text1 = self._preprocess_text(text1)
        text2 = self._preprocess_text(text2)
        
        if not text1 or not text2:
            return 0.0
        
        # 分词并转换为集合
        words1 = set(jieba.cut(text1))
        words2 = set(jieba.cut(text2))
        
        # 计算交集和并集
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        if len(union) == 0:
            return 0.0
        
        return len(intersection) / len(union)
    
    def fuzzy_similarity(self, text1, text2):
        """
        计算模糊匹配相似度
        
        Args:
            text1 (str): 文本1
            text2 (str): 文本2
            
        Returns:
            float: 相似度分数 (0-1)
        """
        text1 = self._preprocess_text(text1)
        text2 = self._preprocess_text(text2)
        
        if not text1 or not text2:
            return 0.0
        
        # 使用FuzzyWuzzy的ratio方法
        similarity = fuzz.ratio(text1, text2) / 100.0
        return similarity
    
    def calculate_similarity(self, question1, question2):
        """
        计算两个题目的综合相似度
        
        Args:
            question1 (dict): 题目1信息
            question2 (dict): 题目2信息
            
        Returns:
            dict: 包含各算法分数和综合分数的结果
        """
        # 提取题干文本
        stem1 = question1.get('question_stem', '')
        stem2 = question2.get('question_stem', '')
        
        # 计算各算法分数
        scores = {
            'edit_distance': self.edit_distance_similarity(stem1, stem2),
            'cosine': self.cosine_similarity_score(stem1, stem2),
            'jaccard': self.jaccard_similarity(stem1, stem2),
            'fuzzy': self.fuzzy_similarity(stem1, stem2)
        }
        
        # 计算加权综合分数
        weighted_score = sum(
            scores[algo] * self.algorithm_weights[algo] 
            for algo in scores
        )
        
        return {
            'individual_scores': scores,
            'weighted_score': weighted_score,
            'question1_id': question1.get('id'),
            'question2_id': question2.get('id')
        }
```

## 2. 备用题目轮换算法

### 2.1 轮换策略设计

#### 轮换策略类型：
1. **随机轮换**：随机选择备用题目进行轮换
2. **基于难度的轮换**：优先轮换特定难度的题目
3. **基于时间的轮换**：优先轮换较旧的题目
4. **智能轮换**：综合考虑多个因素的智能选择

### 2.2 轮换算法实现

```python
import random
from datetime import datetime, timedelta
from typing import List, Dict, Any
from collections import Counter

class QuestionRotationManager:
    """
    题目轮换管理器
    
    负责管理题库中活跃题目和备用题目之间的轮换
    """
    
    def __init__(self, db_session):
        """
        初始化轮换管理器
        
        Args:
            db_session: 数据库会话对象
        """
        self.db_session = db_session
    
    def get_rotation_candidates(self, bank_id: str, strategy: str, count: int) -> List[Dict]:
        """
        获取轮换候选题目
        
        Args:
            bank_id (str): 题库ID
            strategy (str): 轮换策略
            count (int): 需要轮换的题目数量
            
        Returns:
            List[Dict]: 候选题目列表
        """
        if strategy == 'random':
            return self._random_selection(bank_id, count)
        elif strategy == 'difficulty_based':
            return self._difficulty_based_selection(bank_id, count)
        elif strategy == 'time_based':
            return self._time_based_selection(bank_id, count)
        elif strategy == 'intelligent':
            return self._intelligent_selection(bank_id, count)
        else:
            raise ValueError(f"Unknown rotation strategy: {strategy}")
    
    def _random_selection(self, bank_id: str, count: int) -> List[Dict]:
        """
        随机选择轮换题目
        
        Args:
            bank_id (str): 题库ID
            count (int): 选择数量
            
        Returns:
            List[Dict]: 选中的题目列表
        """
        # 获取所有备用题目
        backup_questions = self.db_session.query(Question).filter(
            Question.question_bank_id == bank_id,
            Question.status == 'backup'
        ).all()
        
        # 随机选择
        selected = random.sample(
            backup_questions, 
            min(count, len(backup_questions))
        )
        
        return [self._question_to_dict(q) for q in selected]
    
    def _difficulty_based_selection(self, bank_id: str, count: int) -> List[Dict]:
        """
        基于难度的选择策略
        
        优先选择难度分布不均匀的题目进行轮换
        
        Args:
            bank_id (str): 题库ID
            count (int): 选择数量
            
        Returns:
            List[Dict]: 选中的题目列表
        """
        # 分析当前活跃题目的难度分布
        active_questions = self.db_session.query(Question).filter(
            Question.question_bank_id == bank_id,
            Question.status == 'active'
        ).all()
        
        difficulty_distribution = Counter(
            q.difficulty_code for q in active_questions
        )
        
        # 找出数量最少的难度等级
        min_difficulty = min(difficulty_distribution, key=difficulty_distribution.get)
        
        # 优先选择该难度的备用题目
        backup_questions = self.db_session.query(Question).filter(
            Question.question_bank_id == bank_id,
            Question.status == 'backup',
            Question.difficulty_code == min_difficulty
        ).limit(count).all()
        
        return [self._question_to_dict(q) for q in backup_questions]
    
    def _time_based_selection(self, bank_id: str, count: int) -> List[Dict]:
        """
        基于时间的选择策略
        
        优先选择最久未使用的备用题目
        
        Args:
            bank_id (str): 题库ID
            count (int): 选择数量
            
        Returns:
            List[Dict]: 选中的题目列表
        """
        backup_questions = self.db_session.query(Question).filter(
            Question.question_bank_id == bank_id,
            Question.status == 'backup'
        ).order_by(
            Question.last_used.asc().nullsfirst()
        ).limit(count).all()
        
        return [self._question_to_dict(q) for q in backup_questions]
    
    def _intelligent_selection(self, bank_id: str, count: int) -> List[Dict]:
        """
        智能选择策略
        
        综合考虑难度分布、使用频率、题目质量等因素
        
        Args:
            bank_id (str): 题库ID
            count (int): 选择数量
            
        Returns:
            List[Dict]: 选中的题目列表
        """
        backup_questions = self.db_session.query(Question).filter(
            Question.question_bank_id == bank_id,
            Question.status == 'backup'
        ).all()
        
        # 计算每个题目的优先级分数
        scored_questions = []
        for question in backup_questions:
            score = self._calculate_priority_score(question, bank_id)
            scored_questions.append((question, score))
        
        # 按分数排序并选择前N个
        scored_questions.sort(key=lambda x: x[1], reverse=True)
        selected = scored_questions[:count]
        
        return [self._question_to_dict(q[0]) for q in selected]
    
    def _calculate_priority_score(self, question: Question, bank_id: str) -> float:
        """
        计算题目的优先级分数
        
        Args:
            question (Question): 题目对象
            bank_id (str): 题库ID
            
        Returns:
            float: 优先级分数
        """
        score = 0.0
        
        # 基于难度分布的分数（难度稀缺的题目分数更高）
        difficulty_score = self._get_difficulty_scarcity_score(question.difficulty_code, bank_id)
        score += difficulty_score * 0.4
        
        # 基于使用频率的分数（使用频率低的分数更高）
        usage_score = 1.0 / (question.usage_count + 1)
        score += usage_score * 0.3
        
        # 基于时间的分数（越久未使用分数越高）
        time_score = self._get_time_score(question.last_used)
        score += time_score * 0.3
        
        return score
    
    def execute_rotation(self, bank_id: str, rotation_in: List[str], rotation_out: List[str]) -> Dict:
        """
        执行题目轮换
        
        Args:
            bank_id (str): 题库ID
            rotation_in (List[str]): 轮换进入活跃状态的题目ID列表
            rotation_out (List[str]): 轮换出活跃状态的题目ID列表
            
        Returns:
            Dict: 轮换结果
        """
        try:
            # 更新轮换进入的题目状态
            self.db_session.query(Question).filter(
                Question.id.in_(rotation_in)
            ).update({
                'status': 'active',
                'last_used': datetime.utcnow()
            }, synchronize_session=False)
            
            # 更新轮换出去的题目状态
            self.db_session.query(Question).filter(
                Question.id.in_(rotation_out)
            ).update({
                'status': 'backup'
            }, synchronize_session=False)
            
            # 记录轮换历史
            rotation_record = RotationHistory(
                bank_id=bank_id,
                rotated_in_questions=','.join(rotation_in),
                rotated_out_questions=','.join(rotation_out),
                strategy_used='manual',
                rotation_count=len(rotation_in),
                rotation_date=datetime.utcnow()
            )
            
            self.db_session.add(rotation_record)
            self.db_session.commit()
            
            return {
                'success': True,
                'rotated_in_count': len(rotation_in),
                'rotated_out_count': len(rotation_out),
                'rotation_id': rotation_record.id
            }
            
        except Exception as e:
            self.db_session.rollback()
            return {
                'success': False,
                'error': str(e)
            }
```

## 3. 批量编辑处理算法

### 3.1 选项重排算法

```python
import json
from typing import Dict, List, Any
from datetime import datetime

class BatchEditProcessor:
    """
    批量编辑处理器
    
    负责处理题目的批量编辑操作，包括选项重排和答案更新
    """
    
    def __init__(self, db_session):
        """
        初始化批量编辑处理器
        
        Args:
            db_session: 数据库会话对象
        """
        self.db_session = db_session
        self.option_fields = ['option_a', 'option_b', 'option_c', 'option_d', 'option_e']
        self.option_letters = ['A', 'B', 'C', 'D', 'E']
    
    def reorder_options(self, question_ids: List[str], option_mapping: Dict[str, str]) -> Dict:
        """
        批量重排题目选项
        
        Args:
            question_ids (List[str]): 题目ID列表
            option_mapping (Dict[str, str]): 选项映射关系 {'A': 'B', 'B': 'A', ...}
            
        Returns:
            Dict: 处理结果
        """
        operation_id = self._generate_operation_id()
        affected_questions = []
        
        try:
            for question_id in question_ids:
                question = self.db_session.query(Question).filter(
                    Question.id == question_id
                ).first()
                
                if not question:
                    continue
                
                # 保存原始数据
                original_data = self._extract_question_data(question)
                
                # 执行选项重排
                new_data = self._apply_option_mapping(original_data, option_mapping)
                
                # 更新题目数据
                self._update_question_data(question, new_data)
                
                # 记录编辑历史
                self._record_edit_history(
                    question_id=question_id,
                    operation_type='option_reorder',
                    operation_id=operation_id,
                    old_data=original_data,
                    new_data=new_data
                )
                
                affected_questions.append({
                    'question_id': question_id,
                    'original': original_data,
                    'modified': new_data
                })
            
            self.db_session.commit()
            
            return {
                'success': True,
                'operation_id': operation_id,
                'affected_count': len(affected_questions),
                'affected_questions': affected_questions
            }
            
        except Exception as e:
            self.db_session.rollback()
            return {
                'success': False,
                'error': str(e),
                'operation_id': operation_id
            }
    
    def _apply_option_mapping(self, original_data: Dict, mapping: Dict[str, str]) -> Dict:
        """
        应用选项映射关系
        
        Args:
            original_data (Dict): 原始题目数据
            mapping (Dict[str, str]): 选项映射关系
            
        Returns:
            Dict: 重排后的题目数据
        """
        new_data = original_data.copy()
        
        # 创建临时存储，避免覆盖问题
        temp_options = {}
        
        # 重排选项内容
        for from_letter, to_letter in mapping.items():
            from_field = f'option_{from_letter.lower()}'
            to_field = f'option_{to_letter.lower()}'
            
            if from_field in original_data:
                temp_options[to_field] = original_data[from_field]
        
        # 应用重排结果
        for field, value in temp_options.items():
            new_data[field] = value
        
        # 更新正确答案
        original_answer = original_data.get('correct_answer', '')
        new_answer = self._update_correct_answer(original_answer, mapping)
        new_data['correct_answer'] = new_answer
        
        return new_data
    
    def _update_correct_answer(self, original_answer: str, mapping: Dict[str, str]) -> str:
        """
        更新正确答案字母
        
        Args:
            original_answer (str): 原始答案
            mapping (Dict[str, str]): 选项映射关系
            
        Returns:
            str: 更新后的答案
        """
        if not original_answer:
            return original_answer
        
        # 处理单选题答案（单个字母）
        if len(original_answer) == 1 and original_answer.upper() in mapping:
            return mapping[original_answer.upper()]
        
        # 处理多选题答案（多个字母）
        new_answer = ''
        for char in original_answer.upper():
            if char in mapping:
                new_answer += mapping[char]
            else:
                new_answer += char
        
        return new_answer
    
    def undo_operation(self, operation_id: str) -> Dict:
        """
        撤销批量操作
        
        Args:
            operation_id (str): 操作ID
            
        Returns:
            Dict: 撤销结果
        """
        try:
            # 获取操作历史记录
            edit_records = self.db_session.query(EditHistory).filter(
                EditHistory.operation_id == operation_id
            ).all()
            
            if not edit_records:
                return {
                    'success': False,
                    'error': 'Operation not found'
                }
            
            # 逐个恢复题目数据
            restored_count = 0
            for record in edit_records:
                if record.question_id:
                    question = self.db_session.query(Question).filter(
                        Question.id == record.question_id
                    ).first()
                    
                    if question:
                        # 恢复原始数据
                        original_data = json.loads(record.old_data)
                        self._update_question_data(question, original_data)
                        restored_count += 1
            
            # 标记操作为已撤销
            self.db_session.query(EditHistory).filter(
                EditHistory.operation_id == operation_id
            ).update({
                'operation_type': 'undone_' + edit_records[0].operation_type
            })
            
            self.db_session.commit()
            
            return {
                'success': True,
                'restored_count': restored_count,
                'operation_id': operation_id
            }
            
        except Exception as e:
            self.db_session.rollback()
            return {
                'success': False,
                'error': str(e)
            }
    
    def preview_changes(self, question_ids: List[str], option_mapping: Dict[str, str]) -> List[Dict]:
        """
        预览批量修改效果
        
        Args:
            question_ids (List[str]): 题目ID列表
            option_mapping (Dict[str, str]): 选项映射关系
            
        Returns:
            List[Dict]: 预览结果列表
        """
        preview_results = []
        
        for question_id in question_ids:
            question = self.db_session.query(Question).filter(
                Question.id == question_id
            ).first()
            
            if question:
                original_data = self._extract_question_data(question)
                modified_data = self._apply_option_mapping(original_data, option_mapping)
                
                preview_results.append({
                    'question_id': question_id,
                    'question_stem': original_data.get('question_stem', ''),
                    'original': original_data,
                    'modified': modified_data,
                    'changes': self._identify_changes(original_data, modified_data)
                })
        
        return preview_results
    
    def _identify_changes(self, original: Dict, modified: Dict) -> List[Dict]:
        """
        识别数据变更
        
        Args:
            original (Dict): 原始数据
            modified (Dict): 修改后数据
            
        Returns:
            List[Dict]: 变更列表
        """
        changes = []
        
        for key in original:
            if key in modified and original[key] != modified[key]:
                changes.append({
                    'field': key,
                    'old_value': original[key],
                    'new_value': modified[key]
                })
        
        return changes
```

## 4. 性能优化策略

### 4.1 异步处理

对于大批量的重复检测和批量编辑操作，采用Celery异步任务队列进行处理：

```python
from celery import Celery

app = Celery('question_bank')

@app.task
def detect_duplicates_async(bank_ids, similarity_threshold, algorithm_type):
    """异步执行重复检测任务"""
    detector = QuestionSimilarityDetector()
    # 执行检测逻辑
    return results

@app.task
def batch_edit_async(question_ids, option_mapping):
    """异步执行批量编辑任务"""
    processor = BatchEditProcessor(db_session)
    # 执行编辑逻辑
    return results
```

### 4.2 缓存策略

使用Redis缓存频繁查询的数据：

```python
import redis
import json

class CacheManager:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
    
    def cache_similarity_result(self, question_pair_key, result):
        """缓存相似度计算结果"""
        self.redis_client.setex(
            f"similarity:{question_pair_key}", 
            3600,  # 1小时过期
            json.dumps(result)
        )
    
    def get_cached_similarity(self, question_pair_key):
        """获取缓存的相似度结果"""
        cached = self.redis_client.get(f"similarity:{question_pair_key}")
        return json.loads(cached) if cached else None
```

### 4.3 数据库优化

1. **索引优化**：为频繁查询的字段创建合适的索引
2. **分页查询**：对大量数据采用分页处理
3. **批量操作**：使用批量插入和更新减少数据库交互次数

这套算法实现方案提供了完整的技术基础，支持高效的重复检测、智能的题目轮换和灵活的批量编辑功能。