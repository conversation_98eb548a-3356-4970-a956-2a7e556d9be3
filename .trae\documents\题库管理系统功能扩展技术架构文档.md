## 1.Architecture design

```mermaid
graph TD
    A[用户浏览器] --> B[React前端应用]
    B --> C[Flask后端API]
    C --> D[SQLAlchemy ORM]
    D --> E[SQLite/PostgreSQL数据库]
    C --> F[文本相似度算法模块]
    C --> G[备用题目管理模块]
    C --> H[批量编辑处理模块]
    C --> I[任务队列系统]
    I --> J[Celery Worker]
    
    subgraph "前端层"
        B
    end
    
    subgraph "后端服务层"
        C
        F
        G
        H
        I
        J
    end
    
    subgraph "数据层"
        D
        E
    end
```

## 2.Technology Description

* Frontend: React\@18 + tailwindcss\@3 + vite + axios

* Backend: Flask\@2.3 + SQLAlchemy\@2.0 + Celery\@5.3

* Database: SQLite (开发) / PostgreSQL (生产)

* Algorithm: scikit-learn + difflib + fuzzywuzzy

* Task Queue: Redis + Celery

* Additional: pandas + numpy (数据处理)

## 3.Route definitions

| Route                | Purpose                |
| -------------------- | ---------------------- |
| /duplicate-detection | 重复题目检测页面，提供检测参数设置和结果展示 |
| /backup-management   | 备用题目管理页面，管理题目池和轮换规则    |
| /batch-edit          | 批量编辑页面，支持题目选项和答案的批量修改  |
| /system-settings     | 系统设置页面，配置算法参数和自动化规则    |
| /duplicate-history   | 重复检测历史记录页面             |
| /rotation-history    | 题目轮换历史记录页面             |

## 4.API definitions

### 4.1 重复题目检测API

**执行重复检测**

```
POST /api/duplicate/detect
```

Request:

| Param Name            | Param Type | isRequired | Description                                 |
| --------------------- | ---------- | ---------- | ------------------------------------------- |
| bank\_ids             | array      | true       | 要检测的题库ID列表                                  |
| similarity\_threshold | float      | true       | 相似度阈值(0.5-1.0)                              |
| algorithm\_type       | string     | true       | 算法类型: 'cosine', 'jaccard', 'edit\_distance' |
| detection\_scope      | string     | true       | 检测范围: 'within\_bank', 'cross\_bank'         |

Response:

| Param Name | Param Type | Description |
| ---------- | ---------- | ----------- |
| task\_id   | string     | 异步任务ID      |
| status     | string     | 任务状态        |

**获取检测结果**

```
GET /api/duplicate/result/{task_id}
```

Response:

| Param Name        | Param Type | Description |
| ----------------- | ---------- | ----------- |
| duplicates        | array      | 重复题目对列表     |
| similarity\_score | float      | 相似度分数       |
| question\_pairs   | object     | 题目对详情       |

**批量处理重复题目**

```
POST /api/duplicate/batch-action
```

Request:

| Param Name         | Param Type | isRequired | Description                             |
| ------------------ | ---------- | ---------- | --------------------------------------- |
| question\_ids      | array      | true       | 题目ID列表                                  |
| action             | string     | true       | 操作类型: 'delete', 'mark\_backup', 'merge' |
| keep\_question\_id | string     | false      | 保留的题目ID（合并时使用）                          |

### 4.2 备用题目管理API

**设置题库容量规则**

```
POST /api/backup/set-capacity
```

Request:

| Param Name           | Param Type | isRequired | Description                            |
| -------------------- | ---------- | ---------- | -------------------------------------- |
| bank\_id             | string     | true       | 题库ID                                   |
| standard\_capacity   | integer    | true       | 标准容量                                   |
| rotation\_percentage | float      | true       | 轮换比例(0.1-1.0)                          |
| rotation\_period     | string     | true       | 轮换周期: 'monthly', 'quarterly', 'yearly' |

**执行题目轮换**

```
POST /api/backup/rotate
```

Request:

| Param Name         | Param Type | isRequired | Description                                        |
| ------------------ | ---------- | ---------- | -------------------------------------------------- |
| bank\_id           | string     | true       | 题库ID                                               |
| rotation\_strategy | string     | true       | 轮换策略: 'random', 'difficulty\_based', 'time\_based' |
| preview\_only      | boolean    | false      | 仅预览不执行                                             |

**获取备用题目统计**

```
GET /api/backup/statistics/{bank_id}
```

Response:

| Param Name       | Param Type | Description |
| ---------------- | ---------- | ----------- |
| total\_questions | integer    | 总题目数        |
| active           |            |             |

