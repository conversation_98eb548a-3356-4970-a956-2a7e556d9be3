# 题目采用率统计功能技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[用户浏览器] --> B[React前端应用]
    B --> C[Flask后端API]
    C --> D[Supabase数据库]
    C --> E[统计计算引擎]
    C --> F[报告生成服务]
    
    subgraph "前端层"
        B
    end
    
    subgraph "后端层"
        C
        E
        F
    end
    
    subgraph "数据层"
        D
    end
```

## 2. 技术描述

* **前端**：React\@18 + Chart.js\@4 + tailwindcss\@3 + vite

* **后端**：Flask\@2.3 + SQLAlchemy\@2.0

* **数据库**：Supabase (PostgreSQL)

* **图表库**：Chart.js + react-chartjs-2

* **导出功能**：openpyxl + reportlab

## 3. 路由定义

| 路由                                   | 用途         |
| ------------------------------------ | ---------- |
| /usage-statistics                    | 题目采用率统计主页面 |
| /usage-statistics/detail/:questionId | 单个题目使用详情页面 |
| /usage-statistics/report             | 统计报告生成页面   |
| /usage-statistics/config             | 统计配置管理页面   |

## 4. API定义

### 4.1 核心API

#### 题目使用记录API

```
POST /api/question-usage/record
```

请求参数：

| 参数名          | 参数类型     | 是否必需  | 描述              |
| ------------ | -------- | ----- | --------------- |
| question\_id | integer  | true  | 题目ID            |
| paper\_id    | integer  | true  | 试卷ID            |
| usage\_type  | string   | true  | 使用类型（组卷、预览、导出等） |
| timestamp    | datetime | false | 使用时间（默认当前时间）    |

响应：

| 参数名       | 参数类型    | 描述     |
| --------- | ------- | ------ |
| success   | boolean | 记录是否成功 |
| usage\_id | integer | 使用记录ID |

#### 获取题目采用率统计API

```
GET /api/question-usage/statistics
```

请求参数：

| 参数名         | 参数类型    | 是否必需  | 描述                              |
| ----------- | ------- | ----- | ------------------------------- |
| bank\_id    | integer | false | 题库ID（不传则统计所有题库）                 |
| start\_date | date    | false | 开始日期                            |
| end\_date   | date    | false | 结束日期                            |
| page        | integer | false | 页码（默认1）                         |
| limit       | integer | false | 每页数量（默认20）                      |
| sort\_by    | string  | false | 排序字段（usage\_count, last\_used等） |
| order       | string  | false | 排序方向（asc, desc）                 |

响应：

```json
{
  "success": true,
  "data": {
    "total_questions": 1000,
    "used_questions": 350,
    "total_usage_count": 1250,
    "average_usage_rate": 35.0,
    "questions": [
      {
        "question_id": 1,
        "question_content": "题目内容...",
        "usage_count": 15,
        "last_used": "2024-01-15T10:30:00Z",
        "usage_rate": 1.2,
        "paper_count": 8
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1000,
      "pages": 50
    }
  }
}
```

#### 获取题目使用详情API

```
GET /api/question-usage/detail/{question_id}
```

响应：

```json
{
  "success": true,
  "data": {
    "question_info": {
      "id": 1,
      "content": "题目内容...",
      "type": "单选题",
      "difficulty": "中等"
    },
    "usage_summary": {
      "total_usage": 15,
      "unique_papers": 8,
      "first_used": "2023-09-01T08:00:00Z",
      "last_used": "2024-01-15T10:30:00Z"
    },
    "usage_history": [
      {
        "paper_id": 101,
        "paper_name": "期末考试试卷A",
        "used_at": "2024-01-15T10:30:00Z",
        "usage_type": "组卷"
      }
    ],
    "usage_trend": [
      {
        "date": "2024-01",
        "count": 5
      }
    ]
  }
}
```

#### 生成统计报告API

```
POST /api/question-usage/report
```

请求参数：

| 参数名          | 参数类型   | 是否必需  | 描述                             |
| ------------ | ------ | ----- | ------------------------------ |
| report\_type | string | true  | 报告类型（summary, detailed, trend） |
| bank\_ids    | array  | false | 题库ID列表                         |
| start\_date  | date   | true  | 开始日期                           |
| end\_date    | date   | true  | 结束日期                           |
| format       | string | false | 导出格式（json, excel, pdf）         |

## 5. 服务器架构图

```mermaid
graph TD
    A[客户端请求] --> B[路由控制器]
    B --> C[业务逻辑层]
    C --> D[数据访问层]
    D --> E[数据库]
    
    C --> F[统计计算服务]
    C --> G[报告生成服务]
    
    subgraph "控制器层"
        B
    end
    
    subgraph "服务层"
        C
        F
        G
    end
    
    subgraph "数据层"
        D
        E
    end
```

## 6. 数据模型

### 6.1 数据模型定义

```mermaid
erDiagram
    QUESTION ||--o{ QUESTION_USAGE : has
    PAPER ||--o{ QUESTION_USAGE : contains
    QUESTION_BANK ||--o{ QUESTION : belongs_to
    QUESTION_USAGE ||--o{ USAGE_STATISTICS : aggregates_to
    
    QUESTION {
        int id PK
        int bank_id FK
        text content
        string type
        string difficulty
        datetime created_at
        datetime updated_at
    }
    
    QUESTION_USAGE {
        int id PK
        int question_id FK
        int paper_id FK
        string usage_type
        datetime used_at
        string user_id
    }
    
    PAPER {
        int id PK
        string name
        int bank_id FK
        datetime created_at
        string status
    }
    
    USAGE_STATISTICS {
        int id PK
        int question_id FK
        int usage_count
        datetime last_used
        datetime first_used
        int unique_papers
        float usage_rate
        datetime calculated_at
    }
    
    QUESTION_BANK {
        int id PK
        string name
        int total_questions
        int used_questions
        float average_usage_rate
        datetime updated_at
    }
```

### 6.2 数据定义语言

#### 题目使用记录表 (question\_usage)

```sql
-- 创建题目使用记录表
CREATE TABLE question_usage (
    id SERIAL PRIMARY KEY,
    question_id INTEGER NOT NULL REFERENCES questions(id) ON DELETE CASCADE,
    paper_id INTEGER NOT NULL REFERENCES papers(id) ON DELETE CASCADE,
    usage_type VARCHAR(50) NOT NULL DEFAULT '组卷',
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_question_usage_question_id ON question_usage(question_id);
CREATE INDEX idx_question_usage_paper_id ON question_usage(paper_id);
CREATE INDEX idx_question_usage_used_at ON question_usage(used_at DESC);
CREATE INDEX idx_question_usage_composite ON question_usage(question_id, used_at DESC);
```

#### 使用统计汇总表 (usage\_statistics)

```sql
-- 创建使用统计汇总表
CREATE TABLE usage_statistics (
    id SERIAL PRIMARY KEY,
    question_id INTEGER NOT NULL REFERENCES questions(id) ON DELETE CASCADE,
    usage_count INTEGER DEFAULT 0,
    unique_papers INTEGER DEFAULT 0,
    first_used TIMESTAMP WITH TIME ZONE,
    last_used TIMESTAMP WITH TIME ZONE,
    usage_rate DECIMAL(5,2) DEFAULT 0.00,
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(question_id)
);

-- 创建索引
CREATE INDEX idx_usage_statistics_usage_count ON usage_statistics(usage_count DESC);
CREATE INDEX idx_usage_statistics_last_used ON usage_statistics(last_used DESC);
CREATE INDEX idx_usage_statistics_usage_rate ON usage_statistics(usage_rate DESC);
```

#### 扩展题库表字段

```sql
-- 扩展题库表，添加统计字段
ALTER TABLE question_banks ADD COLUMN IF NOT EXISTS total_questions INTEGER DEFAULT 0;
ALTER TABLE question_banks ADD COLUMN IF NOT EXISTS used_questions INTEGER DEFAULT 0;
ALTER TABLE question_banks ADD COLUMN IF NOT EXISTS average_usage_rate DECIMAL(5,2) DEFAULT 0.00;
ALTER TABLE question_banks ADD COLUMN IF NOT EXISTS last_statistics_update TIMESTAMP WITH TIME ZONE;

-- 创建统计更新索引
CREATE INDEX idx_question_banks_stats ON question_banks(used_questions, average_usage_rate);
```

#### 初始化数据和触发器

```sql
-- 创建统计更新函数
CREATE OR REPLACE FUNCTION update_usage_statistics()
RETURNS TRIGGER AS $$
BEGIN
    -- 更新题目使用统计
    INSERT INTO usage_statistics (question_id, usage_count, unique_papers, first_used, last_used, usage_rate)
    SELECT 
        NEW.question_id,
        COUNT(*) as usage_count,
        COUNT(DISTINCT paper_id) as unique_papers,
        MIN(used_at) as first_used,
        MAX(used_at) as last_used,
        (COUNT(*) * 100.0 / NULLIF((SELECT COUNT(*) FROM papers WHERE bank_id = (SELECT bank_id FROM questions WHERE id = NEW.question_id)), 0)) as usage_rate
    FROM question_usage 
    WHERE question_id = NEW.question_id
    GROUP BY question_id
    ON CONFLICT (question_id) 
    DO UPDATE SET
        usage_count = EXCLUDED.usage_count,
        unique_papers = EXCLUDED.unique_papers,
        first_used = EXCLUDED.first_used,
        last_used = EXCLUDED.last_used,
        usage_rate = EXCLUDED.usage_rate,
        calculated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER trigger_update_usage_statistics
    AFTER INSERT ON question_usage
    FOR EACH ROW
    EXECUTE FUNCTION update_usage_statistics();
```

## 7. 统计算法实现

### 7.1 核心统计指标

* **使用次数**：题目在所有试卷中的总使用次数

* **采用率**：使用次数 / 总试卷数 × 100%

* **覆盖率**：已使用题目数 / 题库总题目数 × 100%

* **使用频率**：单位时间内的平均使用次数

* **热门度排名**：基于使用次数和时间衰减的综合排名

### 7.2 实时统计更新策略

* **增量更新**：每次题目使用后立即更新相关统计

* **批量重算**：定期（每日凌晨）重新计算所有统计数据

* **缓存机制**：热门统计数据缓存1小时，减少数据库查询

* **异步处理**：复杂统计计算采用后台任务处理

### 7.3 性能优化方案

* **数据库索引**：为查询频繁的字段创建复合索引

* **分页查询**：大数据量列表采用分页加载

* **数据预聚合**：预先计算常用统计指标存储在汇总表

* **查询优化**：使用SQL视图简化复杂查询逻辑

